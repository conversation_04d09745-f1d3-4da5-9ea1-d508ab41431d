import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/providers/potd_photos_provider.dart';

class PotdAssignmentUtil {
  final BuildContext context;
  final WidgetRef ref;
  final PhotoData photo;
  final VoidCallback? startLoading;
  final Function({void Function()? callback})? stopLoading;

  PotdAssignmentUtil({
    required this.context,
    required this.ref,
    required this.photo,
    this.startLoading,
    this.stopLoading,
  });

  Future<void> handleAssignment({required AssignmentActionType action}) async {
    if (!context.mounted) return;
    startLoading?.call();

    final moderationService = ModerationService();

    final BaseResponse response = await moderationService.potdAssignment(
      photoId: photo.id,
      actionType: action,
    );

    if (!context.mounted) return;

    if (!response.success) {
      startLoading?.call();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    stopLoading?.call(
      callback: () {
        if (action == AssignmentActionType.assign) {
          ref
              .read(potdPhotoIdsProvider.notifier)
              .addItem(photo.id, reorder: true);
        } else {
          ref.read(potdPhotoIdsProvider.notifier).removeItem(photo.id);
        }

        ref
            .read(photoStoreProvider.notifier)
            .setPotd(photo.id, action == AssignmentActionType.assign);

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              duration: const Duration(seconds: 2),
              content: Text(response.message),
            ),
          );
        }
      },
    );
  }
}

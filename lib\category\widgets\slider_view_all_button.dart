import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class SliderViewAllButton extends StatelessWidget {
  final double fontSize;
  final double? height;
  final String viewAllText;
  final VoidCallback? onTap;

  const SliderViewAllButton({
    super.key,
    this.fontSize = 13.0,
    this.height,
    this.viewAllText = 'See all',
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        if (onTap == null) return;
        onTap!();
      },
      child: Text(
        viewAllText,
        style: TextStyle(
          height: height,
          fontSize: fontSize,
          color: context.colors.accentColor,
        ),
      ),
    );
  }
}

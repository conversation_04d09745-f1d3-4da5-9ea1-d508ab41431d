import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class LoadingIndicator extends StatelessWidget {
  const LoadingIndicator({
    super.key,
    this.color,
    this.size = 20.0,
    this.strokeWidth = 2.0,
    this.centered = true,
  });

  final Color? color;
  final double size;
  final double strokeWidth;
  final bool centered;

  @override
  Widget build(BuildContext context) {
    return centered
        ? Center(child: _buildSizedWidget(context, color))
        : _buildSizedWidget(context, color);
  }

  Widget _buildSizedWidget(BuildContext context, Color? color) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        color:
            color ??
            (context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor),
        strokeWidth: 2.0,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/notification/widgets/notifications_screen.dart';

class NotifIcon extends ConsumerWidget {
  const NotifIcon({super.key, this.size = 24.0});

  final double size;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // NotificationFetchResult notification = ref.watch(notificationProvider);
    int totalUnread = ref.watch(
      notificationProvider.select((value) => value.totalUnread),
    );

    final int profileId = LocalUserService.userId ?? 0;

    return Stack(
      alignment: Alignment.center,
      children: [
        IconButton(
          onPressed: () => _handleOnPressed(context, profileId),
          icon: Icon(Ionicons.notifications_outline, size: size),
        ),
        if (totalUnread > 0)
          // Create rounded notifications count.
          Positioned(
            right: 8.0,
            top: 12.0,
            child: Container(
              padding: const EdgeInsets.all(1),
              decoration: BoxDecoration(
                color: context.colors.accentColor,
                borderRadius: BorderRadius.all(Radius.circular(16)),
              ),
              constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
              child: Align(
                alignment: Alignment.center,
                child: Text(
                  totalUnread.toString(),
                  style: const TextStyle(color: Colors.white, fontSize: 8.0),
                ),
              ),
            ),
          ),
      ],
    );
  }

  void _handleOnPressed(BuildContext context, int profileId) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => NotificationsScreen(profileId: profileId),
      ),
    );
  }
}

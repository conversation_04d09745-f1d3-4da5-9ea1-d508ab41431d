import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/dto/plain_album_data.dart';

Future<PlainAlbumData> readAlbumData(AssetPathEntity album) async {
  final name = album.name;
  final totalPhotos = await album.assetCountAsync;
  final cover = await album.getAssetListRange(start: 0, end: 1);
  final coverEntity = cover.isEmpty ? null : cover[0];

  return PlainAlbumData(
    name: name,
    totalPhotos: totalPhotos,
    coverPhoto: coverEntity,
  );
}

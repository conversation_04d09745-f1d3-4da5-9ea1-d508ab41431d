import 'dart:io';

import 'package:portraitmode/category/dto/category_data.dart';

class PhotoUploadData {
  String address;
  double? lat;
  double? lng;
  List<CategoryData> categories;
  File? photo;
  String? description;
  bool needsFeedback;

  PhotoUploadData({
    this.address = '',
    this.lat,
    this.lng,
    this.categories = const [],
    this.photo,
    this.description,
    this.needsFeedback = false,
  });

  PhotoUploadData copyWith({
    String? address,
    double? lat,
    double? lng,
    List<CategoryData>? categories,
    File? photo,
    String? description,
    bool? needsFeedback,
  }) {
    return PhotoUploadData(
      address: address ?? this.address,
      lat: lat,
      lng: lng,
      categories: categories ?? this.categories,
      photo: photo,
      description: description,
      needsFeedback: needsFeedback ?? this.needsFeedback,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'address': address,
      'lat': lat,
      'lng': lng,
      'categories': categories.map((x) => x.toMap()).toList(),
      'photo': photo,
      'description': description,
      'needs_feedback': needsFeedback,
    };
  }

  factory PhotoUploadData.fromMap(Map<String, dynamic> data) {
    return PhotoUploadData(
      address: data['address'],
      lat: data['lat'],
      lng: data['lng'],
      categories: List<CategoryData>.from(
        data['categories']?.map((x) => CategoryData.fromMap(x)),
      ),
      photo: data['photo'],
      description: data['description'],
      needsFeedback: data['needs_feedback'] ?? false,
    );
  }
}

PhotoUploadData globalPhotoUploadData = PhotoUploadData();

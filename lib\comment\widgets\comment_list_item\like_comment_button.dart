import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/comment/http_responses/like_comment_response.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/services/like_comment_service.dart';

final class LikeCommentButton extends ConsumerWidget {
  final int commentId;
  final bool isLiked;
  final int totalLikes;

  const LikeCommentButton({
    super.key,
    required this.commentId,
    this.isLiked = false,
    this.totalLikes = 0,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Text(
          (totalLikes > 0 ? totalLikes.toString() : ''),
          style: TextStyle(fontSize: 11.0, color: context.colors.timeColor),
        ),
        const SizedBox(width: 3.0),
        IconButton(
          padding: const EdgeInsets.all(0.0),
          constraints: const BoxConstraints(),
          icon: Icon((isLiked ? Ionicons.heart : Ionicons.heart_outline)),
          iconSize: 18.0,
          color: isLiked == true
              ? context.colors.accentColor
              : context.colors.contentLighterColor,
          onPressed: () {
            doAction(
              context: context,
              ref: ref,
              actionType: isLiked ? 'unlike' : 'like',
            );
          },
        ),
      ],
    );
  }

  void doAction({
    required BuildContext context,
    required WidgetRef ref,
    required String actionType,
  }) async {
    // Switch the like status state even though the request hasn't been made.
    ref
        .read(commentStoreProvider.notifier)
        .setIsLiked(commentId, actionType == 'like');

    ref
        .read(commentStoreProvider.notifier)
        .setTotalLikes(
          commentId,
          actionType == 'like' ? totalLikes + 1 : totalLikes - 1,
        );

    final likeCommentService = LikeCommentService();

    LikeCommentResponse response = actionType == 'like'
        ? await likeCommentService.like(commentId)
        : await likeCommentService.unlike(commentId);

    if (!response.success) {
      // Switch the like status state back to the previous state.
      ref
          .read(commentStoreProvider.notifier)
          .setIsLiked(commentId, actionType != 'like');

      ref
          .read(commentStoreProvider.notifier)
          .setTotalLikes(
            commentId,
            actionType != 'like' ? totalLikes + 1 : totalLikes - 1,
          );

      return;
    }
  }
}

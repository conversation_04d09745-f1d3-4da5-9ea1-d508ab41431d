// load_more_id_list_async_notifier.dart

import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/dto/pagination_state.dart';
import 'package:portraitmode/app/providers/id_list_async_notifier.dart';

/// Abstract base class to support infinite scroll, refresh, and background
/// update behaviors in ID-based lists.
///
/// It extends [IdListAsyncNotifier] by adding:
/// - Pagination support (load more)
/// - Pull-to-refresh with debouncing
/// - Background check for new items
/// - Retry logic with exponential backoff
abstract class LoadMoreIdListAsyncNotifier extends IdListAsyncNotifier {
  // Pagination state management
  PaginationState _paginationState = const PaginationState();

  // Operation flags
  bool _isRefreshing = false;
  bool _isCheckingForNew = false;
  bool _isLoadingMore = false;

  // Debounce timers
  Timer? _refreshTimer;
  Timer? _checkNewTimer;

  @override
  Future<List<int>> build() async {
    // Cancel timers when the notifier is disposed
    ref.onDispose(() {
      _refreshTimer?.cancel();
      _checkNewTimer?.cancel();
    });

    return performInitialLoad();
  }

  /// Subclasses must implement their initial fetch logic here.
  Future<List<int>> performInitialLoad();

  /// Fetches the next page of data. The [lastId] refers to the last ID currently loaded,
  /// and [viewedIds] can be used to inform the backend of already displayed items.
  ///
  /// [isInitialLoad] distinguishes between initial and paginated requests.
  Future<List<int>> fetchPage({
    required int? lastId,
    required List<int> viewedIds,
    bool isInitialLoad = false,
  });

  /// Used to determine if new items exist compared to current state.
  Future<bool> hasNewItems();

  // === PAGINATION MANAGEMENT ===

  /// Updates pagination state only if the new state differs from the current.
  void updatePaginationState(PaginationState newState) {
    if (_paginationState != newState) {
      _paginationState = newState;
    }
  }

  /// Loads more data and appends new IDs to the current state.
  ///
  /// Handles loading state and error updates.
  Future<void> loadMore() async {
    if (_isLoadingMore || !_paginationState.canLoadMore || currentIds.isEmpty) {
      return;
    }

    _isLoadingMore = true;
    updatePaginationState(
      _paginationState.copyWith(isLoadingMore: true, loadMoreError: null),
    );

    try {
      final lastId = currentIds.last;
      final newIds = await fetchPage(lastId: lastId, viewedIds: currentIds);

      if (newIds.isNotEmpty) {
        addToState(newIds);
      }

      final hasReachedEnd = await shouldMarkAsReachedEnd(newIds);
      updatePaginationState(
        _paginationState.copyWith(
          isLoadingMore: false,
          hasReachedEnd: hasReachedEnd,
        ),
      );
    } catch (error) {
      updatePaginationState(
        _paginationState.copyWith(isLoadingMore: false, loadMoreError: error),
      );

      if (kDebugMode) {
        debugPrint('Load more error: $error');
      }
    } finally {
      _isLoadingMore = false;
    }
  }

  /// Override to define when pagination should stop.
  ///
  /// Default: stop if no items were returned.
  Future<bool> shouldMarkAsReachedEnd(List<int> lastFetchedIds) async {
    return lastFetchedIds.isEmpty;
  }

  // === REFRESH MANAGEMENT ===

  /// Public method to trigger pull-to-refresh with debouncing.
  Future<void> refresh() async {
    // Cancel any existing refresh timer
    _refreshTimer?.cancel();

    // Start new debounced refresh
    _refreshTimer = Timer(getRefreshDebounceDelay(), () async {
      await _performRefresh();
    });
  }

  /// Override to adjust how long to debounce refresh requests.
  Duration getRefreshDebounceDelay() => const Duration(milliseconds: 300);

  /// Internally performs refresh by resetting pagination and replacing all IDs.
  Future<void> _performRefresh() async {
    if (_isRefreshing) return;

    _isRefreshing = true;

    try {
      updatePaginationState(const PaginationState());

      final newIds = await fetchPage(
        lastId: null,
        viewedIds: [],
        isInitialLoad: true,
      );
      replaceAll(newIds);
    } catch (error) {
      rethrow;
    } finally {
      _isRefreshing = false;
    }
  }

  // === BACKGROUND OPERATIONS ===

  /// Initiates a background check to determine if new items exist.
  ///
  /// Uses debounce to reduce frequency of checks.
  Future<void> checkForNewItems() async {
    // Cancel any existing check timer
    _checkNewTimer?.cancel();

    // Start new debounced check
    _checkNewTimer = Timer(getNewItemsCheckDelay(), () async {
      await _performNewItemsCheck();
    });
  }

  /// Override to customize the delay for checking new items.
  Duration getNewItemsCheckDelay() => const Duration(milliseconds: 500);

  /// Internal logic for checking new items and performing refresh if needed.
  Future<void> _performNewItemsCheck() async {
    if (_isCheckingForNew || currentIds.isEmpty) return;

    _isCheckingForNew = true;

    try {
      final hasNew = await hasNewItems();

      if (hasNew) {
        await _performRefresh();
      }
    } catch (error) {
      if (kDebugMode) {
        debugPrint('Check new items error: $error');
      }
    } finally {
      _isCheckingForNew = false;
    }
  }

  // === UTILITY METHODS ===

  /// Completely resets and reloads the list from scratch.
  Future<void> reload() async {
    _refreshTimer?.cancel();
    _checkNewTimer?.cancel();

    updatePaginationState(const PaginationState());
    _isRefreshing = false;
    _isCheckingForNew = false;
    _isLoadingMore = false;

    state = const AsyncLoading();

    try {
      final newIds = await performInitialLoad();
      updateStateAndCache(newIds);
    } catch (error) {
      state = AsyncError(error, StackTrace.current);
    }
  }

  /// Retry utility with exponential backoff and jitter.
  ///
  /// Retries up to [maxRetries] times and logs context-specific error.
  Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = 3,
    String context = 'unknown',
  }) async {
    Exception? lastException;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } on Exception catch (error) {
        lastException = error;

        if (attempt == maxRetries) {
          if (kDebugMode) {
            debugPrint('Max retries exceeded for $context: $error');
          }
          rethrow;
        }

        final baseDelay = Duration(seconds: 1 << (attempt - 1));
        final jitter = Duration(
          milliseconds: (DateTime.now().millisecond * 0.5).round(),
        );
        await Future.delayed(baseDelay + jitter);

        if (kDebugMode) {
          debugPrint(
            'Retrying $context (attempt $attempt/$maxRetries): $error',
          );
        }
      }
    }

    throw lastException ?? Exception('Max retries exceeded for $context');
  }

  /// Clears the current pagination load-more error.
  void clearLoadMoreError() {
    if (_paginationState.hasLoadMoreError) {
      updatePaginationState(_paginationState.copyWith(loadMoreError: null));
    }
  }

  // === GETTERS ===

  /// Whether more items are currently being loaded.
  bool get isLoadingMore => _isLoadingMore;

  /// Whether a refresh operation is in progress.
  bool get isRefreshing => _isRefreshing;

  /// Whether the background "check for new items" is running.
  bool get isCheckingForNew => _isCheckingForNew;

  /// Whether all available items have been loaded.
  bool get hasReachedEnd => _paginationState.hasReachedEnd;

  /// Error thrown during last load-more operation, if any.
  Object? get loadMoreError => _paginationState.loadMoreError;

  /// Whether loading more is possible based on current state.
  bool get canLoadMore => _paginationState.canLoadMore && currentIds.isNotEmpty;

  /// Whether the last load-more operation encountered an error.
  bool get hasLoadMoreError => _paginationState.hasLoadMoreError;

  /// Current pagination state object.
  PaginationState get paginationState => _paginationState;
}

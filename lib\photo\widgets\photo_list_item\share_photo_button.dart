import 'dart:developer';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:easy_social_share/easy_social_share.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/cache_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class SharePhotoButton extends ConsumerStatefulWidget {
  final PhotoData photo;
  final String title;
  final String message;
  final bool shrinkTapTarget;

  const SharePhotoButton({
    super.key,
    required this.photo,
    this.title = '',
    this.message = '',
    this.shrinkTapTarget = false,
  });

  @override
  SharePhotoButtonState createState() => SharePhotoButtonState();
}

class SharePhotoButtonState extends ConsumerState<SharePhotoButton> {
  final _easySocialShare = EasySocialShare();
  final platform = Platform.operatingSystem;

  @override
  Widget build(BuildContext context) {
    final String title = widget.title.isNotEmpty
        ? widget.title
        : 'Share this photo';

    final String message = widget.message.isNotEmpty
        ? widget.message
        : 'Check out this photo by ${widget.photo.authorDisplayName} on PortraitMode : ${widget.photo.attachmentUrl}';

    return Row(
      children: [
        IconButton(
          onPressed: () async {
            final String? photoLocalFilePath = await photoUrlToLocalFilePath(
              widget.photo.url,
            );

            log('filePath retrieved from cached file: $photoLocalFilePath');

            _shareToSystem(title, message, photoLocalFilePath);
          },
          padding: const EdgeInsets.all(0.0),
          constraints: const BoxConstraints(),
          iconSize: 22.0,
          icon: const Icon(Ionicons.share_outline),
          color: context.colors.iconColor,
          style: widget.shrinkTapTarget
              ? IconButton.styleFrom(
                  tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                  minimumSize: Size.zero,
                  padding: EdgeInsets.zero,
                )
              : null,
        ),
      ],
    );
  }

  Future<void> _shareToSystem(
    String title,
    String message,
    String? filePath,
  ) async {
    try {
      if (platform == 'android') {
        await _easySocialShare.android.shareToSystem(title, message, filePath);
      } else if (platform == 'ios') {
        await _easySocialShare.iOS.shareToSystem(
          message,
          filePaths: filePath != null ? [filePath] : null,
        );
      }
    } catch (e) {
      log('Error sharing photo with error: $e');
    }
  }

  static Future<String?> photoUrlToLocalFilePath(String photoUrl) async {
    try {
      // Get the cached file from cache manager
      final fileInfo = await PmCacheManager().getSingleFile(photoUrl);
      final String filePath = fileInfo.path;

      return filePath;
    } catch (e) {
      log('Error getting cached file path: $e');
      // Handle error appropriately
      return null;
    }
  }
}

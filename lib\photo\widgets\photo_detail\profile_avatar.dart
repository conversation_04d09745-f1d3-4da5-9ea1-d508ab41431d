import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class ProfileAvatar extends ConsumerWidget {
  const ProfileAvatar({
    super.key,
    this.size = 30.0,
    this.useBorder = false,
    this.borderColor = Colors.white,
    this.toProfileScreen = false,
    this.onTap,
  });

  final double size;
  final bool useBorder;
  final Color borderColor;
  final bool toProfileScreen;
  final Function? onTap;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    String providerNicename = ref.watch(
      profileProvider.select((profile) => profile.nicename),
    );

    String providerAvatarUrl = ref.watch(
      profileProvider.select((profile) => profile.avatarUrl),
    );

    late String avatarUrl;

    if (providerNicename.isEmpty) {
      avatarUrl = LocalUserService.avatarUrl ?? '';
    } else {
      avatarUrl = providerAvatarUrl;
    }

    return Avatar(
      imageUrl: avatarUrl,
      size: size,
      useBorder: useBorder,
      borderColor: borderColor,
      isOwnAvatar: true,
      onTap: () {
        _handleOnTap(context, ref);
      },
    );
  }

  void _handleOnTap(BuildContext context, WidgetRef ref) {
    onTap?.call();

    final user = LocalUserService.get();

    if (toProfileScreen) {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => ArtistDetailScreen(
            useBackButton: true,
            isOwnProfile: true,
            partialData: ArtistPartialData(
              id: user.userId ?? 0,
              nicename: user.nicename ?? '',
              displayName: user.displayName ?? '',
              profileUrl: user.profileUrl ?? '',
              avatarUrl: user.avatarUrl ?? '',
              membershipType: user.membershipType ?? '',
            ),
          ),
        ),
      );
    }
  }
}

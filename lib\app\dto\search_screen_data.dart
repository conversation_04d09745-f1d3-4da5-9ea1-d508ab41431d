import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/system_status/dto/system_status_data.dart';

class SearchScreenData {
  final SystemStatusData systemStatus;
  final PhotoData? latestPotd;
  final List<BlogPostData> blogPosts;
  final List<CategoryData> categories;
  final List<CameraData> cameras;
  final List<PhotoData> photos;
  final List<PhotoData> potdList;

  SearchScreenData({
    required this.systemStatus,
    this.latestPotd,
    this.blogPosts = const [],
    this.categories = const [],
    this.cameras = const [],
    this.photos = const [],
    this.potdList = const [],
  });

  factory SearchScreenData.fromMap(Map<String, dynamic> map) {
    return SearchScreenData(
      latestPotd: map['latestPotd'] != null && map['latestPotd'] is Map
          ? PhotoData.fromMap(map['latestPotd'])
          : null,
      blogPosts: map['blogPosts'] != null && map['blogPosts'] is List
          ? (map['blogPosts'] as List)
                .map((e) => BlogPostData.fromMap(e))
                .toList()
          : [],
      categories: map['categories'] != null && map['categories'] is List
          ? (map['categories'] as List)
                .map((e) => CategoryData.fromMap(e))
                .toList()
          : [],
      cameras: map['cameras'] != null && map['cameras'] is List
          ? (map['cameras'] as List).map((e) => CameraData.fromMap(e)).toList()
          : [],
      photos: map['photos'] != null && map['photos'] is List
          ? (map['photos'] as List).map((e) => PhotoData.fromMap(e)).toList()
          : [],
      potdList: map['potdList'] != null && map['potdList'] is List
          ? (map['potdList'] as List).map((e) => PhotoData.fromMap(e)).toList()
          : [],
      systemStatus: map['systemStatus'] != null && map['systemStatus'] is Map
          ? SystemStatusData.fromMap(map['systemStatus'])
          : SystemStatusData(),
    );
  }
}

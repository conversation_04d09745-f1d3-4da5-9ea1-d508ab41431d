import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/dto/category_photo_list_interaction_data.dart';
import 'package:portraitmode/category/providers/category_photo_list_interaction_provider.dart';

class CategoryPhotoListInteractionWatcher extends ConsumerWidget {
  final String slug;

  const CategoryPhotoListInteractionWatcher({super.key, required this.slug});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final NotifierProvider<
      CategoryPhotoListInteractionNotifier,
      CategoryPhotoListInteractionData
    >
    provider = getCategoryPhotoListInteractionProvider(slug);

    final int lastItemSeenId = ref.watch(
      provider.select((data) => data.lastItemSeenId),
    );

    return lastItemSeenId > 0
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}

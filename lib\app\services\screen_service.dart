import 'package:dio/dio.dart';
import 'package:portraitmode/app/http_responses/search_screen_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class ScreenService extends BaseService {
  Future<SearchScreenResponse> fetchSearchScreenData() async {
    try {
      final httpResponse = await http.get(
        '${URL.baseApiUrl}/screens/search-screen',
      );

      SearchScreenResponse response = SearchScreenResponse.fromMap(
        httpResponse.data,
      );

      return response;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return SearchScreenResponse.fromMap(e.response?.data);
      }

      return SearchScreenResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return SearchScreenResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

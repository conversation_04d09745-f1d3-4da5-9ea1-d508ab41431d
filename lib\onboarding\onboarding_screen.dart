import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/widgets/screen_selector.dart';
import 'package:portraitmode/hive/services/local_settings_service.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/onboarding/submission_guidelines.dart';

class OnboardingScreen extends StatefulWidget {
  final bool isLoggedIn;

  const OnboardingScreen({super.key, this.isLoggedIn = false});

  @override
  OnboardingScreenState createState() => OnboardingScreenState();
}

class OnboardingScreenState extends State<OnboardingScreen> {
  final int _totalPages = 4;
  int _currentPageIndex = 0;
  final PageController _pageController = PageController(initialPage: 0);
  ScrollPhysics? _pageViewPhysics;
  final _scrollController = ScrollController();

  String? _displayName;

  @override
  void initState() {
    if (widget.isLoggedIn) {
      _displayName = LocalUserService.displayName;
    }

    super.initState();
  }

  @override
  void dispose() {
    _pageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        constraints: const BoxConstraints.expand(),
        child: PageView(
          controller: _pageController,
          physics: _pageViewPhysics,
          onPageChanged: (int pageIndex) {
            if (!mounted) return;

            setState(() {
              _currentPageIndex = pageIndex;
            });
          },
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 55.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: _generateContent(
                  imagePath:
                      'assets/onboarding/onboarding-illustration-welcome${context.isDarkMode ? '-dark' : ''}.jpg',
                  title: 'Welcome to PortraitMode',
                  bodyWidget: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildParagraph(
                        text:
                            "PortraitMode is the fastest-growing platform for sharing street photography. Join our global community, build your portfolio, and share your photos.",
                        isCentered: false,
                      ),
                      const SizedBox(height: 14.0),
                      _buildParagraph(
                        text: "It's 100% free. Now & forever.",
                        isCentered: false,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.only(bottom: 20.0),
              child: ListView(
                controller: _scrollController,
                padding: const EdgeInsets.only(
                  top: 45.0,
                  right: 55.0,
                  left: 55.0,
                  bottom: 20.0,
                ),
                children: _generateContent(
                  title: 'Submission guidelines',
                  bodyWidget: SubmissionGuidelines(
                    artistDisplayName: _displayName,
                  ),
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 55.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: _generateContent(
                  imagePath:
                      'assets/onboarding/onboarding-illustration-categories${context.isDarkMode ? '-dark' : ''}.jpg',
                  title: 'No hashtags, but categories',
                  body:
                      "Increase discoverability - Categorize your photos and help us make PortraitMode the best possible resource for street photography.",
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 55.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.center,
                children: _generateContent(
                  imagePath:
                      'assets/onboarding/onboarding-illustration-post-photos${context.isDarkMode ? '-dark' : ''}.jpg',
                  title: 'Post your first 3 photos',
                  body:
                      'Share your first 3 photos to get listed on PortraitMode and have other photographers discover you and your street photography.',
                ),
              ),
            ),
          ],
        ),
      ),
      bottomNavigationBar: SafeArea(child: _buildBottomSheet()),
    );
  }

  Widget _buildBottomSheet() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 55),
      margin: const EdgeInsets.only(bottom: 20.0),
      height: 60.0,
      child: LayoutBuilder(
        builder: (context, constraints) {
          return Stack(
            children: [
              // Dots indicator
              Positioned(
                left: 0,
                top: 0,
                bottom: 0,
                child: Visibility(
                  visible: _currentPageIndex != _totalPages - 1,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      for (int i = 0; i < _totalPages; i++)
                        i == _currentPageIndex
                            ? _buildPageIndicatorItem(true)
                            : _buildPageIndicatorItem(false),
                    ],
                  ),
                ),
              ),
              // Animated button
              Positioned(
                right: 0,
                top: 0,
                bottom: 0,
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 150),
                  width: _currentPageIndex == _totalPages - 1
                      ? constraints.maxWidth
                      : 60.0,
                  height: 60.0,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.2),
                        offset: const Offset(-4, 4),
                        blurRadius: 5.0,
                        spreadRadius: 1.0,
                      ),
                    ],
                  ),
                  child: FilledButton(
                    onPressed: () {
                      if (_currentPageIndex != _totalPages - 1) {
                        _handleNextButtonPressed();
                      } else {
                        _handleDoneButtonPressed();
                      }
                    },
                    style: FilledButton.styleFrom(
                      padding: const EdgeInsets.all(0.0),
                      elevation: 0.0,
                      backgroundColor: context.colors.brandColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50.0),
                      ),
                    ),
                    child: AnimatedCrossFade(
                      duration: const Duration(milliseconds: 300),
                      firstChild: const Icon(
                        Ionicons.arrow_forward_outline,
                        size: 20.0,
                        color: Colors.white,
                      ),
                      secondChild: const Text(
                        "Let's go!",
                        style: TextStyle(color: Colors.white, fontSize: 16.0),
                      ),
                      crossFadeState: _currentPageIndex == _totalPages - 1
                          ? CrossFadeState.showSecond
                          : CrossFadeState.showFirst,
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  Widget _buildPageIndicatorItem(bool isCurrentPage) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 350),
      margin: const EdgeInsets.symmetric(horizontal: 5.0),
      height: isCurrentPage ? 10.0 : 6.0,
      width: isCurrentPage ? 10.0 : 6.0,
      decoration: BoxDecoration(
        color: isCurrentPage
            ? context.colors.brandColorAlt
            : context.colors.baseColor,
        borderRadius: const BorderRadius.all(Radius.circular(12)),
      ),
    );
  }

  void _handleNextButtonPressed() {
    _pageController.nextPage(
      duration: const Duration(milliseconds: 500),
      curve: Curves.ease,
    );
  }

  Future<void> _handleDoneButtonPressed() async {
    await LocalSettingsService.setDoneOnboarding(true);

    if (mounted) {
      Navigator.pushReplacement(
        context,
        MaterialPageRoute(builder: (context) => const ScreenSelector()),
      );
    }
  }

  List<Widget> _generateContent({
    String? imagePath,
    Widget? titleWidget,
    String? title,
    double? titleFontSize,
    Widget? bodyWidget,
    String? body,
  }) {
    return <Widget>[
      if (imagePath != null) Center(child: Image.asset(imagePath)),
      const SizedBox(height: 34),
      if (titleWidget != null) titleWidget,
      if (title != null)
        Text(
          title,
          style: TextStyle(
            fontSize: titleFontSize ?? 20.0,
            height: 1.5,
            fontWeight: FontWeight.w600,
          ),
        ),
      const SizedBox(height: 14),
      if (bodyWidget != null) bodyWidget,
      if (body != null)
        Text(
          body,
          style: TextStyle(
            fontSize: 14,
            height: 1.8,
            color: context.colors.brandColorAlt,
          ),
        ),
    ];
  }

  Text _buildParagraph({required String text, required bool isCentered}) {
    return Text(
      text,
      textAlign: isCentered ? TextAlign.center : TextAlign.start,
      style: TextStyle(
        fontSize: 14.0,
        height: 1.4,
        fontWeight: FontWeight.w500,
        color: context.colors.brandColorAlt,
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:portraitmode/image_pinch_zooming/backup/pinch_zoom_overlay_image.dart';

/// Thanks a lot to <PERSON><PERSON><PERSON> for his "pinch_zoom_image_last" library.
/// His package is discontinued.
///
/// His website & GitHub URL:
/// furkankurt.com.tr
/// https://github.com/furkankurt
///
/// His package URL on pub.dev & GitHub:
/// https://pub.dev/packages/image_pinch_zooming
/// https://github.com/furkankurt/pinch_zoom_image_last
///
/// I have to modify his package to make it work properly
/// when then image is displayed in a scrollable list.
/// The issue was: it's a bit hard to pinch zoom the image due to scrolling.
///
/// Thanks a lot for Furkan KURT and the package contributors.
class ImagePinchZooming extends StatefulWidget {
  const ImagePinchZooming({
    super.key,
    required this.image,
    this.zoomedBackgroundColor = Colors.transparent,
    this.hideStatusBarWhileZooming = false,
    this.twoFingersOn,
    this.twoFingersOff,
    this.onZoomStart,
    this.onZoomEnd,
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
  });

  final Widget image;
  final Color zoomedBackgroundColor;
  final bool hideStatusBarWhileZooming;
  final Function? twoFingersOn;
  final Function? twoFingersOff;
  final Function? onZoomStart;
  final Function? onZoomEnd;
  final Function? onTap;
  final Function? onDoubleTap;
  final Function? onLongPress;

  @override
  ImagePinchZoomingState createState() => ImagePinchZoomingState();
}

class ImagePinchZoomingState extends State<ImagePinchZooming> {
  OverlayEntry? overlayEntry;
  Offset? scaleStartPosition;
  Offset? origin;
  int numPointers = 0;
  bool zooming = false;
  bool reversing = false;
  GlobalKey<PinchZoomOverlayImageState> overlayKey = GlobalKey();

  bool _twoFingersOnIsCalled = false;
  bool _twoFingersOffIsCalled = true;

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: _handlePointerDown,
      onPointerUp: _handlePointerUp,
      onPointerCancel: _handlePointerCancel,
      child: GestureDetector(
        onScaleStart: _handleScaleStart,
        onScaleUpdate: _handleScaleUpdate,
        onScaleEnd: _handleScaleEnd,
        onTap: () {
          widget.onTap?.call();
        },
        onDoubleTap: () {
          widget.onDoubleTap?.call();
        },
        onLongPress: () {
          widget.onLongPress?.call();
        },
        child: Stack(
          clipBehavior: Clip.none,
          children: <Widget>[
            Opacity(opacity: zooming ? 0.0 : 1.0, child: widget.image),
            Positioned(
              top: 0.0,
              left: 0.0,
              right: 0.0,
              bottom: 0.0,
              child: Container(
                color: zooming
                    ? widget.zoomedBackgroundColor
                    : Colors.transparent,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _handlePointerDown(PointerDownEvent event) {
    numPointers++;

    // log('pointer is down and numPointers is: $numPointers');

    if (!_twoFingersOnIsCalled && numPointers >= 2) {
      widget.twoFingersOn?.call();
      _twoFingersOnIsCalled = true;
      _twoFingersOffIsCalled = false;
    }

    // log('_twoFingersOnIsCalled is: ${_twoFingersOnIsCalled.toString()}');
  }

  void _handlePointerUp(PointerUpEvent event) {
    numPointers--;

    // log('pointer is up and numPointers is: $numPointers');

    if (!_twoFingersOffIsCalled && numPointers < 2) {
      widget.twoFingersOff?.call();
      _twoFingersOnIsCalled = false;
      _twoFingersOffIsCalled = true;
    }

    // log('_twoFingersOffIsCalled is: ${_twoFingersOffIsCalled.toString()}');
  }

  void _handlePointerCancel(PointerCancelEvent event) {
    numPointers = 0;

    // log('pointer is cancelled and numPointers is: $numPointers');

    _twoFingersOnIsCalled = false;
    _twoFingersOffIsCalled = false;
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (overlayEntry != null || reversing || numPointers < 2) return;
    setState(() {
      zooming = true;
    });

    if (widget.hideStatusBarWhileZooming) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    }

    widget.onZoomStart?.call();

    OverlayState overlayState = Overlay.of(context);
    double width = context.size!.width;
    double height = context.size!.height;
    origin = (context.findRenderObject() as RenderBox).localToGlobal(
      const Offset(0.0, 0.0),
    );
    scaleStartPosition = details.focalPoint;

    overlayEntry = OverlayEntry(
      maintainState: true,
      builder: (BuildContext context) {
        return PinchZoomOverlayImage(
          key: overlayKey,
          height: height,
          width: width,
          origin: origin!,
          image: widget.image,
        );
      },
    );

    overlayState.insert(overlayEntry!);
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (reversing || numPointers < 2) return;
    overlayKey.currentState?.updatePosition(
      origin! - (scaleStartPosition! - details.focalPoint),
    );

    if (details.scale >= 1.0) {
      overlayKey.currentState?.updateScale(details.scale);
    }
  }

  void _handleScaleEnd(ScaleEndDetails details) async {
    if (reversing || !zooming) return;
    reversing = true;

    if (widget.hideStatusBarWhileZooming) {
      SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
    }

    widget.onZoomEnd?.call();

    await overlayKey.currentState?.reverse();
    overlayEntry?.remove();
    overlayEntry = null;
    origin = null;
    scaleStartPosition = null;
    reversing = false;
    setState(() {
      zooming = false;
    });
  }
}

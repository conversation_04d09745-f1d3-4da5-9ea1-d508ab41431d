// comment_list_provider.dart

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

/// Manages comment ordering and photo associations WITHOUT storing CommentData
/// All actual CommentData is stored in comment_store_provider.dart
final class CommentListManagerNotifier extends Notifier<Map<int, List<int>>> {
  @override
  Map<int, List<int>> build() => {};

  void _updatePhotoCommentIds(int photoId, List<int> commentIds) {
    final newState = Map<int, List<int>>.from(state);
    newState[photoId] = commentIds;
    state = newState;
  }

  List<int> getCommentIdsForPhoto(int photoId) {
    return state[photoId] ?? [];
  }

  void prepend(int photoId, int commentId) {
    final currentIds = getCommentIdsForPhoto(photoId);
    if (currentIds.contains(commentId)) return; // Prevent duplicates

    final newIds = [commentId, ...currentIds];
    _updatePhotoCommentIds(photoId, newIds);
  }

  void add(int photoId, int commentId) {
    final currentIds = getCommentIdsForPhoto(photoId);
    if (currentIds.contains(commentId)) return; // Prevent duplicates

    final newIds = [...currentIds, commentId];
    _updatePhotoCommentIds(photoId, newIds);
  }

  void prependAll(int photoId, List<int> commentIds) {
    final currentIds = getCommentIdsForPhoto(photoId);
    final uniqueNewIds = commentIds
        .where((id) => !currentIds.contains(id))
        .toList();

    if (uniqueNewIds.isEmpty) return;

    final newIds = [...uniqueNewIds, ...currentIds];
    _updatePhotoCommentIds(photoId, newIds);
  }

  void addAll(int photoId, List<int> commentIds) {
    final currentIds = getCommentIdsForPhoto(photoId);
    final uniqueNewIds = commentIds
        .where((id) => !currentIds.contains(id))
        .toList();

    if (uniqueNewIds.isEmpty) return;

    final newIds = [...currentIds, ...uniqueNewIds];
    _updatePhotoCommentIds(photoId, newIds);
  }

  void remove(int photoId, int commentId) {
    final currentIds = getCommentIdsForPhoto(photoId);
    final newIds = currentIds.where((id) => id != commentId).toList();

    if (newIds.length != currentIds.length) {
      _updatePhotoCommentIds(photoId, newIds);
    }
  }

  void reorder(int photoId, int oldIndex, int newIndex) {
    final currentIds = List<int>.from(getCommentIdsForPhoto(photoId));

    if (oldIndex < 0 ||
        oldIndex >= currentIds.length ||
        newIndex < 0 ||
        newIndex >= currentIds.length) {
      return;
    }

    final item = currentIds.removeAt(oldIndex);
    currentIds.insert(newIndex, item);
    _updatePhotoCommentIds(photoId, currentIds);
  }

  void replaceAll(int photoId, List<int> commentIds) {
    _updatePhotoCommentIds(photoId, commentIds);
  }

  void clearByPhotoId(int photoId) {
    final newState = Map<int, List<int>>.from(state);
    newState.remove(photoId);
    state = newState;
  }

  void clearAll() {
    state = {};
  }

  // Memory management: Remove unused photo comment associations
  void evictUnusedPhotos(Set<int> activePhotoIds) {
    final keysToRemove = state.keys
        .where((photoId) => !activePhotoIds.contains(photoId))
        .toList();

    if (keysToRemove.isEmpty) return;

    final newState = Map<int, List<int>>.from(state);
    for (final photoId in keysToRemove) {
      newState.remove(photoId);
    }

    state = newState;
  }
}

/// Global provider for comment list management (only stores IDs and ordering)
final commentListManagerProvider =
    NotifierProvider.autoDispose<
      CommentListManagerNotifier,
      Map<int, List<int>>
    >(CommentListManagerNotifier.new);

/// Derived provider that returns actual CommentData for a specific photo
/// This fetches data from the global comment_store_provider
final commentListProvider = Provider.family.autoDispose<List<CommentData>, int>(
  (ref, photoId) {
    final commentIds = ref.watch(commentListManagerProvider)[photoId] ?? [];
    final commentStore = ref.watch(commentStoreProvider);

    // Convert IDs to actual CommentData objects, preserving order
    return commentIds
        .map(
          (id) => commentStore.firstWhereOrNull((comment) => comment.id == id),
        )
        .where((comment) => comment != null)
        .cast<CommentData>()
        .toList();
  },
);

/// Derived provider that returns a specific (single) comment from a photo
final commentFromListProvider = Provider.family
    .autoDispose<CommentData?, ({int photoId, int commentId})>((ref, params) {
      final commentStore = ref.watch(commentStoreProvider);
      final commentIds =
          ref.watch(commentListManagerProvider)[params.photoId] ?? [];

      if (!commentIds.contains(params.commentId)) return null;

      return commentStore.firstWhereOrNull(
        (comment) => comment.id == params.commentId,
      );
    });

/// Derived provider for comment list length
final commentListLengthProvider = Provider.family.autoDispose<int, int>((
  ref,
  photoId,
) {
  final commentIds = ref.watch(commentListManagerProvider)[photoId] ?? [];
  return commentIds.length;
});

/// Derived provider that checks if a photo has comments
final hasCommentsProvider = Provider.family.autoDispose<bool, int>((
  ref,
  photoId,
) {
  final length = ref.watch(commentListLengthProvider(photoId));
  return length > 0;
});

/// Direct access to a single comment by ID.
/// Most efficient for global single comment access.
final singleCommentProvider = Provider.family.autoDispose<CommentData?, int>((
  ref,
  commentId,
) {
  final commentStore = ref.watch(commentStoreProvider);
  return commentStore.firstWhereOrNull((comment) => comment.id == commentId);
});

/// High-level service for managing photo comments
/// This is the recommended way to manage comments for photos
final class PhotoCommentService {
  const PhotoCommentService(this.ref);

  final Ref ref;

  // Get comments for a photo
  List<CommentData> getComments(int photoId) {
    return ref.read(commentListProvider(photoId));
  }

  // Add a new comment (adds to both global store and photo list)
  void addComment(
    int photoId,
    CommentData comment, {
    bool updatePhotoTotalComments = false,
  }) {
    // Add to global store
    ref.read(commentStoreProvider.notifier).addItem(comment);

    // Only add to photo's comment list if this is a top level comment.
    if (comment.parentId == 0) {
      ref.read(commentListManagerProvider.notifier).add(photoId, comment.id);
    }

    // If comment is in submitted (ready) state,
    // increment the related photo's totalComments.
    if (updatePhotoTotalComments &&
        comment.submissionStatus == CommentSubmissionStatus.submitted) {
      ref.read(photoStoreProvider.notifier).incrementTotalComments(photoId);
    }
  }

  // Add multiple comments
  void addComments(
    int photoId,
    List<CommentData> comments, {
    bool updatePhotoTotalComments = false,
  }) {
    // Add to global store
    ref.read(commentStoreProvider.notifier).addItems(comments);

    // Add to photo's comment list if they are top level comments.
    final commentIds = comments
        .where((c) => c.parentId == 0)
        .map((c) => c.id)
        .toList();

    ref.read(commentListManagerProvider.notifier).addAll(photoId, commentIds);

    final totalCommentsWithSubmittedStatus = comments
        .where((c) => c.submissionStatus == CommentSubmissionStatus.submitted)
        .length;

    if (updatePhotoTotalComments) {
      // Set the related photo's totalComments via the `addTotalComments` method.
      ref
          .read(photoStoreProvider.notifier)
          .addTotalComments(photoId, totalCommentsWithSubmittedStatus);
    }
  }

  // Prepend a comment (for new comments that should appear first)
  void prependComment(
    int photoId,
    CommentData comment, {
    bool updatePhotoTotalComments = false,
  }) {
    // Add to global store
    ref.read(commentStoreProvider.notifier).addItem(comment);

    // Prepend to photo's comment list if it's top level comment.
    if (comment.parentId == 0) {
      ref
          .read(commentListManagerProvider.notifier)
          .prepend(photoId, comment.id);
    }

    // If comment is in submitted (ready) state,
    // increment the related photo's totalComments
    if (updatePhotoTotalComments &&
        comment.submissionStatus == CommentSubmissionStatus.submitted) {
      ref.read(photoStoreProvider.notifier).incrementTotalComments(photoId);
    }
  }

  void prependComments(
    int photoId,
    List<CommentData> comments, {
    bool updatePhotoTotalComments = false,
  }) {
    // Add to global store
    ref.read(commentStoreProvider.notifier).addItems(comments);

    // Add to photo's comment list if they are top level comments.
    final commentIds = comments
        .where((c) => c.parentId == 0)
        .map((c) => c.id)
        .toList();

    ref
        .read(commentListManagerProvider.notifier)
        .prependAll(photoId, commentIds);

    if (updatePhotoTotalComments) {
      final totalCommentsWithSubmittedStatus = comments
          .where((c) => c.submissionStatus == CommentSubmissionStatus.submitted)
          .length;

      // Set the related photo's totalComments via the `addTotalComments` method.
      ref
          .read(photoStoreProvider.notifier)
          .addTotalComments(photoId, totalCommentsWithSubmittedStatus);
    }
  }

  // Remove a comment
  void removeComment(
    int photoId,
    int commentId, {
    bool updatePhotoTotalComments = false,
  }) {
    // Remove from photo's comment list
    ref.read(commentListManagerProvider.notifier).remove(photoId, commentId);

    // Optionally remove from global store if not used elsewhere
    ref.read(commentStoreProvider.notifier).removeItem(commentId);

    if (updatePhotoTotalComments) {
      final CommentData? comment = ref.read(singleCommentProvider(commentId));

      // If comment is in submitted (ready) state,
      // decrement the related photo's totalComments.
      if (comment?.submissionStatus == CommentSubmissionStatus.submitted) {
        ref.read(photoStoreProvider.notifier).decrementTotalComments(photoId);
      }
    }
  }

  // Update a comment (only updates global store, lists automatically reflect changes)
  void updateComment(CommentData updatedComment) {
    ref.read(commentStoreProvider.notifier).updateItem(updatedComment);
  }

  // Bulk update comment list (only updates global store, lists automatically reflect changes)
  void updateComments(List<CommentData> updatedComments) {
    ref.read(commentStoreProvider.notifier).updateItems(updatedComments);
  }

  // For pending state management
  void updateSubmissionStatus(
    int commentId,
    CommentSubmissionStatus submissionStatus,
  ) {
    final commentStore = ref.read(commentStoreProvider.notifier);
    final comment = commentStore.getItem(commentId);

    if (comment != null) {
      final updatedComment = comment.copyWith(
        submissionStatus: submissionStatus,
      );

      commentStore.updateItem(updatedComment);
    }
  }

  /// Replace all comments for a photo
  ///
  /// Don't update the photo's total comments and parent's total replies.
  void replaceAllComments(int photoId, List<CommentData> comments) {
    // Add to global store
    ref.read(commentStoreProvider.notifier).updateItems(comments);

    // Replace photo's comment list
    final commentIds = comments.map((c) => c.id).toList();

    ref
        .read(commentListManagerProvider.notifier)
        .replaceAll(photoId, commentIds);
  }

  /// Clear comments for a photo
  ///
  /// Don't update the photo's total comments and parent's total replies.
  void clearPhotoComments(int photoId) {
    ref.read(commentListManagerProvider.notifier).clearByPhotoId(photoId);

    // Update the related photo's totalComments
    ref.read(photoStoreProvider.notifier).setTotalComments(photoId, 0);
  }

  // Like/unlike operations (delegates to global store)
  void toggleLike(int commentId, bool isLiked) {
    ref.read(commentStoreProvider.notifier).setIsLiked(commentId, isLiked);

    if (isLiked) {
      ref.read(commentStoreProvider.notifier).incrementTotalLikes(commentId);
    } else {
      ref.read(commentStoreProvider.notifier).decrementTotalLikes(commentId);
    }
  }

  // Reply operations (delegates to global store)
  void setTotalReplies(int commentId, int totalReplies) {
    ref
        .read(commentStoreProvider.notifier)
        .setTotalReplies(commentId, totalReplies);
  }
}

/// Provider for the photo comment service
final photoCommentServiceProvider = Provider.autoDispose<PhotoCommentService>((
  ref,
) {
  return PhotoCommentService(ref);
});

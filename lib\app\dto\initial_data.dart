import 'package:portraitmode/artist/dto/simple_artist_data.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/notification/dto/notification_fetch_result.dart';

class InitialData {
  InitialData({
    this.simpleArtistData,
    this.unfollowedCategoryList = const [],
    this.trendingCategoryList = const [],
    this.notificationFetchResult,
    this.feedbackTokensAmount = 0,
  });

  final SimpleArtistData? simpleArtistData;
  final List<CategoryData> unfollowedCategoryList;
  final List<CategoryData> trendingCategoryList;
  final NotificationFetchResult? notificationFetchResult;
  final int feedbackTokensAmount;

  factory InitialData.fromMap(Map<String, dynamic> map) {
    return InitialData(
      simpleArtistData:
          map.containsKey('simpleArtistData') && map['simpleArtistData'] is Map
          ? SimpleArtistData.fromMap(map['simpleArtistData'])
          : null,
      unfollowedCategoryList:
          map.containsKey('unfollowedCategoryList') &&
              map['unfollowedCategoryList'] is List &&
              map['unfollowedCategoryList'].isNotEmpty
          ? map['unfollowedCategoryList']
                .map<CategoryData>((data) => CategoryData.fromMap(data))
                .toList()
          : [],
      trendingCategoryList:
          map.containsKey('trendingCategoryList') &&
              map['trendingCategoryList'] is List &&
              map['trendingCategoryList'].isNotEmpty
          ? map['trendingCategoryList']
                .map<CategoryData>((data) => CategoryData.fromMap(data))
                .toList()
          : [],
      notificationFetchResult:
          map.containsKey('notificationFetchResult') &&
              map['notificationFetchResult'] is Map
          ? NotificationFetchResult.fromMap(map['notificationFetchResult'])
          : null,
      feedbackTokensAmount:
          map.containsKey('feedbackTokensAmount') &&
              map['feedbackTokensAmount'] is int
          ? map['feedbackTokensAmount']
          : 0,
    );
  }
}

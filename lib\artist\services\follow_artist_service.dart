// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_service.dart';

// Internal packages.
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/artist/http_responses/follow_response.dart';

class FollowArtistService extends BaseService {
  Future<FollowResponse> follow(int artistId) async {
    return doAction(artistId, 'follow');
  }

  Future<FollowResponse> unfollow(int artistId) async {
    return doAction(artistId, 'unfollow');
  }

  Future<FollowResponse> doAction(int artistId, String actionType) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/$actionType-artist',
        data: {'artist_id': artistId.toString()},
      );

      return FollowResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return FollowResponse.fromMap(e.response?.data);
      }

      return FollowResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return FollowResponse(success: false, message: "Something went wrong.");
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/widgets/album_dropdown.dart';
import 'package:portraitmode/asset_picker/widgets/asset_grid_view.dart';
import 'package:portraitmode/asset_picker/widgets/gallery_header.dart';

class GalleryScreen extends StatefulWidget {
  final int gridAxisCount;
  final int gridItemSize;
  final int gridImageQuality;
  final int perPage;
  final int initialPage;
  final List<AssetPathEntity> initialAlbumEntityList;
  final List<AssetEntity> initialAssetEntityList;
  final double galleryHeaderHeight;

  const GalleryScreen({
    super.key,
    required this.gridAxisCount,
    required this.gridItemSize,
    required this.gridImageQuality,
    required this.perPage,
    required this.initialPage,
    this.initialAlbumEntityList = const [],
    this.initialAssetEntityList = const [],
    this.galleryHeaderHeight = 63.0,
  });

  @override
  GalleryScreenState createState() => GalleryScreenState();
}

class GalleryScreenState extends State<GalleryScreen>
    with SingleTickerProviderStateMixin {
  final List<AssetPathEntity> _albumEntityList = [];

  AssetPathEntity? _activeAlbumEntity;
  bool _albumDropdownOpened = false;

  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();

    _albumEntityList.addAll(widget.initialAlbumEntityList);

    _activeAlbumEntity = _albumEntityList.isEmpty ? null : _albumEntityList[0];

    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 800),
      reverseDuration: const Duration(milliseconds: 300),
      value: 0.0,
    );

    _animation = Tween(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.fastLinearToSlowEaseIn,
        reverseCurve: Curves.easeOut,
      ),
    );
  }

  @override
  void dispose() {
    _albumEntityList.clear();
    _activeAlbumEntity = null;
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final panelMaxHeight =
        MediaQuery.sizeOf(context).height - widget.galleryHeaderHeight;

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: SystemUiOverlayStyle.dark,
      child: Scaffold(
        backgroundColor: Colors.black,
        body: SafeArea(
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  GalleryHeader(
                    activeAlbum: _activeAlbumEntity,
                    height: widget.galleryHeaderHeight,
                    onCloseButtonTap: _handleCloseButtonTap,
                    onDropdownToggle: _handleDropdownToggle,
                  ),
                  Expanded(
                    child: RepaintBoundary(
                      child: AssetGridView(
                        gridAxisCount: widget.gridAxisCount,
                        gridItemSize: widget.gridItemSize,
                        gridImageQuality: widget.gridImageQuality,
                        perPage: widget.perPage,
                        initialPage: widget.initialPage,
                        album: _activeAlbumEntity,
                        initialAssetEntityList: widget.initialAssetEntityList,
                      ),
                    ),
                  ),
                ],
              ),
              AlbumDropdown(
                galleryHeaderHeight: widget.galleryHeaderHeight,
                gridImageQuality: widget.gridImageQuality,
                gridItemSize: widget.gridItemSize,
                panelMaxHeight: panelMaxHeight,
                animation: _animation,
                albums: widget.initialAlbumEntityList,
                onSelect: _handleAlbumSelected,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleCloseButtonTap() {
    if (_albumDropdownOpened) {
      _handleDropdownToggle();
      return;
    }

    if (Navigator.of(context).canPop()) {
      Navigator.of(context).pop(null);
    }
  }

  void _handleDropdownToggle() {
    if (_animationController.isAnimating) return;

    if (_animation.value == 1.0) {
      _animationController.reverse();
      _albumDropdownOpened = false;

      return;
    }

    _animationController.forward();
    _albumDropdownOpened = true;
  }

  Future<void> _handleAlbumSelected(AssetPathEntity album) async {
    if (_activeAlbumEntity == album) return;

    if (mounted) {
      setState(() {
        _activeAlbumEntity = album;
      });
    }

    _handleDropdownToggle();
  }
}

import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class FeedbackTokenService extends BaseService {
  Future<BaseResponse> submitAssignment(int photoId, String actionType) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/feedback/assignment',
        data: {'photo_id': photoId, 'action_type': actionType},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

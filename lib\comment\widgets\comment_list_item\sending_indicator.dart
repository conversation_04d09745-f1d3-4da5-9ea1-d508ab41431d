import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class SendingIndicator extends StatefulWidget {
  const SendingIndicator({super.key, this.onRetry});

  final void Function()? onRetry;

  @override
  SendingIndicatorState createState() => SendingIndicatorState();
}

class SendingIndicatorState extends State<SendingIndicator> {
  @override
  Widget build(BuildContext context) {
    return Text(
      'Sending...',
      style: TextStyle(fontSize: 12.0, color: context.colors.darkerGreyColor),
    );
  }
}

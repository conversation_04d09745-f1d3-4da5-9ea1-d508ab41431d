import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/utils/add_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form/reply_to.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

final class CommentForm extends ConsumerWidget {
  final TextEditingController fieldController;
  final FocusNode focusNode;
  final GlobalKey photoDescriptionKey;
  final int photoId;
  final GlobalKey? activeCommentKey;

  const CommentForm({
    super.key,
    required this.fieldController,
    required this.focusNode,
    required this.photoDescriptionKey,
    required this.photoId,
    this.activeCommentKey,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final CommentFormMode formMode = ref.watch(
      commentActivityProvider.select((data) => data.formMode),
    );

    final int? commentIdToReply = ref.watch(
      commentActivityProvider.select((item) => item.commentIdToReply),
    );

    final CommentData? commentToReply = commentIdToReply != null
        ? ref.watch(singleCommentProvider(commentIdToReply))
        : null;

    final bool autofocus = commentIdToReply != null;

    return SafeArea(
      top: false,
      child: Form(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            commentIdToReply == null
                ? const SizedBox.shrink()
                : ReplyTo(
                    name: commentToReply?.authorNicename ?? '',
                    hasCloseButton: formMode == CommentFormMode.add,
                    onClose: () => _handleCloseReplyTo(ref),
                  ),
            Container(
              padding: (commentIdToReply == null
                  ? const EdgeInsets.symmetric(
                      horizontal: ScreenStyleConfig.horizontalPadding,
                    )
                  : const EdgeInsets.only(
                      right: ScreenStyleConfig.horizontalPadding,
                      left: ScreenStyleConfig.horizontalPadding,
                      bottom: 5.0,
                    )),
              decoration: BoxDecoration(
                border: Border(
                  top: BorderSide(
                    color: context.isDarkMode
                        ? AppColorsCache.dark().baseColorAlt
                        : AppColorsCache.light().baseColor,
                    width: 0.5,
                  ),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  ProfileAvatar(),
                  const SizedBox(width: 10.0),
                  Expanded(
                    child: TextFormField(
                      controller: fieldController,
                      focusNode: focusNode,
                      keyboardType: TextInputType.multiline,
                      textCapitalization: TextCapitalization.sentences,
                      autofocus: autofocus,
                      minLines: 1,
                      maxLines: 7,
                      style: const TextStyle(fontSize: 13.0),
                      decoration: const InputDecoration(
                        hintText: "Add a comment...",
                        contentPadding: EdgeInsets.symmetric(
                          vertical: 18.0,
                          horizontal: 5.0,
                        ),
                        border: InputBorder.none,
                      ),
                      onTap: () => _handleInputTap(ref),
                      onEditingComplete: () async {
                        FocusScope.of(context).nextFocus();
                      },
                    ),
                  ),
                  if (CommentFormMode.edit == formMode)
                    _buildEditButtons(context, ref)
                  else
                    _buildSubmitButton(context, ref, labelText: "Submit"),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEditButtons(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        TextButton(
          onPressed: () => _handleCancelButtonPressed(ref),
          child: Text(
            'Cancel',
            style: TextStyle(
              color: ref.context.colors.dangerColor,
              fontSize: 14.0,
              fontWeight: FontWeight.w400,
            ),
          ),
        ),
        const SizedBox(width: 10.0),
        _buildSubmitButton(context, ref, labelText: "Save"),
      ],
    );
  }

  Widget _buildSubmitButton(
    BuildContext context,
    WidgetRef ref, {
    required String labelText,
  }) {
    return TextButton(
      onPressed: () => _handleSubmitButtonPressed(context, ref),
      child: Text(
        labelText,
        style: TextStyle(
          color: ref.context.colors.accentColor,
          fontSize: 14.0,
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  void _handleCloseReplyTo(WidgetRef ref) async {
    final targetContext = photoDescriptionKey.currentContext;

    if (targetContext != null) {
      await Scrollable.ensureVisible(
        targetContext,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.0,
      );
    }
  }

  Future<void> _handleInputTap(WidgetRef ref) async {
    final GlobalKey targetKey = activeCommentKey ?? photoDescriptionKey;

    if (targetKey.currentContext != null) {
      await Scrollable.ensureVisible(
        targetKey.currentContext!,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.0,
      );
    }
  }

  void _handleCancelButtonPressed(WidgetRef ref) {
    ref.read(commentActivityProvider.notifier).setFormMode(CommentFormMode.add);
    ref.read(commentActivityProvider.notifier).setCommentIdToEdit(null);
    ref.read(commentActivityProvider.notifier).setCommentIdToReply(null);

    fieldController.text = "";
    focusNode.unfocus();
  }

  Future<void> _handleSubmitButtonPressed(
    BuildContext context,
    WidgetRef ref,
  ) async {
    final CommentFormMode formMode = ref.read(commentActivityProvider).formMode;
    final GlobalKey targetKey = activeCommentKey ?? photoDescriptionKey;

    focusNode.unfocus();
    fieldController.text = "";

    if (formMode == CommentFormMode.add) {
      int? commentIdToReply = ref
          .read(commentActivityProvider)
          .commentIdToReply;

      log('commentIdToReply: $commentIdToReply');

      final DateTime now = DateTime.now();
      final formatter = DateFormat('yyyy-MM-dd HH:mm:ss');

      final CommentData commentToSubmit = CommentData(
        // Use negative id for temporary comment.
        id: -DateTime.now().microsecondsSinceEpoch,
        parentId: commentIdToReply ?? 0,
        postId: photoId,
        date: formatter.format(now),
        content: fieldController.text,
        authorId: LocalUserService.userId ?? 0,
        authorDisplayName: LocalUserService.displayName ?? '',
        authorAvatarUrl: LocalUserService.avatarUrl ?? '',
        authorMembershipType: LocalUserService.membershipType ?? '',
        submissionStatus: CommentSubmissionStatus.submitting,
      );

      await AddCommentUtil(
        context: context,
        ref: ref,
        photoId: photoId,
        commentToSubmit: commentToSubmit,
        targetKey: targetKey,
      ).handleSubmitEvent();

      return;
    }

    final int? commentIdToEdit = ref
        .read(commentActivityProvider)
        .commentIdToEdit;

    if (commentIdToEdit != null) {
      final CommentData? commentToEdit = ref.read(
        singleCommentProvider(commentIdToEdit),
      );

      if (commentToEdit != null) {
        CommentData? commentToSubmit = CommentData(
          id: commentIdToEdit,
          parentId: commentToEdit.parentId,
          postId: commentToEdit.postId,
          content: fieldController.text,
        );

        await EditCommentUtil(
          context: context,
          ref: ref,
          photoId: photoId,
          commentToEdit: commentToEdit,
          commentToSubmit: commentToSubmit,
          targetKey: targetKey,
        ).handleSubmitEvent();
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/utils/content_util.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';

class PhotoDescription extends StatelessWidget {
  final PhotoData photo;
  final String screenName;

  const PhotoDescription({
    super.key,
    required this.photo,
    required this.screenName,
  });

  @override
  Widget build(BuildContext context) {
    Color? textColor = Theme.of(context).textTheme.bodySmall?.color;

    return GestureDetector(
      onTap: () {
        _onTap(context);
      },
      child: Text(
        parseEmojiChars(photo.description),
        textAlign: TextAlign.start,
        style: TextStyle(fontSize: 13.0, height: 1.5, color: textColor),
      ),
    );
  }

  void _onTap(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoDetailScreen(
          photo: photo,
          originScreenName: screenName,
          isPhotoDetail: false,
        ),
      ),
    );
  }
}

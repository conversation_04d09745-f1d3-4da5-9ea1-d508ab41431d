// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/album/http_responses/album_list_response.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/photo/http_responses/photo_detail_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class PhotoService extends BaseService {
  Future<PhotoDetailResponse> find(int id) async {
    try {
      final response = await http.get('${URL.baseApiUrl}/photo/$id');

      return PhotoDetailResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoDetailResponse.fromMap(e.response?.data);
      }

      return PhotoDetailResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoDetailResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<AlbumListResponse> assignToAlbum({
    required int photoId,
    String albumSlug = '',
  }) async {
    try {
      final response = await http.put(
        '${URL.baseApiUrl}/photo/to-album',
        data: {'photo_id': photoId.toString(), 'album_slug': albumSlug},
      );

      return AlbumListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AlbumListResponse.fromMap(e.response?.data);
      }

      return AlbumListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return AlbumListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<BaseResponse> suggestTobeFeatured({required int photoId}) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/suggest-photo/tobe-featured',
        data: {'photo_id': photoId.toString()},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> reportPhoto({
    required int photoId,
    required String reason,
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/report-photo',
        data: {'photo_id': photoId.toString(), 'reason': reason},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

import 'package:portraitmode/album/dto/album_data.dart';

class AlbumListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<AlbumData> data;

  AlbumListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory AlbumListResponse.fromMap(Map<String, dynamic> map) {
    return AlbumListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<AlbumData>((data) => AlbumData.fromMap(data))
                .toList()
          : [],
    );
  }
}

class AlbumListWithPhotosResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<AlbumWithPhotosData> data;

  AlbumListWithPhotosResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory AlbumListWithPhotosResponse.fromMap(Map<String, dynamic> map) {
    return AlbumListWithPhotosResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<AlbumWithPhotosData>(
                  (data) => AlbumWithPhotosData.fromMap(data),
                )
                .toList()
          : [],
    );
  }
}

import 'package:portraitmode/moderation/dto/reported_photo_data.dart';

class ReportedPhotoListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<ReportedPhotoData> data;

  ReportedPhotoListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory ReportedPhotoListResponse.fromMap(Map<String, dynamic> map) {
    return ReportedPhotoListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<ReportedPhotoData>(
                  (data) => ReportedPhotoData.fromMap(data),
                )
                .toList()
          : [],
    );
  }
}

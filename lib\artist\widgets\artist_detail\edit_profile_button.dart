import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/profile/widgets/edit_profile_screen.dart';

class EditProfileButton extends StatelessWidget {
  const EditProfileButton({super.key, required this.artistId});

  final int artistId;

  @override
  Widget build(BuildContext context) {
    Color? textColor = Theme.of(context).textTheme.bodySmall?.color;

    return SizedBox(
      height: 35.0,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(builder: (context) => const EditProfileScreen()),
          );
        },
        style: ElevatedButton.styleFrom(
          elevation: 0.0,
          backgroundColor: context.isDarkMode
              ? AppColorsCache.dark().baseColorAlt
              : AppColorsCache.light().baseColor,
          padding: const EdgeInsets.symmetric(horizontal: 20.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(35.0),
          ),
        ),
        child: Text(
          'Edit profile',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 12.0,
            color: textColor,
          ),
        ),
      ),
    );
  }
}

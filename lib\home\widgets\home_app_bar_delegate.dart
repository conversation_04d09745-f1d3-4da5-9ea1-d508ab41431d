import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/title_with_logo.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

class HomeAppBarDelegate extends SliverPersistentHeaderDelegate {
  HomeAppBarDelegate();

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return AppBar(
      primary: true,
      title: SizedBox(width: double.infinity, child: TitleWithLogo()),
      actions: [
        Padding(
          padding: const EdgeInsets.only(
            right: ScreenStyleConfig.horizontalPadding,
          ),
          child: ProfileAvatar(size: 32.0, toProfileScreen: true),
        ),
      ],
      bottom: PreferredSize(
        preferredSize: Size.fromHeight(0.8),
        child: Container(height: 0.8, color: context.colors.borderColor),
      ),
    );
  }

  @override
  double get maxExtent => kToolbarHeight;

  @override
  double get minExtent => kToolbarHeight;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}

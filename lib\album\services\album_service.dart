// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/album/http_responses/album_list_response.dart';
import 'package:portraitmode/album/http_responses/album_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class AlbumService extends BaseService {
  Future<AlbumListResponse> add(String albumName) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/album/add',
        data: {'album_name': albumName},
      );

      return AlbumListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AlbumListResponse.fromMap(e.response?.data);
      }

      return AlbumListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return AlbumListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<AlbumResponse> edit({
    required String albumSlug,
    required String albumName,
  }) async {
    try {
      final response = await http.put(
        '${URL.baseApiUrl}/album/edit',
        data: {'album_slug': albumSlug, 'album_name': albumName},
      );

      return AlbumResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AlbumResponse.fromMap(e.response?.data);
      }

      return AlbumResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return AlbumResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<AlbumListResponse> delete(String albumSlug) async {
    try {
      final response = await http.delete('${URL.baseApiUrl}/album/$albumSlug');

      return AlbumListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AlbumListResponse.fromMap(e.response?.data);
      }

      return AlbumListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return AlbumListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class PmFieldLabel extends StatelessWidget {
  final String labelText;
  final Color? labelColor;

  const PmFieldLabel({super.key, this.labelText = '', this.labelColor});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: Text(
        labelText,
        style: TextStyle(color: labelColor ?? context.colors.brandColorAlt),
      ),
    );
  }
}

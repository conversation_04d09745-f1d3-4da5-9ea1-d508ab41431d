import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/photo/dto/mixed_feeed_sub_arg_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';

class PhotoListService extends BaseService {
  Future<PhotoListResponse> fetch({
    int limit = LoadMoreConfig.photosPerPage,
    int lastId = 0,
    bool featured = false,
    int? artistId,
    String? albumSlug,
    int? categoryId,
    String? cameraName,
  }) async {
    String url =
        '${URL.baseApiUrl}/${featured ? 'featured-photos' : 'photos'}/${limit.toString()}/${lastId.toString()}';

    if (artistId != null) {
      url =
          '${URL.baseApiUrl}/artist/${artistId.toString()}/album-photos/${albumSlug ?? 'all-photos'}/${limit.toString()}/${lastId.toString()}';
    } else if (categoryId != null) {
      url =
          '${URL.baseApiUrl}/category/${categoryId.toString()}/photos/${limit.toString()}/${lastId.toString()}';
    } else if (cameraName != null) {
      url =
          '${URL.baseApiUrl}/camera/$cameraName/photos/${limit.toString()}/${lastId.toString()}';
    }

    try {
      final response = await http.get(url);

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> search({
    String keyword = '',
    int limit = LoadMoreConfig.photosPerPage,
    int lastId = 0,
    bool featured = false,
  }) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/${featured ? 'search-featured-photos' : 'search-photos'}/$keyword/${limit.toString()}/${lastId.toString()}',
      );

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> fetchTrending({int limit = 24}) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/trending-photos/$limit',
      );

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> fetchFromFollowedArtists({
    int limit = LoadMoreConfig.photosPerPage,
    int? lastId,
    List<int> viewedPhotoIds = const [],
  }) async {
    final String viewedPhotoIdsStr = viewedPhotoIds.isNotEmpty
        ? viewedPhotoIds.join(',')
        : '';

    try {
      final response = await http.post(
        '${URL.baseApiUrl}/followed-artists-photos/$limit/${lastId ?? -1}',
        data: {'viewed_photo_ids': viewedPhotoIdsStr},
      );

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> fetchRecent({
    int limit = LoadMoreConfig.photosPerPage,
    int? lastId,
    List<int> viewedPhotoIds = const [],
  }) async {
    final String viewedPhotoIdsStr = viewedPhotoIds.isNotEmpty
        ? viewedPhotoIds.join(',')
        : '';

    try {
      final response = viewedPhotoIds.isNotEmpty
          ? await http.post(
              '${URL.baseApiUrl}/photos/$limit/${lastId ?? -1}',
              data: {'viewed_photo_ids': viewedPhotoIdsStr},
            )
          : await http.get('${URL.baseApiUrl}/photos/$limit/${lastId ?? -1}');

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      log('DioException: $e');

      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<PhotoListResponse> fetchMixed({
    required MixedFeedSubArgData latestArg,
    required MixedFeedSubArgData fromFollowedArtistsArg,
    List<int> viewedPhotoIds = const [],
  }) async {
    String url =
        '${URL.baseApiUrl}/mixed-photos/${latestArg.limit}/${latestArg.lastId ?? -1}/${fromFollowedArtistsArg.limit}/${fromFollowedArtistsArg.lastId ?? -1}';

    final String viewedPhotoIdsStr = viewedPhotoIds.isNotEmpty
        ? viewedPhotoIds.join(',')
        : '';

    try {
      final response = await http.post(
        url,
        data: {'viewed_photo_ids': viewedPhotoIdsStr},
      );

      return PhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return PhotoListResponse.fromMap(e.response?.data);
      }

      return PhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return PhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

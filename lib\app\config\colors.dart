// colors.dart - Enhanced for efficient theme switching
import 'package:flutter/material.dart';

class AppColors {
  final bool isDark;

  const AppColors._(this.isDark);

  factory AppColors.fromBrightness(Brightness brightness) {
    return AppColors._(brightness == Brightness.dark);
  }

  Color get baseColor =>
      isDark ? const Color(0xFF121419) : const Color(0xFFDEDEE5);
  Color get baseColorAlt =>
      isDark ? const Color(0xFF1D2025) : const Color(0xFFF5F5F7);
  Color get accentColor => const Color(0xFF2B56FD);
  Color get brandColor => isDark ? Colors.white : Colors.black;
  Color get brandColorAlt =>
      isDark ? const Color(0xFFAEAFB0) : const Color(0xFF41464C);
  Color get contentLighterColor =>
      isDark ? const Color(0xFFD9D9D9) : const Color(0xFF262626);

  Color get darkerGreyColor =>
      isDark ? const Color(0xFF7F7F7F) : const Color(0xFF808080);
  Color get greyColor => const Color(0xFFD5D5DE);
  Color get lightColor =>
      isDark ? const Color(0xFF080808) : const Color(0xFFF7F7F7);

  Color get dangerColor => const Color(0xFFFF6347);
  Color get warningColor => const Color(0xFFFFA500);
  Color get successColor => const Color(0xFF28A745);

  Color get scaffoldColor => isDark ? const Color(0xFF121419) : Colors.white;
  Color get dividerColor =>
      isDark ? const Color(0xFF1D2025) : const Color(0xFFEEEEEE);
  Color get borderColor =>
      isDark ? const Color(0xFF1D2025) : const Color(0xFFDEDEE5);
  Color get timeColor =>
      isDark ? const Color(0xFFB2B2B2) : const Color(0xFF4D4D4D);
  Color get iconColor =>
      isDark ? const Color(0xFFD5D5DE) : const Color(0xFF262626);
  Color get labelColor => isDark ? Colors.white : const Color(0xFF41464C);

  MaterialColor get primarySwatch => isDark
      ? const MaterialColor(0xFFAEAFB0, {
          50: Color(0xFFF5F5F6),
          100: Color(0xFFE7E7E7),
          200: Color(0xFFD7D7D8),
          300: Color(0xFFC6C7C8),
          400: Color(0xFFBABBBC),
          500: Color(0xFFAEAFB0),
          600: Color(0xFFA7A8A9),
          700: Color(0xFF9D9FA0),
          800: Color(0xFF949697),
          900: Color(0xFF848687),
        })
      : const MaterialColor(0xFF41464C, {
          50: Color(0xFFEAEAEB),
          100: Color(0xFFCACBCE),
          200: Color(0xFFA6A9AD),
          300: Color(0xFF82878C),
          400: Color(0xFF686D73),
          500: Color(0xFF41464C),
          600: Color(0xFF464C52),
          700: Color(0xFF3D4248),
          800: Color(0xFF34393F),
          900: Color(0xFF25292E),
        });
}

// Cached singleton approach for maximum performance
class AppColorsCache {
  static AppColors? _lightColors;
  static AppColors? _darkColors;

  static AppColors light() {
    return _lightColors ??= AppColors.fromBrightness(Brightness.light);
  }

  static AppColors dark() {
    return _darkColors ??= AppColors.fromBrightness(Brightness.dark);
  }

  // This DOES respond to theme switches - it checks current brightness each time
  static AppColors of(BuildContext context) {
    final brightness = Theme.of(context).brightness;
    return brightness == Brightness.dark ? dark() : light();
  }
}

// Enhanced extension for clean syntax and efficient rebuilds
extension AppColorsExtension on BuildContext {
  AppColors get colors => AppColorsCache.of(this);
  bool get isDarkMode => Theme.of(this).brightness == Brightness.dark;
  bool get isLightMode => Theme.of(this).brightness == Brightness.light;
}

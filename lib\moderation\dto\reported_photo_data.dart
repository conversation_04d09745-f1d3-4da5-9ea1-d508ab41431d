import 'package:portraitmode/moderation/dto/reported_photo_reporter_data.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class ReportedPhotoData {
  final int id;
  final String postName;
  final String date;
  final String description;
  final String attachmentUrl;
  final int authorId;
  final String authorNicename;
  final String authorDisplayName;
  final String authorProfileUrl;
  final String authorAvatarUrl;
  final String authorMembershipType;
  final bool potd;
  final bool featured;
  final String address;
  final String album;
  final List<int> categories;
  final String imageTitle;
  final String aperture;
  final String shutterSpeed;
  final String iso;
  final String camera;
  final String focalLength;
  final bool editableCamera;
  final bool editableFocalLength;
  final String url;
  final int width;
  final int height;
  final String urlLarge;
  final int totalLikes;
  final int totalComments;
  final bool isLiked;
  final int adId;
  final List<ReportedPhotoReporterData> reporters;

  ReportedPhotoData({
    this.id = 0,
    this.postName = "",
    this.date = "",
    this.description = "",
    this.attachmentUrl = "",
    this.authorId = 0,
    this.authorNicename = "",
    this.authorDisplayName = "",
    this.authorProfileUrl = "",
    this.authorAvatarUrl = "",
    this.authorMembershipType = "",
    this.potd = false,
    this.featured = false,
    this.address = "",
    this.album = "",
    this.categories = const [],
    this.imageTitle = "",
    this.aperture = "",
    this.shutterSpeed = "",
    this.iso = "",
    this.camera = "",
    this.focalLength = "",
    this.editableCamera = false,
    this.editableFocalLength = false,
    this.url = "",
    this.width = 0,
    this.height = 0,
    this.urlLarge = '',
    this.totalLikes = 0,
    this.totalComments = 0,
    this.isLiked = false,
    this.adId = 0,
    this.reporters = const [],
  });

  ReportedPhotoData copyWith({
    int? id,
    String? postName,
    String? date,
    String? description,
    String? attachmentUrl,
    int? authorId,
    String? authorNicename,
    String? authorDisplayName,
    String? authorProfileUrl,
    String? authorAvatarUrl,
    String? authorMembershipType,
    bool? potd,
    bool? featured,
    String? address,
    String? album,
    List<int>? categories,
    String? imageTitle,
    String? aperture,
    String? shutterSpeed,
    String? iso,
    String? camera,
    String? focalLength,
    bool? editableCamera,
    bool? editableFocalLength,
    String? url,
    int? width,
    int? height,
    String? urlLarge,
    int? totalLikes,
    int? totalComments,
    bool? isLiked,
    int? adId,
    List<ReportedPhotoReporterData>? reporters,
  }) {
    return ReportedPhotoData(
      id: id ?? this.id,
      postName: postName ?? this.postName,
      date: date ?? this.date,
      description: description ?? this.description,
      attachmentUrl: attachmentUrl ?? this.attachmentUrl,
      authorId: authorId ?? this.authorId,
      authorNicename: authorNicename ?? this.authorNicename,
      authorDisplayName: authorDisplayName ?? this.authorDisplayName,
      authorProfileUrl: authorProfileUrl ?? this.authorProfileUrl,
      authorAvatarUrl: authorAvatarUrl ?? this.authorAvatarUrl,
      authorMembershipType: authorMembershipType ?? this.authorMembershipType,
      potd: potd ?? this.potd,
      featured: featured ?? this.featured,
      address: address ?? this.address,
      album: album ?? this.album,
      categories: categories ?? this.categories,
      imageTitle: imageTitle ?? this.imageTitle,
      aperture: aperture ?? this.aperture,
      shutterSpeed: shutterSpeed ?? this.shutterSpeed,
      iso: iso ?? this.iso,
      camera: camera ?? this.camera,
      focalLength: focalLength ?? this.focalLength,
      editableCamera: editableCamera ?? this.editableCamera,
      editableFocalLength: editableFocalLength ?? this.editableFocalLength,
      url: url ?? this.url,
      width: width ?? this.width,
      height: height ?? this.height,
      urlLarge: urlLarge ?? this.urlLarge,
      totalLikes: totalLikes ?? this.totalLikes,
      totalComments: totalComments ?? this.totalComments,
      isLiked: isLiked ?? this.isLiked,
      adId: adId ?? this.adId,
      reporters: reporters ?? this.reporters,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'postName': postName,
      'date': date,
      'description': description,
      'attachmentUrl': attachmentUrl,
      'authorId': authorId,
      'authorNicename': authorNicename,
      'authorDisplayName': authorDisplayName,
      'authorProfileUrl': authorProfileUrl,
      'authorAvatarUrl': authorAvatarUrl,
      'authorMembershipType': authorMembershipType,
      'potd': potd,
      'featured': featured,
      'address': address,
      'album': album,
      'categories': categories,
      'imageTitle': imageTitle,
      'aperture': aperture,
      'shutterSpeed': shutterSpeed,
      'iso': iso,
      'camera': camera,
      'focalLength': focalLength,
      'editableCamera': editableCamera,
      'editableFocalLength': editableFocalLength,
      'url': url,
      'width': width,
      'height': height,
      'urlLarge': urlLarge,
      'totalLikes': totalLikes,
      'totalComments': totalComments,
      'isLiked': isLiked,
      'adId': adId,
      'reporters': reporters
          .map((ReportedPhotoReporterData x) => x.toMap())
          .toList(),
    };
  }

  factory ReportedPhotoData.fromMap(Map<String, dynamic> data) {
    return ReportedPhotoData(
      id: data['id'],
      postName: data['postName'],
      date: data['date'],
      description: data['description'],
      attachmentUrl: data['attachmentUrl'],
      authorId: data['authorId'],
      authorNicename: data['authorNicename'],
      authorDisplayName: data['authorDisplayName'],
      authorProfileUrl: data['authorProfileUrl'],
      authorAvatarUrl: data['authorAvatarUrl'],
      authorMembershipType: data['authorMembershipType'],
      potd: data['potd'],
      featured: data['featured'],
      address: data['address'],
      album: data['album'],
      categories: List<int>.from(data['categories']),
      imageTitle: data['imageTitle'],
      aperture: data['aperture'],
      shutterSpeed: data['shutterSpeed'],
      iso: data['iso'],
      camera: data['camera'],
      focalLength: data['focalLength'],
      editableCamera: data['editableCamera'],
      editableFocalLength: data['editableFocalLength'],
      url: data['url'],
      width: data['width'],
      height: data['height'],
      urlLarge: data['urlLarge'],
      totalLikes: data['totalLikes'],
      totalComments: data['totalComments'],
      isLiked: data['isLiked'],
      adId: data['adId'],
      reporters:
          data['reporters'] != null &&
              data['reporters'] is List &&
              data['reporters'].isNotEmpty
          ? data['reporters']
                .map<ReportedPhotoReporterData>(
                  (dynamic x) => ReportedPhotoReporterData.fromMap(x),
                )
                .toList()
          : [],
    );
  }

  PhotoData toPhotoData() {
    return PhotoData(
      id: id,
      postName: postName,
      date: date,
      description: description,
      attachmentUrl: attachmentUrl,
      authorId: authorId,
      authorNicename: authorNicename,
      authorDisplayName: authorDisplayName,
      authorProfileUrl: authorProfileUrl,
      authorAvatarUrl: authorAvatarUrl,
      authorMembershipType: authorMembershipType,
      potd: potd,
      featured: featured,
      address: address,
      album: album,
      categories: categories,
      imageTitle: imageTitle,
      aperture: aperture,
      shutterSpeed: shutterSpeed,
      iso: iso,
      camera: camera,
      focalLength: focalLength,
      editableCamera: editableCamera,
      editableFocalLength: editableFocalLength,
      url: url,
      width: width,
      height: height,
      urlLarge: urlLarge,
      totalLikes: totalLikes,
      totalComments: totalComments,
      isLiked: isLiked,
      adId: adId,
    );
  }
}

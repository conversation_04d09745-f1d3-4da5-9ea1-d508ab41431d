class LikePhotoResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final LikePhotoResponseData? data;

  LikePhotoResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory LikePhotoResponse.fromMap(Map<String, dynamic> map) {
    return LikePhotoResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? LikePhotoResponseData.fromMap(map['data'])
          : null,
    );
  }
}

class LikePhotoResponseData {
  final int totalLikes;

  LikePhotoResponseData({this.totalLikes = 0});

  factory LikePhotoResponseData.fromMap(Map<String, dynamic> map) {
    return LikePhotoResponseData(totalLikes: map['totalLikes'] ?? 0);
  }
}

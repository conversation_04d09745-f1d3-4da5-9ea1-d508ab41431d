import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class SettingsScreenListTile extends StatelessWidget {
  final String title;
  final IconData icon;
  final bool isDanger;
  final Function? onTap;

  const SettingsScreenListTile({
    super.key,
    required this.title,
    required this.icon,
    this.isDanger = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      dense: true,
      visualDensity: const VisualDensity(horizontal: 0.0, vertical: -2.0),
      horizontalTitleGap: 1.0,
      minLeadingWidth: 28.0,
      minVerticalPadding: 0.0,
      title: Text(
        title,
        style: TextStyle(
          fontSize: 16.0,
          color: isDanger ? context.colors.dangerColor : null,
        ),
      ),
      leading: Icon(
        icon,
        size: 22.0,
        color: isDanger
            ? context.colors.dangerColor
            : context.colors.brandColor,
      ),
      onTap: () {
        if (onTap == null) return;
        onTap!();
      },
    );
  }
}

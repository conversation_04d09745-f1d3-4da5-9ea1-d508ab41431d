import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/app/utils/device_util.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/hive/services/local_auth_service.dart';

class AuthUtil {
  Future<DioException?> refreshAccessToken() async {
    // Create a new Dio instance to make the request to the token endpoint
    // This instance won't be affected by the interceptor.
    Dio dio = Dio();

    // To refresh accessToken, the url is using URL.baseTokenUrl not URL.refreshTokenUrl
    // The URL.refreshTokenUrl is used to refresh the refreshToken.
    String url = URL.baseTokenUrl;

    // log('we are refreshing the access token to: $url');

    final String refreshToken = LocalAuthService.refreshToken ?? '';
    final deviceModel = await getDeviceModelSlug();

    try {
      // Make request to the url above.
      // The request should send a `refresh_token` cookie.
      final Response<dynamic> response = await dio.post(
        url,
        data: {'device': deviceModel},
        options: Options(headers: {'Cookie': 'refresh_token=$refreshToken'}),
      );

      // log('The refreshAccessToken request was successful');

      // If the request was successful, parse the JSON response
      if (response.data != null && response.data is Map) {
        String accessToken = response.data['data']['accessToken'];

        await LocalAuthService.setAccessToken(accessToken);

        // log("And the accessToken is updated in Hive object");
      } else {
        // log("But the accessToken is not updated in Hive object because our checking is failed");
      }

      return null;
    } on DioException catch (e) {
      await LocalAuthService.destroy();

      if (BaseService().errorHasData(e)) {
        // log('Error when refreshing accessToken. It falls into DioException and the error data is: ${e.response?.data}');
        return e;
      }

      // log("Error when refreshing accessToken. It falls into DioException and doesn't have error data. But the response is: ${e.response}");
      return e;
    }
  }

  String readRefreshTokenCookie(Response response) {
    Headers headers = response.headers;
    List<String>? rawCookies = headers['set-cookie'];
    String refreshToken = '';

    if (rawCookies != null) {
      // Get the the refresh_token value from rawCookies
      String rawRefreshToken = rawCookies.firstWhere(
        (cookie) => cookie.contains('refresh_token'),
        orElse: () => '',
      );

      // log('rawRefreshToken: $rawRefreshToken');

      // If the refresh_token value is not empty, then parse it.
      if (rawRefreshToken.isNotEmpty) {
        // Split the refresh_token value into a list of strings.
        List<String> splitRawRefreshToken = rawRefreshToken.split(';');

        // Get the refresh_token value from the list of strings.
        refreshToken = splitRawRefreshToken.firstWhere(
          (cookie) => cookie.contains('refresh_token'),
          orElse: () => '',
        );

        // Remove the refresh_token= from the refresh_token value.
        refreshToken = refreshToken.replaceAll('refresh_token=', '');
      }
    }

    if (refreshToken.isEmpty) {
      return '';
    }

    // log('refreshToken: $refreshToken');

    return refreshToken;
  }

  bool errorCodeRequiresLogin(String errorCode) {
    return errorCode == 'jwt_auth_no_auth_header' ||
        errorCode == 'jwt_auth_bad_auth_header' ||
        errorCode == 'jwt_auth_obsolete_token' ||
        errorCode == 'jwt_auth_invalid_refresh_token';
  }
}

AuthUtil authUtil = AuthUtil();

import 'package:portraitmode/app/dto/initial_data.dart';

class InitialDataResponse {
  final bool success;
  final String errorCode;
  final String message;
  final InitialData? data;

  InitialDataResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory InitialDataResponse.fromMap(Map<String, dynamic> map) {
    return InitialDataResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? InitialData.fromMap(map['data'])
          : null,
    );
  }
}

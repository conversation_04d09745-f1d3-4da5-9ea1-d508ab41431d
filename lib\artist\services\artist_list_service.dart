// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class ArtistListService extends BaseService {
  Future<ArtistListResponse> fetch({
    int limit = 15,
    int? lastId,
    int? lastTotalPhotos,
    List<int>? excludeIds,
    String? orderBy,
  }) async {
    lastId ??= -1;
    lastTotalPhotos ??= -1;

    String url = '${URL.baseApiUrl}/artists';

    if (excludeIds != null && excludeIds.isNotEmpty) {
      url += '/exclude-ids/${excludeIds.join(',')}';

      orderBy ??= 'recently_uploaded';
    }

    if (orderBy != null) {
      url += '/order-by/$orderBy';
    }

    url +=
        '/${limit.toString()}/${lastId.toString()}/${lastTotalPhotos.toString()}';

    try {
      final response = await http.get(url);

      return ArtistListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistListResponse.fromMap(e.response?.data);
      }

      return ArtistListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ArtistListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<ArtistListResponse> search({
    String keyword = '',
    int limit = 15,
    int? lastId,
    int? lastTotalPhotos,
  }) async {
    lastId ??= -1;
    lastTotalPhotos ??= -1;

    try {
      final response = await http.get(
        '${URL.baseApiUrl}/search-artists/$keyword/${limit.toString()}/${lastId.toString()}/${lastTotalPhotos.toString()}',
      );

      return ArtistListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistListResponse.fromMap(e.response?.data);
      }

      return ArtistListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ArtistListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<ArtistListResponse> fetchBlocked({
    int limit = 15,
    int? lastId,
    int? lastTotalPhotos,
    String? orderBy,
  }) async {
    lastId ??= -1;
    lastTotalPhotos ??= -1;

    String url = '${URL.baseApiUrl}/blocked-artists';

    if (orderBy != null) {
      url += '/order-by/$orderBy';
    }

    url +=
        '/${limit.toString()}/${lastId.toString()}/${lastTotalPhotos.toString()}';

    try {
      final response = await http.get(url);

      return ArtistListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ArtistListResponse.fromMap(e.response?.data);
      }

      return ArtistListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ArtistListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

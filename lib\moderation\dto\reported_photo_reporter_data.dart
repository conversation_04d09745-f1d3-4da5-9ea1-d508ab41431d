class ReportedPhotoReporterData {
  final int id;
  final String nicename;
  final String displayName;
  final String avatarUrl;
  final String profileUrl;
  final String reason;
  final bool isCurator;

  ReportedPhotoReporterData({
    this.id = 0,
    this.nicename = "",
    this.displayName = "",
    this.avatarUrl = "",
    this.profileUrl = "",
    this.reason = "",
    this.isCurator = false,
  });

  ReportedPhotoReporterData copyWith({
    int? id,
    String? nicename,
    String? displayName,
    String? avatarUrl,
    String? profileUrl,
    String? reason,
    bool? isCurator,
  }) {
    return ReportedPhotoReporterData(
      id: id ?? this.id,
      nicename: nicename ?? this.nicename,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      profileUrl: profileUrl ?? this.profileUrl,
      reason: reason ?? this.reason,
      isCurator: isCurator ?? this.isCurator,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nicename': nicename,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'profileUrl': profileUrl,
      'reason': reason,
      'isCurator': isCurator,
    };
  }

  factory ReportedPhotoReporterData.fromMap(Map<String, dynamic> data) {
    return ReportedPhotoReporterData(
      id: data['id'] ?? 0,
      nicename: data['nicename'] ?? "",
      displayName: data['displayName'] ?? "",
      avatarUrl: data['avatarUrl'] ?? "",
      profileUrl: data['profileUrl'] ?? "",
      reason: data['reason'] ?? "",
      isCurator: data['isCurator'] ?? false,
    );
  }
}

import 'package:portraitmode/comment/dto/comment_data.dart';

class CommentListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<CommentData> data;

  CommentListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory CommentListResponse.fromMap(Map<String, dynamic> map) {
    return CommentListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<CommentData>((data) => CommentData.fromMap(data))
                .toList()
          : [],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/photo/widgets/photo_detail/like_button.dart';

class PhotoMetadataGroup extends StatelessWidget {
  final double containerWidth;
  final PhotoData photo;

  const PhotoMetadataGroup({
    super.key,
    required this.containerWidth,
    required this.photo,
  });

  @override
  Widget build(BuildContext context) {
    // log('  build PhotoMetadataGroup\n    photo id: ${photo.id.toString()}');

    const double hPadding = ScreenStyleConfig.horizontalPadding * 2;
    const double vPadding = hPadding + 4.0;

    double innerScreenWidth = containerWidth - (hPadding * 2);
    double artistColWidth = (innerScreenWidth / 100) * 36;
    double locationColWidth = (innerScreenWidth / 100) * 36;
    double likeColWidth = (innerScreenWidth / 100) * 28;

    DateFormat dateFormat = DateFormat('MMM d, yyyy');
    DateTime uploadDate = DateTime.parse(photo.date);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: artistColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Artist", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  Row(
                    children: [
                      Avatar(
                        imageUrl: photo.authorAvatarUrl,
                        size: 34.0,
                        useBorder: true,
                        onTap: () {
                          _gotoArtistDetailScreen(context, photo);
                        },
                      ),
                      const SizedBox(width: 7.0),
                      Flexible(
                        child: InkWell(
                          onTap: () {
                            _gotoArtistDetailScreen(context, photo);
                          },
                          child: Text(
                            photo.authorDisplayName,
                            style: const TextStyle(fontSize: 13.0),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              width: locationColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Location", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  Text(photo.address, style: const TextStyle(fontSize: 13.0)),
                ],
              ),
            ),
            SizedBox(
              width: likeColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Like", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  SizedBox(
                    height: 30.0,
                    child: LikeButton(
                      photoId: photo.id,
                      isLiked: photo.isLiked,
                      totalLikes: photo.totalLikes,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: vPadding),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: artistColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Uploaded", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  Text(
                    dateFormat.format(uploadDate),
                    style: const TextStyle(fontSize: 13.0),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: locationColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Camera", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  Text(
                    (photo.camera.isNotEmpty ? photo.camera : '-'),
                    style: const TextStyle(fontSize: 13.0),
                  ),
                ],
              ),
            ),
            SizedBox(
              width: likeColWidth,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Focal length", style: TextStyle(fontSize: 15.0)),
                  const SizedBox(height: 5.0),
                  Text(
                    (photo.focalLength.isNotEmpty && photo.focalLength != '0')
                        ? '${photo.focalLength}mm'
                        : "-",
                    style: const TextStyle(fontSize: 13.0),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  void _gotoArtistDetailScreen(BuildContext context, PhotoData photo) {
    final bool isOwnProfile = LocalUserService.userId == photo.authorId;

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          isOwnProfile: isOwnProfile,
          partialData: ArtistPartialData(
            id: photo.authorId,
            nicename: photo.authorNicename,
            displayName: photo.authorDisplayName,
            profileUrl: photo.authorProfileUrl,
            avatarUrl: photo.authorAvatarUrl,
            membershipType: photo.authorMembershipType,
          ),
        ),
      ),
    );
  }
}

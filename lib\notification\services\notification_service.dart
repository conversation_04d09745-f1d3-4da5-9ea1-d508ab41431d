import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';

class NotificationService extends BaseService {
  NotificationService() : super();

  Future<BaseResponse> read(List<int> ids) async {
    String idsString = ids.join(',');

    try {
      final response = await http.put(
        '$apiUrl/notifications/read',
        data: {'ids': idsString},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

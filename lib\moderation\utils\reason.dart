Map<String, String> getPhotoReportingReasons() {
  return {
    'not_street': 'Not street',
    'duplicate': 'Duplicate',
    'category_misuse': 'Category misuse',
    'resolution': 'Too small',
    'over_edited': 'Over edited',
    'has_watermark': 'Watermark, logo, text, frame or border',
    'is_collage': 'Montage',
    'nudity': 'Nudity',
    'violence': 'Violence',
    'other': 'Other',
  };
}

String getPhotoReportingReason(String reason) {
  return getPhotoReportingReasons()[reason] ?? reason;
}

Map<String, String> getModerationDeletionReasons() {
  return {
    'not_street': 'Not street',
    'duplicate': 'Duplicate',
    'category_misuse': 'Category misuse',
    'missing_focus': 'Missing focus',
    'resolution': 'Too small',
    'over_edited': 'Over edited',
    'has_watermark': 'Watermark, logo, text, frame or border',
    'is_collage': 'Montage',
    'not_original': 'Not original author',
    'fake_name': 'Fake name',
    'nudity': 'Nudity',
    'violence': 'Violence',
    'technical': 'Technical issue',
  };
}

String getModerationDeletionReason(String reason) {
  return getModerationDeletionReasons()[reason] ?? reason;
}

Map<String, String> getCommentReportingReasons() {
  return {
    'spam': 'Spam',
    'hate_speech': 'Hate speech',
    'harassment': 'Harassment',
    'other': 'Other',
  };
}

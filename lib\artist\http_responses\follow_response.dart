class FollowResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final FollowResponseData? data;

  FollowResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory FollowResponse.fromMap(Map<String, dynamic> map) {
    return FollowResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? FollowResponseData.fromMap(map['data'])
          : null,
    );
  }
}

class FollowResponseData {
  FollowResponseData({
    this.artistTotalFollowing = 0,
    this.artistTotalFollowers = 0,
    this.followerTotalFollowing = 0,
    this.followerTotalFollowers = 0,
  });

  final int artistTotalFollowing;
  final int artistTotalFollowers;
  final int followerTotalFollowing;
  final int followerTotalFollowers;

  factory FollowResponseData.fromMap(Map<String, dynamic> map) {
    return FollowResponseData(
      artistTotalFollowing: map['artistTotalFollowing'] ?? 0,
      artistTotalFollowers: map['artistTotalFollowers'] ?? 0,
      followerTotalFollowing: map['followerTotalFollowing'] ?? 0,
      followerTotalFollowers: map['followerTotalFollowers'] ?? 0,
    );
  }
}

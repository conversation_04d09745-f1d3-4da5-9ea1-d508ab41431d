import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

final class ArchivedPhotoIdsNotifier extends IdListNotifier {}

final archivedPhotoIdsProvider =
    NotifierProvider.autoDispose<ArchivedPhotoIdsNotifier, List<int>>(
      ArchivedPhotoIdsNotifier.new,
    );

final archivedPhotosProvider = Provider.autoDispose<List<PhotoData>>((ref) {
  final ids = ref.watch(archivedPhotoIdsProvider);
  final store = ref.watch(photoStoreProvider);
  final items = <PhotoData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

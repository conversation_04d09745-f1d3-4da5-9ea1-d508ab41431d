// sliver_grid_load_more.dart

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:portraitmode/load_more/enum.dart';

typedef LoadMoreCallback = Future<LoadMoreResult> Function();

enum SliverGridLoadMoreStatusState { idle, loading, failed, finished }

class SliverGridLoadMoreStatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';

  static String getText(SliverGridLoadMoreStatusState state) {
    switch (state) {
      case SliverGridLoadMoreStatusState.loading:
        return loading;
      case SliverGridLoadMoreStatusState.failed:
        return failed;
      case SliverGridLoadMoreStatusState.finished:
        return finished;
      default:
        return idle;
    }
  }
}

class SliverGridLoadMoreDefaults {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int scrollThreshold = 200;
  static const int debounceMilliseconds = 500;
  static const int maxRetries = 3;
  static const int minScrollThreshold = 50;
  static const int maxScrollThreshold = 1000;
  static const int minDebounceMs = 100;
  static const int maxDebounceMs = 3000;
}

class SliverGridLoadMore extends StatefulWidget {
  final GlobalKey? sliverKey;

  /// Optional external scroll controller.
  ///
  /// If supplied, you must not dispose it manually while the widget is active.
  /// Let SliverGridLoadMore manage the listener lifecycle.
  final ScrollController? scrollController;

  final double loadingWidgetContainerHeight;
  final double loadingWidgetSize;
  final double loadingWidgetStrokeWidth;
  final Color loadingWidgetColor;
  final int scrollThreshold;
  final int debounceMilliseconds;
  final int maxRetries;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final bool isFinished;
  final bool runOnEmptyResult;
  final bool showLoadMoreIndicator;
  final bool enableAutoLoadMore;
  final LoadMoreCallback onLoadMore;
  final double verticalPaddingValue;
  final double horizontalPaddingValue;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final double childAspectRatio;
  final List<Widget> children;

  const SliverGridLoadMore({
    super.key,
    this.sliverKey,
    this.scrollController,
    this.loadingWidgetContainerHeight =
        SliverGridLoadMoreDefaults.containerHeight,
    this.loadingWidgetSize = SliverGridLoadMoreDefaults.size,
    this.loadingWidgetStrokeWidth = SliverGridLoadMoreDefaults.strokeWidth,
    this.loadingWidgetColor = SliverGridLoadMoreDefaults.color,
    this.scrollThreshold = SliverGridLoadMoreDefaults.scrollThreshold,
    this.debounceMilliseconds = SliverGridLoadMoreDefaults.debounceMilliseconds,
    this.maxRetries = SliverGridLoadMoreDefaults.maxRetries,
    this.idleStatusText = SliverGridLoadMoreStatusText.idle,
    this.loadingStatusText = SliverGridLoadMoreStatusText.loading,
    this.failedStatusText = SliverGridLoadMoreStatusText.failed,
    this.finishedStatusText = SliverGridLoadMoreStatusText.finished,
    this.isFinished = false,
    this.runOnEmptyResult = false,
    this.showLoadMoreIndicator = true,
    this.enableAutoLoadMore = true,
    required this.onLoadMore,
    this.verticalPaddingValue = 0.0,
    this.horizontalPaddingValue = 0.0,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    this.childAspectRatio = 1.0,
    required this.children,
  }) : assert(crossAxisCount > 0, 'crossAxisCount must be positive'),
       assert(children.length >= 0, 'children must be non-empty list'),
       assert(
         loadingWidgetContainerHeight > 0,
         'loadingWidgetContainerHeight must be positive',
       ),
       assert(loadingWidgetSize > 0, 'loadingWidgetSize must be positive'),
       assert(
         loadingWidgetStrokeWidth > 0,
         'loadingWidgetStrokeWidth must be positive',
       ),
       assert(
         scrollThreshold >= SliverGridLoadMoreDefaults.minScrollThreshold &&
             scrollThreshold <= SliverGridLoadMoreDefaults.maxScrollThreshold,
         'scrollThreshold must be between ${SliverGridLoadMoreDefaults.minScrollThreshold} and ${SliverGridLoadMoreDefaults.maxScrollThreshold}',
       ),
       assert(
         debounceMilliseconds >= SliverGridLoadMoreDefaults.minDebounceMs &&
             debounceMilliseconds <= SliverGridLoadMoreDefaults.maxDebounceMs,
         'debounceMilliseconds must be between ${SliverGridLoadMoreDefaults.minDebounceMs} and ${SliverGridLoadMoreDefaults.maxDebounceMs}',
       ),
       assert(maxRetries >= 0, 'maxRetries must be non-negative'),
       assert(mainAxisSpacing >= 0, 'mainAxisSpacing must be non-negative'),
       assert(crossAxisSpacing >= 0, 'crossAxisSpacing must be non-negative'),
       assert(childAspectRatio > 0, 'childAspectRatio must be positive'),
       assert(
         verticalPaddingValue >= 0,
         'verticalPaddingValue must be non-negative',
       ),
       assert(
         horizontalPaddingValue >= 0,
         'horizontalPaddingValue must be non-negative',
       );

  @override
  SliverGridLoadMoreState createState() => SliverGridLoadMoreState();
}

class SliverGridLoadMoreState extends State<SliverGridLoadMore> {
  late ScrollController _activeScrollController;
  ScrollController? _internalScrollController;
  SliverGridLoadMoreStatusState _status = SliverGridLoadMoreStatusState.idle;
  Timer? _debounceTimer;
  bool _isDisposed = false;
  bool _isLoadingInProgress = false;
  bool _hasScrollListener = false;
  int _retryCount = 0;
  int _loadMoreCallCount = 0;

  // Track empty load more
  bool _hasTriggeredEmptyLoad = false;

  // Atomic timer operations
  final Object _timerLock = Object();

  bool get _shouldShowLoadMore =>
      widget.showLoadMoreIndicator && !_isFinishedState;

  bool get _isFinishedState =>
      widget.isFinished || _status == SliverGridLoadMoreStatusState.finished;

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    _scheduleEmptyLoadIfNeeded();
  }

  void _initializeScrollController() {
    if (widget.scrollController != null) {
      _activeScrollController = widget.scrollController!;
      _internalScrollController = null;
    } else {
      _internalScrollController = ScrollController();
      _activeScrollController = _internalScrollController!;
    }

    _updateScrollListener();
  }

  void _updateScrollListener() {
    final shouldHaveListener = widget.enableAutoLoadMore && !_isFinishedState;

    if (shouldHaveListener && !_hasScrollListener) {
      _addScrollListener();
    } else if (!shouldHaveListener && _hasScrollListener) {
      _removeScrollListener();
    }
  }

  void _addScrollListener() {
    if (!_hasScrollListener && _activeScrollController.hasClients) {
      try {
        _activeScrollController.addListener(_scrollListener);
        _hasScrollListener = true;
      } catch (e) {
        debugPrint('Warning: Could not add scroll listener: $e');
      }
    }
  }

  void _removeScrollListener() {
    if (_hasScrollListener) {
      try {
        _activeScrollController.removeListener(_scrollListener);
        _hasScrollListener = false;
      } catch (e) {
        debugPrint('Warning: Could not remove scroll listener: $e');
        // Don't set _hasScrollListener to false if removal failed
        // This prevents memory leaks by ensuring we don't lose track of attached listeners
      }
    }
  }

  void _scheduleEmptyLoadIfNeeded() {
    if (widget.children.isEmpty &&
        widget.runOnEmptyResult &&
        !_isFinishedState &&
        !_hasTriggeredEmptyLoad) {
      _hasTriggeredEmptyLoad = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposed && !_isLoadingInProgress) {
          _triggerLoadMore();
        }
      });
    }
  }

  @override
  void didUpdateWidget(SliverGridLoadMore oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle scroll controller changes
    _handleScrollControllerChange(oldWidget);

    // Handle auto load more changes
    if (oldWidget.enableAutoLoadMore != widget.enableAutoLoadMore) {
      _updateScrollListener();
    }

    // Update status if isFinished changed
    if (oldWidget.isFinished != widget.isFinished) {
      if (widget.isFinished) {
        _safeUpdateStatus(SliverGridLoadMoreStatusState.finished);
      }
      _updateScrollListener();
    }

    // Reset empty load trigger when children count changes from 0 to something else
    if (oldWidget.children.isEmpty && widget.children.isNotEmpty) {
      _hasTriggeredEmptyLoad = false;
    }

    // Reset retry count if maxRetries changed
    if (oldWidget.maxRetries != widget.maxRetries) {
      _retryCount = 0;
    }
  }

  void _handleScrollControllerChange(SliverGridLoadMore oldWidget) {
    if (oldWidget.scrollController != widget.scrollController) {
      _removeScrollListener();

      // Handle all scroll controller transition scenarios
      if (oldWidget.scrollController == null &&
          widget.scrollController != null) {
        // Internal -> External
        _internalScrollController?.dispose();
        _internalScrollController = null;
      } else if (oldWidget.scrollController != null &&
          widget.scrollController == null) {
        // External -> Internal (need to create new internal controller)
        _internalScrollController = ScrollController();
      }
      // External -> External: no disposal needed

      _initializeScrollController();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _cancelTimerSafely();
    _removeScrollListener();

    // Only dispose internal controller
    _internalScrollController?.dispose();
    _internalScrollController = null;

    super.dispose();
  }

  void _cancelTimerSafely() {
    synchronized(_timerLock, () {
      _debounceTimer?.cancel();
      _debounceTimer = null;
    });
  }

  void _scrollListener() {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    if (!_activeScrollController.hasClients) {
      return;
    }

    try {
      final position = _activeScrollController.position;
      if (!position.hasContentDimensions ||
          !position.hasPixels ||
          !position.hasViewportDimension) {
        return;
      }

      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      final threshold = widget.scrollThreshold.toDouble();

      if (maxScroll > 0 &&
          currentScroll >= (maxScroll - threshold) &&
          _status == SliverGridLoadMoreStatusState.idle) {
        _triggerLoadMore();
      }
    } catch (e) {
      debugPrint('Error in scroll listener: $e');
    }
  }

  void _triggerLoadMore() {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    // Prevent multiple simultaneous calls
    final currentCallCount = ++_loadMoreCallCount;

    _cancelTimerSafely();

    synchronized(_timerLock, () {
      if (_debounceTimer != null) {
        _debounceTimer!.cancel();
      }

      _debounceTimer = Timer(
        Duration(milliseconds: widget.debounceMilliseconds),
        () {
          // Verify this is still the latest call
          if (currentCallCount == _loadMoreCallCount &&
              !_isDisposed &&
              mounted &&
              !_isLoadingInProgress &&
              !_isFinishedState) {
            _performLoadMore();
          }
        },
      );
    });
  }

  Future<void> _performLoadMore() async {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    _isLoadingInProgress = true;
    _safeUpdateStatus(SliverGridLoadMoreStatusState.loading);

    try {
      final result = await widget.onLoadMore();

      if (!_isDisposed && mounted) {
        _isLoadingInProgress = false;

        switch (result) {
          case LoadMoreResult.success:
            _retryCount = 0; // Reset retry count on success
            _safeUpdateStatus(SliverGridLoadMoreStatusState.idle);
            break;
          case LoadMoreResult.finished:
            _safeUpdateStatus(SliverGridLoadMoreStatusState.finished);
            break;
          case LoadMoreResult.failed:
            _retryCount++;
            if (_retryCount >= widget.maxRetries) {
              _safeUpdateStatus(SliverGridLoadMoreStatusState.finished);
            } else {
              _safeUpdateStatus(SliverGridLoadMoreStatusState.failed);
            }
            break;
        }
      }
    } catch (e) {
      debugPrint('SliverGridLoadMore error: $e');

      if (!_isDisposed && mounted) {
        _isLoadingInProgress = false;
        _retryCount++;

        if (_retryCount >= widget.maxRetries) {
          _safeUpdateStatus(SliverGridLoadMoreStatusState.finished);
        } else {
          _safeUpdateStatus(SliverGridLoadMoreStatusState.failed);
        }
      }
    }
  }

  void _safeUpdateStatus(SliverGridLoadMoreStatusState newStatus) {
    if (_isDisposed || !mounted || _status == newStatus) {
      return;
    }

    setState(() {
      _status = newStatus;
    });
  }

  /// Public method to manually trigger load more
  void loadMore() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status != SliverGridLoadMoreStatusState.loading &&
        !_isFinishedState) {
      _triggerLoadMore();
    }
  }

  /// Public method to retry loading after failure
  void retry() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status == SliverGridLoadMoreStatusState.failed) {
      _triggerLoadMore();
    }
  }

  /// Public method to reset the load more state
  void reset() {
    if (!_isDisposed && !_isLoadingInProgress) {
      _hasTriggeredEmptyLoad = false;
      _retryCount = 0;
      _loadMoreCallCount = 0;
      _safeUpdateStatus(SliverGridLoadMoreStatusState.idle);
    }
  }

  /// Get current retry count
  int get retryCount => _retryCount;

  /// Get current status
  SliverGridLoadMoreStatusState get status => _status;

  /// Check if loading is in progress
  bool get isLoading => _isLoadingInProgress;

  @override
  Widget build(BuildContext context) {
    return _buildScrollView();
  }

  Widget _buildScrollView() {
    // Handle empty children case
    if (widget.children.isEmpty) {
      if (widget.runOnEmptyResult && widget.showLoadMoreIndicator) {
        return Padding(
          padding: EdgeInsets.symmetric(
            horizontal: widget.horizontalPaddingValue,
          ),
          child: CustomScrollView(
            controller: _activeScrollController,
            slivers: [
              SliverToBoxAdapter(
                child: SizedBox(height: widget.verticalPaddingValue),
              ),
              SliverToBoxAdapter(child: Center(child: _buildLoadMoreView())),
            ],
          ),
        );
      }
      return const SizedBox.shrink();
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: widget.horizontalPaddingValue),
      child: CustomScrollView(
        controller: _activeScrollController,
        slivers: [
          SliverToBoxAdapter(
            child: SizedBox(height: widget.verticalPaddingValue),
          ),
          SliverGrid(
            key: widget.sliverKey,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: widget.crossAxisCount,
              mainAxisSpacing: widget.mainAxisSpacing,
              crossAxisSpacing: widget.crossAxisSpacing,
              childAspectRatio: widget.childAspectRatio,
            ),
            delegate: SliverChildBuilderDelegate(
              (context, index) => widget.children[index],
              childCount: widget.children.length,
            ),
          ),
          if (_shouldShowLoadMore) _buildSliverLoadMoreView(),
        ],
      ),
    );
  }

  Widget _buildSliverLoadMoreView() {
    return SliverToBoxAdapter(child: _buildLoadMoreView());
  }

  Widget _buildLoadMoreView() {
    final currentStatus = _isFinishedState
        ? SliverGridLoadMoreStatusState.finished
        : _status;

    return SliverGridLoadMoreView(
      status: currentStatus,
      containerHeight: widget.loadingWidgetContainerHeight,
      size: widget.loadingWidgetSize,
      strokeWidth: widget.loadingWidgetStrokeWidth,
      color: widget.loadingWidgetColor,
      idleStatusText: widget.idleStatusText,
      loadingStatusText: widget.loadingStatusText,
      failedStatusText: widget.failedStatusText,
      finishedStatusText: widget.finishedStatusText,
      retryCount: _retryCount,
      maxRetries: widget.maxRetries,
      onRetry: retry,
    );
  }
}

class SliverGridLoadMoreView extends StatelessWidget {
  final SliverGridLoadMoreStatusState status;
  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final int retryCount;
  final int maxRetries;
  final VoidCallback? onRetry;

  const SliverGridLoadMoreView({
    super.key,
    required this.status,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
    required this.retryCount,
    required this.maxRetries,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if ((status == SliverGridLoadMoreStatusState.failed ||
                status == SliverGridLoadMoreStatusState.idle) &&
            onRetry != null) {
          onRetry!();
        }
      },
      child: Container(
        height: containerHeight,
        alignment: Alignment.center,
        child: _buildStatusWidget(),
      ),
    );
  }

  Widget _buildStatusWidget() {
    switch (status) {
      case SliverGridLoadMoreStatusState.idle:
        return Text(
          idleStatusText,
          style: TextStyle(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        );

      case SliverGridLoadMoreStatusState.loading:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            const SizedBox(width: 12),
            Text(loadingStatusText, style: TextStyle(color: Colors.grey[600])),
          ],
        );

      case SliverGridLoadMoreStatusState.failed:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[400], size: size),
            const SizedBox(height: 8),
            Text(
              failedStatusText,
              style: TextStyle(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
            if (maxRetries > 0) ...[
              const SizedBox(height: 4),
              Text(
                'Retry $retryCount/$maxRetries',
                style: TextStyle(color: Colors.grey[500], fontSize: 11),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              'Tap to retry',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        );

      case SliverGridLoadMoreStatusState.finished:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green[400],
              size: size * 0.8,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                finishedStatusText,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
    }
  }
}

// Utility function for synchronized access (simplified mutex)
T synchronized<T>(Object lock, T Function() callback) {
  // In a real implementation, you might want to use a proper mutex
  // For now, this is a placeholder that executes the callback directly
  return callback();
}

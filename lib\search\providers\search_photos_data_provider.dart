import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/search/dto/search_data.dart';

final class SearchPhotosDataNotifier extends Notifier<PhotosSearchData> {
  @override
  PhotosSearchData build() => const PhotosSearchData();

  void setLoadMoreLastId(int loadMoreLastId) {
    if (state.loadMoreLastId == loadMoreLastId) return;
    state = state.copyWith(loadMoreLastId: loadMoreLastId);
  }

  void setLoadMoreEndReached(bool loadMoreEndReached) {
    if (state.loadMoreEndReached == loadMoreEndReached) return;
    state = state.copyWith(loadMoreEndReached: loadMoreEndReached);
  }

  void replace(PhotosSearchData data) {
    state = data;
  }

  void reset() {
    state = const PhotosSearchData();
  }
}

final searchPhotosDataProvider =
    NotifierProvider.autoDispose<SearchPhotosDataNotifier, PhotosSearchData>(
      SearchPhotosDataNotifier.new,
    );

import 'package:photo_manager/photo_manager.dart';

class PlainAlbumData {
  final String name;
  final int totalPhotos;
  final AssetEntity? coverPhoto;

  PlainAlbumData({
    required this.name,
    required this.totalPhotos,
    this.coverPhoto,
  });

  factory PlainAlbumData.fromMap(Map<String, dynamic> map) {
    return PlainAlbumData(
      name: map.containsKey('name') ? map['name'] : '',
      totalPhotos: map.containsKey('totalPhotos') ? map['totalPhotos'] : '',
      coverPhoto: map.containsKey('coverPhoto') ? map['coverPhoto'] : null,
    );
  }

  Map<String, dynamic> toMap() {
    return {'name': name, 'totalPhotos': totalPhotos, 'coverPhoto': coverPhoto};
  }

  PlainAlbumData clone() {
    return PlainAlbumData(
      name: name,
      totalPhotos: totalPhotos,
      coverPhoto: coverPhoto,
    );
  }

  PlainAlbumData copyWith({
    String? name,
    int? totalPhotos,
    AssetEntity? coverPhoto,
  }) {
    return PlainAlbumData(
      name: name ?? this.name,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      coverPhoto: coverPhoto ?? this.coverPhoto,
    );
  }

  @override
  String toString() {
    return 'PlainAlbumData(name: $name, totalPhotos: $totalPhotos)';
  }

  @override
  int get hashCode => name.hashCode ^ totalPhotos.hashCode;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;

    return other is PlainAlbumData &&
        other.name == name &&
        other.totalPhotos == totalPhotos;
  }
}

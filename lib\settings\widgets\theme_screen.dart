import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/utils/theme_manager.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/settings/widgets/theme_screen/theme_screen_list_tile.dart';

class ThemeScreen extends StatefulWidget {
  const ThemeScreen({super.key});

  @override
  ThemeScreenState createState() => ThemeScreenState();
}

class ThemeScreenState extends State<ThemeScreen> {
  final _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          PmSliverAppBar(
            scrollController: _scrollController,
            titleText: 'Theme',
            automaticallyImplyLeading: true,
            useLogo: false,
            actions: const [],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.only(left: 3.0),
              child: ValueListenableBuilder<ThemeMode>(
                valueListenable: ThemeManager.instance,
                builder: (context, themeMode, child) {
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'Light mode',
                        value: 'light',
                        selected: themeMode == ThemeMode.light,
                        onTap: _handleThemeChange,
                      ),
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'Dark mode',
                        value: 'dark',
                        selected: themeMode == ThemeMode.dark,
                        onTap: _handleThemeChange,
                      ),
                      const SizedBox(height: LayoutConfig.contentTopGap),
                      ThemeScreenListTile(
                        title: 'System default',
                        value: 'system',
                        selected: themeMode == ThemeMode.system,
                        onTap: _handleThemeChange,
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleThemeChange(String value) {
    log('Switching theme mode to: $value');

    switch (value) {
      case 'light':
        ThemeManager.instance.setLightTheme();
        break;
      case 'dark':
        ThemeManager.instance.setDarkTheme();
        break;
      case 'system':
        ThemeManager.instance.setSystemTheme();
        break;
    }
  }
}

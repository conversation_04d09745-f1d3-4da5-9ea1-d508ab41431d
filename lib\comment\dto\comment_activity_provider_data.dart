import 'package:flutter/material.dart';
import 'package:portraitmode/comment/enum.dart';

@immutable
final class CommentActivityProviderData {
  final CommentFormMode formMode;
  final int? commentIdToEdit;
  final int? commentIdToReply;
  final int? expandedCommentId;

  const CommentActivityProviderData({
    this.formMode = CommentFormMode.add,
    this.commentIdToEdit,
    this.commentIdToReply,
    this.expandedCommentId,
  });

  CommentActivityProviderData copyWithFormMode(CommentFormMode mode) {
    return CommentActivityProviderData(
      formMode: mode,
      commentIdToEdit: commentIdToEdit,
      commentIdToReply: commentIdToReply,
      expandedCommentId: expandedCommentId,
    );
  }

  CommentActivityProviderData copyWithCommentIdToEdit(int? commentId) {
    return CommentActivityProviderData(
      formMode: formMode,
      commentIdToEdit: commentId,
      commentIdToReply: commentIdToReply,
      expandedCommentId: expandedCommentId,
    );
  }

  CommentActivityProviderData copyWithCommentIdToReply(int? commentId) {
    return CommentActivityProviderData(
      formMode: formMode,
      commentIdToEdit: commentIdToEdit,
      commentIdToReply: commentId,
      expandedCommentId: expandedCommentId,
    );
  }

  CommentActivityProviderData copyWithExpandedComment(int? commentId) {
    return CommentActivityProviderData(
      formMode: formMode,
      commentIdToEdit: commentIdToEdit,
      commentIdToReply: commentIdToReply,
      expandedCommentId: commentId,
    );
  }

  @override
  int get hashCode => Object.hash(
    formMode,
    commentIdToEdit,
    commentIdToReply,
    expandedCommentId,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CommentActivityProviderData) return false;

    return other.formMode == formMode &&
        other.commentIdToEdit == commentIdToEdit &&
        other.commentIdToReply == commentIdToReply &&
        other.expandedCommentId == expandedCommentId;
  }

  @override
  String toString() {
    return '''
CommentActivityProviderData(
  formMode: $formMode,
  commentIdToEdit: $commentIdToEdit,
  commentIdToReply: $commentIdToReply,
  expandedCommentId: $expandedCommentId,
)
''';
  }
}

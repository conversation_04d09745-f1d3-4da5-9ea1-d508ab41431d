import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';

class DropdownIconButton extends StatelessWidget {
  final Function onDropdownToggle;
  final bool dropdownOpened;

  const DropdownIconButton({
    super.key,
    required this.onDropdownToggle,
    this.dropdownOpened = false,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onDropdownToggle();
      },
      borderRadius: BorderRadius.circular(40.0),
      child: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Icon(
          (dropdownOpened ? Ionicons.chevron_up : Ionicons.chevron_down),
          color: Colors.white,
        ),
      ),
    );
  }
}

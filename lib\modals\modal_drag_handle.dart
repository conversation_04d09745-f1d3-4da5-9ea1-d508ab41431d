import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class ModalDragHandle extends StatelessWidget {
  const ModalDragHandle({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SizedBox(
        height: 4.0,
        width: 40.0,
        child: Container(
          decoration: BoxDecoration(
            color: context.colors.greyColor,
            borderRadius: BorderRadius.circular(2.0),
          ),
        ),
      ),
    );
  }
}

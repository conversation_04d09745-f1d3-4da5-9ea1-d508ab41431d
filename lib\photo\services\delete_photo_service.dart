// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';

// Internal packages.
import 'package:portraitmode/app/config/url.dart';

class DeletePhotoService extends BaseService {
  Future<BaseResponse> delete(int photoId) async {
    try {
      final response = await http.delete(
        '${URL.baseApiUrl}/photo/$photoId',
        data: {'photo_id': photoId.toString()},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

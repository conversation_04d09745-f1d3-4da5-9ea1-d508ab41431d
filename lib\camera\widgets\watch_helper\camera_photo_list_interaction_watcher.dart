import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_photo_list_interaction_data.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_interaction_provider.dart';

class CameraPhotoListInteractionWatcher extends ConsumerWidget {
  final String slug;

  const CameraPhotoListInteractionWatcher({super.key, required this.slug});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final NotifierProvider<
      CameraPhotoListInteractionNotifier,
      CameraPhotoListInteractionData
    >
    provider = getCameraPhotoListInteractionProvider(slug);

    final int lastItemSeenId = ref.watch(
      provider.select((data) => data.lastItemSeenId),
    );

    return lastItemSeenId > 0
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}

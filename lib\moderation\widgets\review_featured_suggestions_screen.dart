import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/moderation/dto/featured_suggestion_photo_data.dart';
import 'package:portraitmode/moderation/http_responses/featured_suggestion_list_response.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/widgets/review_photo_list_item.dart';

class ReviewFeaturedSuggestionsScreen extends ConsumerStatefulWidget {
  const ReviewFeaturedSuggestionsScreen({super.key});

  @override
  ReviewFeaturedSuggestionsScreenState createState() =>
      ReviewFeaturedSuggestionsScreenState();
}

class ReviewFeaturedSuggestionsScreenState
    extends ConsumerState<ReviewFeaturedSuggestionsScreen> {
  final _scrollController = ScrollController();

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _loadMoreLastId = 0;
  bool _loadMoreEndReached = false;

  final ModerationService _moderationService = ModerationService();

  List<FeaturedSuggestionPhotoData> _photoList = [];

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            edgeOffset: LayoutConfig.bottomNavBarHeight,
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: <Widget>[
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: "Featured suggestions",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                ),
                EasyLoadMore(
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  loadingStatusText: "",
                  finishedStatusText: "",
                  child: _buildSliverListView(),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView() {
    final int userId = LocalUserService.userId ?? 0;

    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        final double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;
        final FeaturedSuggestionPhotoData photo = _photoList[index];

        return Container(
          margin: EdgeInsets.only(top: marginTop),
          child: ReviewPhotoListItem(
            index: index,
            photo: photo.toPhotoData(),
            isOwnPhoto: photo.authorId == userId,
            moderationType: 'featured_suggestion',
            curators: photo.curators,
            onModerationDismissed: _handleActionDone,
            onPhotoDeleted: _handleActionDone,
            onPhotoFeatured: _handleActionDone,
          ),
        );
      }, childCount: _photoList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _photoList = [];
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    FeaturedSuggestionListResponse response = await _moderationService
        .fetchFeaturedSuggestions(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
        );

    _handlePhotoListResponse(response, true, false);
  }

  Future<bool> _handleLoadMore() async {
    FeaturedSuggestionListResponse response = await _moderationService
        .fetchFeaturedSuggestions(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
        );

    final isFirstLoad = _loadMoreLastId == 0;

    return _handlePhotoListResponse(response, false, isFirstLoad);
  }

  bool _handlePhotoListResponse(
    FeaturedSuggestionListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    _loadMoreLastId = response.data.last.id;

    // Sort the array before storing it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        if (isRefresh) {
          _photoList = response.data;
        } else {
          if (isFirstLoad) {
            _photoList = response.data;
          } else {
            _photoList.addAll(response.data);
          }
        }
      });
    }

    return true;
  }

  void _handleActionDone(int photoId) {
    int index = _photoList.indexWhere((photo) => photo.id == photoId);
    if (index == -1) return;

    if (mounted) {
      setState(() {
        _photoList = List.from(_photoList)..removeAt(index);
      });
    }
  }
}

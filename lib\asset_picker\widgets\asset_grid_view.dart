import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/services/asset_service.dart';
import 'package:portraitmode/asset_picker/widgets/asset_grid_item.dart';

class AssetGridView extends StatefulWidget {
  final int gridAxisCount;
  final int gridItemSize;
  final int gridImageQuality;
  final int perPage;
  final int initialPage;
  final AssetPathEntity? album;
  final List<AssetEntity> initialAssetEntityList;

  const AssetGridView({
    super.key,
    required this.gridAxisCount,
    required this.gridItemSize,
    required this.gridImageQuality,
    required this.perPage,
    required this.initialPage,
    this.album,
    this.initialAssetEntityList = const [],
  });

  @override
  AssetGridViewState createState() => AssetGridViewState();
}

class AssetGridViewState extends State<AssetGridView> {
  final List<AssetEntity> _assetEntityList = [];

  int _currentPage = 0;
  int? _lastPage;
  bool _isFetchingEntities = false;

  @override
  void initState() {
    super.initState();
    _currentPage = widget.initialPage;
    _assetEntityList.addAll(widget.initialAssetEntityList);
  }

  @override
  void dispose() {
    _assetEntityList.clear();
    _currentPage = 0;
    _lastPage = null;
    _isFetchingEntities = false;

    super.dispose();
  }

  @override
  void didUpdateWidget(covariant AssetGridView oldWidget) {
    super.didUpdateWidget(oldWidget);
    _resetGrid();
  }

  @override
  Widget build(BuildContext context) {
    return NotificationListener<ScrollNotification>(
      onNotification: (ScrollNotification notification) {
        _handleScrollEvent(notification);
        return true;
      },
      child: _buildGridView(),
    );
  }

  GridView _buildGridView() {
    return GridView.builder(
      itemCount: _assetEntityList.length,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: widget.gridAxisCount,
      ),
      itemBuilder: (BuildContext context, int index) {
        return Padding(
          padding: const EdgeInsets.all(1.0),
          child: GestureDetector(
            onTap: () {
              if (Navigator.of(context).canPop()) {
                Navigator.of(context).pop(_assetEntityList[index]);
                return;
              }
            },
            child: AssetGridItem(
              entity: _assetEntityList[index],
              size: widget.gridItemSize,
              quality: widget.gridImageQuality,
            ),
          ),
        );
      },
    );
  }

  void _resetGrid() {
    _assetEntityList.clear();
    _currentPage = 0;
    _lastPage = null;
    _isFetchingEntities = false;

    _fetchAssetEntities();
  }

  void _handleScrollEvent(ScrollNotification notif) {
    if (notif.metrics.pixels / notif.metrics.maxScrollExtent <= 0.9) {
      return;
    }

    _fetchAssetEntities();
  }

  Future<void> _fetchAssetEntities() async {
    if (widget.album == null ||
        _isFetchingEntities ||
        _currentPage == _lastPage) {
      return;
    }

    _lastPage = _currentPage;

    _isFetchingEntities = true;

    final entities = await AssetService().fetch(
      pathEntity: widget.album!,
      perPage: widget.perPage,
      page: _currentPage,
    );

    if (entities.isEmpty) {
      _isFetchingEntities = false;
      return;
    }

    _currentPage++;
    _isFetchingEntities = false;

    if (mounted) {
      setState(() {
        _assetEntityList.addAll(entities);
      });
    }
  }
}

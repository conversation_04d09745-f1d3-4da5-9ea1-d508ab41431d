import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/widgets/membership_badge.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/profile/widgets/edit_profile_screen.dart';

class ProfileHeaderLoading extends StatelessWidget {
  const ProfileHeaderLoading({super.key, required this.artist});

  final ArtistPartialData artist;

  @override
  Widget build(BuildContext context) {
    int profileId = LocalUserService.userId ?? 0;
    final bool isOwnProfile = artist.id == profileId;

    Color placeholderColor = context.isDarkMode
        ? AppColorsCache.dark().baseColor
        : AppColorsCache.light().baseColor.withAlpha(70);

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 30.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Avatar(
            imageUrl: artist.avatarUrl,
            size: 80.0,
            useBorder: true,
            borderColor: Colors.white,
            onTap: () {
              if (!isOwnProfile) {
                return;
              }

              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const EditProfileScreen(),
                ),
              );
            },
          ),
          const SizedBox(height: 8.0),
          Text(
            '@${artist.nicename}',
            style: TextStyle(
              fontSize: 12.0,
              color: context.colors.primarySwatch[200],
            ),
          ),
          const SizedBox(height: 5.0),
          Row(
            children: [
              Text(
                artist.displayName,
                style: TextStyle(
                  // Fill the "color" based on textTheme's display color.
                  color: Theme.of(context).textTheme.bodySmall!.color,
                  fontSize: 18.0,
                  fontWeight: FontWeight.w700,
                ),
              ),
              if (artist.membershipType.isNotEmpty)
                MembershipBadge(
                  membershipType: artist.membershipType,
                  size: 13.0,
                  padding: const EdgeInsets.only(left: 4.0, top: 2.0),
                ),
            ],
          ),
          const SizedBox(height: 15.0),
          Column(
            children: [
              Container(
                width: double.infinity,
                height: 40.0,
                decoration: BoxDecoration(
                  color: placeholderColor,
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              const SizedBox(height: 10.0),
              Row(
                children: [
                  Container(
                    width: ScreenSize.width(context) * 0.6,
                    height: 20.0,
                    decoration: BoxDecoration(
                      color: placeholderColor,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 4.0),
              Row(
                children: [
                  Container(
                    width: ScreenSize.width(context) * 0.6,
                    height: 20.0,
                    decoration: BoxDecoration(
                      color: placeholderColor,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 15.0),
          Container(
            width: MediaQuery.sizeOf(context).width / 1.5,
            height: 42.0,
            decoration: BoxDecoration(
              color: placeholderColor,
              borderRadius: BorderRadius.circular(4.0),
            ),
          ),
        ],
      ),
    );
  }
}

class LikeCommentResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final LikeCommentResponseData? data;

  LikeCommentResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory LikeCommentResponse.fromMap(Map<String, dynamic> map) {
    return LikeCommentResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? LikeCommentResponseData.fromMap(map['data'])
          : null,
    );
  }
}

class LikeCommentResponseData {
  final bool isLiked;
  final int totalLikes;

  LikeCommentResponseData({this.isLiked = false, this.totalLikes = 0});

  factory LikeCommentResponseData.fromMap(Map<String, dynamic> map) {
    return LikeCommentResponseData(
      isLiked: map['isLiked'] ?? false,
      totalLikes: map['totalLikes'] ?? 0,
    );
  }
}

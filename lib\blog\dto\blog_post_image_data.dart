class BlogPostImageData {
  final int id;
  final String url;
  final int width;
  final int height;
  final String mediumUrl;
  final int mediumWidth;
  final int mediumHeight;
  final String thumbnailUrl;
  final int thumbnailWidth;
  final int thumbnailHeight;

  BlogPostImageData({
    this.id = 0,
    this.url = '',
    this.width = 0,
    this.height = 0,
    this.mediumUrl = '',
    this.mediumWidth = 0,
    this.mediumHeight = 0,
    this.thumbnailUrl = '',
    this.thumbnailWidth = 0,
    this.thumbnailHeight = 0,
  });

  factory BlogPostImageData.fromMap(Map<String, dynamic> map) {
    return BlogPostImageData(
      id: map['id'] ?? 0,
      url: map['url'] ?? '',
      width: map['width'] ?? 0,
      height: map['height'] ?? 0,
      mediumUrl: map['mediumUrl'] ?? '',
      mediumWidth: map['mediumWidth'] ?? 0,
      mediumHeight: map['mediumHeight'] ?? 0,
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      thumbnailWidth: map['thumbnailWidth'] ?? 0,
      thumbnailHeight: map['thumbnailHeight'] ?? 0,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'url': url,
      'width': width,
      'height': height,
      'mediumUrl': mediumUrl,
      'mediumWidth': mediumWidth,
      'mediumHeight': mediumHeight,
      'thumbnailUrl': thumbnailUrl,
      'thumbnailWidth': thumbnailWidth,
      'thumbnailHeight': thumbnailHeight,
    };
  }

  // to string using '''.
  @override
  String toString() {
    return '''BlogPostImageData(
      id: $id,
      url: $url,
      width: $width,
      height: $height,
      mediumUrl: $mediumUrl,
      mediumWidth: $mediumWidth,
      mediumHeight: $mediumHeight,
      thumbnailUrl: $thumbnailUrl,
      thumbnailWidth: $thumbnailWidth,
      thumbnailHeight: $thumbnailHeight,
    )''';
  }
}

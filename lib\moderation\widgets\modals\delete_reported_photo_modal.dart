import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/moderation/utils/reason.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

class DeleteReportedPhotoModal extends ConsumerStatefulWidget {
  final ScrollController scrollController;
  final PhotoData photo;
  final bool isOwnPhoto;
  final Function(int)? onPhotoDeleted;

  const DeleteReportedPhotoModal({
    super.key,
    required this.scrollController,
    required this.photo,
    this.isOwnPhoto = false,
    this.onPhotoDeleted,
  });

  @override
  DeleteReportedPhotoModalState createState() =>
      DeleteReportedPhotoModalState();
}

class DeleteReportedPhotoModalState
    extends ConsumerState<DeleteReportedPhotoModal> {
  final ModerationService _moderationService = ModerationService();
  bool _isLoading = false;
  String _selectedReason = '';

  final Map<String, String> _reasons = getModerationDeletionReasons();

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        children: [
          Expanded(
            child: ListView(
              controller: widget.scrollController,
              children: [
                const ModalDragHandle(),
                const SizedBox(height: 8),
                for (String reason in _reasons.keys)
                  ListTile(
                    title: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _reasons[reason]!,
                          style: const TextStyle(fontSize: 14.5),
                        ),
                        if (_selectedReason == reason)
                          const SizedBox(width: 5.0),
                        if (_selectedReason == reason)
                          Icon(
                            Ionicons.checkmark_circle_sharp,
                            color: context.colors.accentColor,
                            size: 15,
                          ),
                      ],
                    ),
                    onTap: () => _handleOnTap(reason),
                    // dense: true,
                    visualDensity: const VisualDensity(vertical: -3.0),
                  ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 14.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: SubmitButton(
              buttonText: "Report",
              width: double.infinity,
              height: 40.0,
              fontWeight: FontWeight.w600,
              onPressed: () async {
                await _handleOnSubmit();
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _handleOnTap(String targettedAlbumSlug) async {
    if (!mounted) return;

    setState(() {
      if (_selectedReason == targettedAlbumSlug) {
        _selectedReason = '';
      } else {
        _selectedReason = targettedAlbumSlug;
      }
    });
  }

  Future<void> _handleOnSubmit() async {
    if (_isLoading) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    BaseResponse response = await _moderationService.deleteReportedPhoto(
      photoId: widget.photo.id,
      reason: _selectedReason,
    );

    if (!response.success) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });
    }

    if (widget.isOwnPhoto) {
      ref.read(archivedPhotoIdsProvider.notifier).removeItem(widget.photo.id);

      ref
          .read(myAlbumProvider.notifier)
          .decrementTotalPhotos(widget.photo.album);

      myAlbumPhotoListProviderMap.forEach((
        String albumSlug,
        NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
      ) {
        ref.read(provider.notifier).removeItem(widget.photo.id);
      });
    }

    ref.read(photoStoreProvider.notifier).removeItem(widget.photo.id);

    if (widget.onPhotoDeleted != null) {
      widget.onPhotoDeleted!(widget.photo.id);
    }

    if (mounted) {
      Navigator.of(context).pop();

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(response.message)));
    }
  }
}

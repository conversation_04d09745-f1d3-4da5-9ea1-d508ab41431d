import 'package:flutter/material.dart';

class MaxWidth extends StatelessWidget {
  final double? width;
  final double? height;
  final double maxWidth;
  final EdgeInsetsGeometry? padding;
  final Widget child;

  const MaxWidth({
    super.key,
    this.width,
    this.height,
    required this.maxWidth,
    this.padding,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      padding: padding,
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: child,
    );
  }
}

class MaxWidthBuilder extends StatelessWidget {
  const MaxWidthBuilder({
    super.key,
    this.width,
    this.height,
    required this.maxWidth,
    required this.builder,
  });

  final double? width;
  final double? height;
  final double maxWidth;
  final Widget Function(BuildContext, BoxConstraints) builder;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      constraints: BoxConstraints(maxWidth: maxWidth),
      child: Layout<PERSON><PERSON><PERSON>(builder: builder),
    );
  }
}

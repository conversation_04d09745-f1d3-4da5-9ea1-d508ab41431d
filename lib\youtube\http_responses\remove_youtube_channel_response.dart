import 'package:portraitmode/youtube/dto/youtube_channel_data.dart';

class RemoveYoutubeChannelResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final List<YoutubeChannelData> data;

  RemoveYoutubeChannelResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory RemoveYoutubeChannelResponse.fromMap(Map<String, dynamic> map) {
    return RemoveYoutubeChannelResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List
          ? (map['data'] as List)
                .map<YoutubeChannelData>(
                  (channel) => YoutubeChannelData.fromMap(channel),
                )
                .toList()
          : [],
    );
  }
}

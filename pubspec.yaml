name: portraitmode
description: The space your photos deserve.
publish_to: "none"
version: 1.10.5+92
environment:
  sdk: ">=3.8.1 <4.0.0"
  flutter: "3.32.5"

dependencies:
  flutter:
    sdk: flutter
  collection: ^1.17.2

  # When we sticked to Flutter 3.13.9 (with Dart SDK 3.1.5),
  # we needed to use package_info_plus version 4.2.0
  #
  # Because at that time, package_info_plus version 5.0.1 requires Dart SDK >=3.2.0
  # which was available since Flutter 3.16.0
  #
  # But now, we are following Flutter latest version,
  # so we can use the latest version of package_info_plus
  package_info_plus: ^8.3.0

  path: ^1.9.1
  path_provider: ^2.1.5
  url_launcher: ^6.3.1
  image_picker: ^1.1.2
  flutter_svg: ^2.1.0

  # Last update 2 years ago, consider forking it
  flutter_staggered_grid_view: ^0.7.0

  flutter_riverpod: ^3.0.0-dev.16
  device_info_plus: ^11.4.0
  dio: ^5.8.0+1
  flutter_secure_storage: ^9.2.4
  hive_ce: ^2.11.3
  hive_ce_flutter: ^2.3.1
  google_sign_in: ^6.3.0
  extension_google_sign_in_as_googleapis_auth: ^2.0.13
  app_links: ^6.4.0
  intl: ^0.20.0
  numeral: ^3.1.1
  google_fonts: ^6.2.1

  # Last update was 2 years ago, consider forking it
  ionicons: ^0.2.2

  flutter_inappwebview: ^6.1.5
  easy_loading_button: ^0.4.0
  easy_load_more: ^2.0.0

  # Last update was 2 years ago, consider forking it
  scrollable_positioned_list: ^0.3.8

  flutter_slidable: ^4.0.0
  date_format: ^2.0.9
  flutter_native_splash: ^2.4.6
  timeago: ^3.7.1
  easy_social_share: ^0.1.2

  form_field_validator:
    git:
      url: https://github.com/ultrafastwork/form_field_validator
      ref: master

  # When sticking with Flutter 3.13.9, we needed used photo_manager version 2.7.1 (EXACT version)
  # If we used caret version, it would automatically upgrade to version 2.8.1 which had issue with permission denied
  # photo_manager: 2.7.1

  # What was the problem with photo_manager version 2.8.1?
  # At that time, photo_manager version 2.8.1 has DecoderCallback issue on Flutter 3.16.0
  # So we sticked back with Flutter 3.13.9
  # But turned out, photo_manager 2.8.1 still had issue with permission denied even with Flutter 3.13.9

  # How about photo_manager version 3 (dev)?
  # photo_manager version 3 (dev) still has permission denied issue on Flutter 3.16.0 at that time.

  # But since we are now following Flutter latest version,
  # let's try to use the latest version of photo_manager and let's see the result.
  photo_manager: 3.7.1

  photo_view: 0.15.0

  google_place:
    git:
      url: https://github.com/ultrafastwork/google_place
      ref: master

  cached_network_image: ^3.4.1
  flutter_widget_from_html: ^0.16.0
  cupertino_icons: ^1.0.8

  flutter_cache_manager:
    git:
      url: https://github.com/ultrafastwork/flutter_cache_manager
      ref: mapsteps
      path: flutter_cache_manager

dependency_overrides:
  flutter_cache_manager:
    git:
      url: https://github.com/ultrafastwork/flutter_cache_manager
      ref: mapsteps
      path: flutter_cache_manager

dev_dependencies:
  flutter_test:
    sdk: flutter
  hive_ce_generator: ^1.9.2
  build_runner: ^2.4.6
  flutter_launcher_icons: ^0.14.3
  flutter_lints: ^6.0.0
  custom_lint: ^0.7.5
  riverpod_lint: ^3.0.0-dev.16

flutter:
  uses-material-design: true
  assets:
    - assets/
    - assets/onboarding/

flutter_native_splash:
  image: "assets/logo.png"
  color: "#F5F5F7"
  image_dark: "assets/logo-white.png"
  color_dark: "#121419"
  android_disable_fullscreen: true

flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/launcher-icon.png"

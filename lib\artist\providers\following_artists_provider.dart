import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/providers/id_list_notifier.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';

final class FollowingArtistIdsNotifier extends IdListNotifier {}

final followingArtistIdsProvider =
    NotifierProvider.autoDispose<FollowingArtistIdsNotifier, List<int>>(
      FollowingArtistIdsNotifier.new,
    );

final followingArtistsProvider = Provider.autoDispose<List<ArtistData>>((ref) {
  final ids = ref.watch(followingArtistIdsProvider);
  final store = ref.watch(artistStoreProvider);
  final items = <ArtistData>[];

  if (ids.isEmpty) return items;

  for (final id in ids) {
    final item = store[id];
    if (item != null) {
      items.add(item);
    }
  }

  return items;
});

/// High-level service for managing following artists.
/// This is the recommended way to manage following artists.
final class FollowingArtistsService {
  const FollowingArtistsService(this.ref);

  final Ref ref;

  // Please complete the methods.
}

/// Provider for the following artists service
final followingArtistsServiceProvider =
    Provider.autoDispose<FollowingArtistsService>((ref) {
      return FollowingArtistsService(ref);
    });

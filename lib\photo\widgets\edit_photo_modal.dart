import 'package:collection/collection.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/providers/category_list_provider.dart';
import 'package:portraitmode/category/widgets/categories_picker.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/edit_photo_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';

class EditPhotoModal extends ConsumerStatefulWidget {
  const EditPhotoModal({super.key, required this.photo});

  final PhotoData photo;

  @override
  EditPhotoModalState createState() => EditPhotoModalState();
}

class EditPhotoModalState extends ConsumerState<EditPhotoModal> {
  final _formKey = GlobalKey<FormState>();

  final _locationFieldController = TextEditingController();
  final _descriptionFieldController = TextEditingController();

  List<CategoryData> _selectedCategories = [];

  @override
  void initState() {
    super.initState();

    // List<CategoryData> categories = ref.read(categoryListProvider.notifier).all;
    List<CategoryData> categories = globalCategoryList;

    for (int categoryId in widget.photo.categories) {
      CategoryData? category = categories.firstWhereOrNull(
        (CategoryData category) => category.id == categoryId,
      );

      if (category != null) {
        _selectedCategories.add(category);
      }
    }

    _descriptionFieldController.text = widget.photo.description;
  }

  @override
  void dispose() {
    _locationFieldController.dispose();
    _descriptionFieldController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Form(
      key: _formKey,
      child: Column(
        children: [
          SizedBox(
            height: 10.0,
            child: Center(
              // Build bottom sheet drag handle.
              child: SizedBox(
                height: 4.0,
                width: 40.0,
                child: Container(
                  decoration: BoxDecoration(
                    color: context.colors.greyColor,
                    borderRadius: BorderRadius.circular(2.0),
                  ),
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 16.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: PmTextField(
              controller: _descriptionFieldController,
              labelText: "Caption",
              minLines: 2,
              maxLines: 7,
              // validator: FieldValidators.photoDescriptionValidator,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 16.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4.0),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4.0),
                border: Border.all(
                  color: context.colors.borderColor,
                  width: 1.0,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  TextButton(
                    onPressed: () {
                      Navigator.of(context).push(
                        MaterialPageRoute(
                          builder: (context) => CategoriesPicker(
                            selectedCategories: _selectedCategories,
                            onClose: (cats) {
                              FocusScope.of(context).unfocus();

                              if (mounted) {
                                setState(() {
                                  _selectedCategories = cats;
                                });
                              }
                            },
                          ),
                        ),
                      );
                    },
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        Icon(
                          Ionicons.add_circle,
                          color: context.colors.accentColor,
                          size: 20.0,
                        ),
                        const SizedBox(width: 4.0),
                        Text(
                          "Add categories",
                          style: TextStyle(color: context.colors.accentColor),
                        ),
                      ],
                    ),
                  ),
                  if (_selectedCategories.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 4.0,
                        right: 4.0,
                        bottom: 2.0,
                      ),
                      child: Wrap(
                        spacing: 8.0,
                        runSpacing: -6.0,
                        children: _selectedCategories
                            .map(
                              (cat) => Chip(
                                label: Text(cat.name),
                                onDeleted: () {
                                  if (!mounted) return;

                                  setState(() {
                                    _selectedCategories.remove(cat);
                                  });
                                },
                              ),
                            )
                            .toList(),
                      ),
                    ),
                ],
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 8.0,
              bottom: 16.0,
              left: ScreenStyleConfig.horizontalPadding,
              right: ScreenStyleConfig.horizontalPadding,
            ),
            child: SubmitButton(
              buttonText: "Update",
              width: double.infinity,
              height: 40.0,
              fontWeight: FontWeight.w600,
              onPressed: _handleSubmission,
            ),
          ),
        ],
      ),
    );
  }

  final _editPhotoService = EditPhotoService();

  Future<void> _handleSubmission() async {
    if (!_formKey.currentState!.validate()) return;

    UploadPhotoResponse response = await _editPhotoService.submit(
      photoId: widget.photo.id,
      description: _descriptionFieldController.text,
      categoryIds: _selectedCategories.map((cat) => cat.id).toList(),
    );

    if (!response.success || response.data == null) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    PhotoData data = response.data!;

    // Update the matched photo in the global myAlbumPhotoListProviderMap.
    myAlbumPhotoListProviderMap.forEach((
      String albumSlug,
      NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
    ) {
      List<PhotoData> photos = ref.read(provider);
      int matchedIndex = photos.indexWhere((photo) => photo.id == data.id);

      if (matchedIndex != -1) {
        ref.read(provider.notifier).updateItem(data);
      }
    });

    ref
        .read(photoStoreProvider.notifier)
        .updateItem(data, addIfNotExists: true);

    if (mounted) {
      Navigator.of(context).pop();

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          duration: const Duration(seconds: 2),
          content: Text(response.message),
        ),
      );

      showDialog(
        context: context,
        builder: (BuildContext context) {
          return AlertDialog(
            content: Text(response.message),
            actions: <Widget>[
              TextButton(
                child: const Text("Close"),
                onPressed: () {
                  Navigator.of(context).pop();
                },
              ),
            ],
          );
        },
      );
    }
  }
}

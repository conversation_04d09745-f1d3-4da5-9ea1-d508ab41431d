import 'package:flutter/material.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/album_tabbar.dart';

class AlbumTabbarDelegate extends SliverPersistentHeaderDelegate {
  final List<AlbumData> albumList;

  AlbumTabbarDelegate({required this.albumList});

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return AlbumTabbar(albumList: albumList);
  }

  @override
  double get maxExtent => 45.0;

  @override
  double get minExtent => 45.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}

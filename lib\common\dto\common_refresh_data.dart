import 'package:portraitmode/notification/dto/notification_fetch_result.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';

class CommonRefreshData {
  CommonRefreshData({
    this.notificationFetchResult,
    this.feedbackTokensAmount = 0,
    this.profileData,
  });

  final NotificationFetchResult? notificationFetchResult;
  final int feedbackTokensAmount;
  final ProfileData? profileData;

  factory CommonRefreshData.fromMap(Map<String, dynamic> map) {
    return CommonRefreshData(
      notificationFetchResult:
          map.containsKey('notificationFetchResult') &&
              map['notificationFetchResult'] is Map
          ? NotificationFetchResult.fromMap(map['notificationFetchResult'])
          : null,
      feedbackTokensAmount:
          map.containsKey('feedbackTokensAmount') &&
              map['feedbackTokensAmount'] is int
          ? map['feedbackTokensAmount']
          : 0,
      profileData: map.containsKey('profileData') && map['profileData'] is Map
          ? ProfileData.fromMap(map['profileData'])
          : null,
    );
  }
}

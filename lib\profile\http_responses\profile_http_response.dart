import 'package:portraitmode/profile/dto/profile_data.dart';

class ProfileHttpResponse {
  final bool success;
  final String errorCode;
  final String message;
  final ProfileData? data;

  ProfileHttpResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory ProfileHttpResponse.fromMap(Map<String, dynamic> map) {
    return ProfileHttpResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? ProfileData.fromMap(map['data'])
          : null,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

enum MembershipType { superbacker, supporter }

class MembershipBadge extends StatelessWidget {
  final String membershipType;
  final double? size;
  final EdgeInsetsGeometry padding;

  const MembershipBadge({
    super.key,
    required this.membershipType,
    this.size = 11.0,
    this.padding = const EdgeInsets.only(left: 4.0, top: 2.0),
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding,
      child: membershipType == 'superbacker'
          ? SvgPicture.asset(
              'assets/superbacker_icon.svg',
              width: size,
              height: size,
            )
          : membershipType == 'supporter'
          ? SvgPicture.asset(
              'assets/supporter_icon.svg',
              width: size,
              height: size,
            )
          : const SizedBox.shrink(),
    );
  }
}

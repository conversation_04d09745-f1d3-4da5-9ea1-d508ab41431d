// Core packages
import 'package:flutter/material.dart';

// Extension packages

// Internal packages
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/widget/blog_post_detail_screen.dart';

class BlogPostListSliderItem extends StatelessWidget {
  final BlogPostData post;
  final double? borderRadius;
  final bool isSquare;
  final double heightReducer;

  const BlogPostListSliderItem({
    super.key,
    required this.post,
    this.borderRadius,
    this.isSquare = false,
    this.heightReducer = 0.0,
  });

  @override
  Widget build(BuildContext context) {
    // log('Building category card with name is ${category.displayName}');

    double borderRadiusValue = borderRadius == null
        ? PhotoStyleConfig.borderRadius
        : borderRadius!;

    return LayoutBuilder(
      builder: (BuildContext layoutBuildContext, BoxConstraints constraints) {
        double itemWidth = constraints.maxWidth;
        // double itemWidth = double.infinity;
        double itemHeight = isSquare
            ? itemWidth
            : itemWidth - (itemWidth / 6) - heightReducer;
        // double itemHeight = 100;

        return SizedBox(
          width: itemWidth,
          height: itemHeight,
          child: GestureDetector(
            onTap: () => _handleOnTap(context),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (post.featuredImage != null)
                  ClipRRect(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    child: PmNetworkImage(
                      url: post.featuredImage!.url,
                      width: itemWidth,
                      height: itemHeight,
                    ),
                  ),
                Container(
                  height: itemHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    // color: Colors.black.withValues(alpha: 0.5),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.6),
                        Colors.black.withValues(alpha: 0.2),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Center(
                    child: Text(
                      post.title,
                      style: const TextStyle(
                        fontSize: 16.0,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleOnTap(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlogPostDetailScreen(post: post, isLoading: true),
      ),
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

final class CameraPhotoListNotifier extends Notifier<List<PhotoData>> {
  final Map<int, int> _idIndexCache = {};

  @override
  List<PhotoData> build() => [];

  void _updateState(List<PhotoData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  PhotoData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  void addItem(PhotoData newItem) {
    if (hasItem(newItem.id)) return;
    final updated = [...state, newItem];
    _updateState(updated);
  }

  void addItems(List<PhotoData> newItems) {
    final existingIds = _idIndexCache.keys.toSet();
    final filtered = newItems.where((item) => !existingIds.contains(item.id));
    if (filtered.isEmpty) return;

    final updated = [...state, ...filtered];
    _updateState(updated);
  }

  void updateItem(PhotoData newItem) {
    final index = _idIndexCache[newItem.id];
    if (index == null || index >= state.length) return;

    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  void removeItem(int id) {
    if (!_idIndexCache.containsKey(id)) return;
    final updated = state.where((item) => item.id != id).toList();
    _updateState(updated);
  }

  void replaceAll(List<PhotoData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }
}

Map cameraPhotoListProviderMap = {};

NotifierProvider<CameraPhotoListNotifier, List<PhotoData>>
getCameraPhotoListProvider(String cameraName) {
  if (cameraPhotoListProviderMap.containsKey(cameraName)) {
    return cameraPhotoListProviderMap[cameraName];
  }

  cameraPhotoListProviderMap[cameraName] =
      NotifierProvider.autoDispose<CameraPhotoListNotifier, List<PhotoData>>(
        CameraPhotoListNotifier.new,
      );

  return cameraPhotoListProviderMap[cameraName];
}

void deleteCameraPhotoListProvider(String cameraName) {
  if (cameraPhotoListProviderMap.containsKey(cameraName)) {
    cameraPhotoListProviderMap.remove(cameraName);
  }
}

void clearCameraPhotoListProviders() {
  cameraPhotoListProviderMap.clear();
}

void removeCameraPhotoListItem(WidgetRef ref, int photoId) {
  if (!ref.context.mounted) return;

  // Loop through cameraPhotoListProviderMap.
  for (final cameraName in cameraPhotoListProviderMap.keys) {
    final NotifierProvider<CameraPhotoListNotifier, List<PhotoData>> provider =
        cameraPhotoListProviderMap[cameraName];

    ref.read(provider.notifier).removeItem(photoId);
  }
}

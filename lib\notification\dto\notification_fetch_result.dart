import 'package:flutter/foundation.dart';
import 'package:portraitmode/notification/dto/notification_data.dart';

@immutable
class NotificationFetchResult {
  final int totalFound;
  final int totalUnread;
  final int totalRead;
  final List<NotificationData> notifications;

  const NotificationFetchResult({
    this.totalFound = 0,
    this.totalUnread = 0,
    this.totalRead = 0,
    this.notifications = const [],
  });

  NotificationFetchResult copyWith({
    int? totalFound,
    int? totalUnread,
    int? totalRead,
    List<NotificationData>? notifications,
  }) {
    return NotificationFetchResult(
      totalFound: totalFound ?? this.totalFound,
      totalUnread: totalUnread ?? this.totalUnread,
      totalRead: totalRead ?? this.totalRead,
      notifications: notifications ?? this.notifications,
    );
  }

  factory NotificationFetchResult.fromMap(Map<String, dynamic> map) {
    return NotificationFetchResult(
      totalFound: map['totalFound'] ?? 0,
      totalUnread: map['totalUnread'] ?? 0,
      totalRead: map['totalRead'] ?? 0,
      notifications:
          map['notifications'] != null &&
              map['notifications'] is List &&
              map['notifications'].isNotEmpty
          ? map['notifications']
                .map<NotificationData>((data) => NotificationData.fromMap(data))
                .toList()
          : [],
    );
  }
}

// image_pinch_zooming.dart

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// A highly optimized widget for pinch-to-zoom interaction on images.
///
/// This widget is ideal for use within scrollable containers like `ListView`,
/// where standard pinch gestures often conflict with vertical scrolling.
/// To address this, the zoomed view is rendered using an `Overlay`,
/// ensuring smooth gesture handling and better UX inside complex layouts.
///
/// Key Features:
/// - Smooth pinch-to-zoom interaction with customizable `minScale` and `maxScale`
/// - Zoomed view appears in a fullscreen overlay
/// - Optional background color behind the zoomed image
/// - Optionally hides the status bar while zooming (immersive mode)
/// - Built-in gesture support: tap, double-tap, long press
/// - Callbacks for zoom start and zoom end
/// - Designed to prevent scroll interference during zoom
///
/// ⚡ This widget has been highly optimized through iterative improvements
/// assisted by AI to ensure performance, responsiveness,
/// and reliability across edge cases.
///
/// Example usage:
/// ```dart
/// ImagePinchZooming(
///   image: Image.network('https://example.com/photo.jpg'),
/// )
/// ```
///
/// ---
///
/// ### Credits
/// This implementation is originally based on the now-discontinued package
/// [`pinch_zoom_image_last`](https://pub.dev/packages/pinch_zoom_image_last)
/// by [Furkan KURT](https://furkankurt.com.tr).
/// Source available on
/// [GitHub](https://github.com/furkankurt/pinch_zoom_image_last).
///
/// Substantial modifications were made
/// to address gesture handling within scrollable views,
/// improve overall interaction experience, and modernize the implementation.
///
/// Thanks to Furkan KURT
/// and the original contributors for their foundational work.
class ImagePinchZooming extends StatefulWidget {
  const ImagePinchZooming({
    super.key,
    required this.image,
    this.zoomedBackgroundColor = Colors.black54,
    this.hideStatusBarWhileZooming = false,
    this.minScale = 1.0,
    this.maxScale = 4.0,
    this.animationDuration = const Duration(milliseconds: 200),
    this.onTap,
    this.onDoubleTap,
    this.onLongPress,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final Widget image;
  final Color zoomedBackgroundColor;
  final bool hideStatusBarWhileZooming;
  final double minScale;
  final double maxScale;
  final Duration animationDuration;
  final VoidCallback? onTap;
  final VoidCallback? onDoubleTap;
  final VoidCallback? onLongPress;
  final VoidCallback? onZoomStart;
  final VoidCallback? onZoomEnd;

  @override
  State<ImagePinchZooming> createState() => _ImagePinchZoomingState();
}

class _ImagePinchZoomingState extends State<ImagePinchZooming>
    with TickerProviderStateMixin {
  OverlayEntry? _overlayEntry;
  Offset? _initialFocalPoint;
  Offset? _widgetOrigin;
  Size? _widgetSize;
  bool _isZooming = false;
  bool _isReversing = false;
  int _activePointers = 0;

  // Store original system UI mode for proper restoration
  SystemUiMode? _originalSystemUiMode;

  // Use a single key for overlay management
  final GlobalKey<_PinchZoomOverlayState> _overlayKey = GlobalKey();

  @override
  void initState() {
    super.initState();
    // Store original system UI mode if we need to hide status bar
    if (widget.hideStatusBarWhileZooming) {
      _storeOriginalSystemUiMode();
    }
  }

  @override
  void dispose() {
    _cleanupOverlay();
    _restoreSystemUI();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Listener(
      onPointerDown: _handlePointerDown,
      onPointerUp: _handlePointerUp,
      onPointerCancel: _handlePointerCancel,
      child: AbsorbPointer(
        absorbing: _isZooming,
        child: GestureDetector(
          onScaleStart: _handleScaleStart,
          onScaleUpdate: _handleScaleUpdate,
          onScaleEnd: _handleScaleEnd,
          onTap: widget.onTap,
          onDoubleTap: widget.onDoubleTap,
          onLongPress: widget.onLongPress,
          child: Visibility(
            visible: !_isZooming,
            maintainSize: true,
            maintainAnimation: true,
            maintainState: true,
            child: widget.image,
          ),
        ),
      ),
    );
  }

  void _handlePointerDown(PointerDownEvent event) {
    _activePointers++;
  }

  void _handlePointerUp(PointerUpEvent event) {
    _activePointers = (_activePointers - 1).clamp(0, 10);
  }

  void _handlePointerCancel(PointerCancelEvent event) {
    _activePointers = 0;
  }

  void _handleScaleStart(ScaleStartDetails details) {
    if (_overlayEntry != null || _isReversing || _activePointers < 2) return;

    // Safely get widget bounds
    final renderBox = context.findRenderObject() as RenderBox?;
    if (renderBox?.hasSize != true) return;

    _widgetSize = renderBox!.size;
    _widgetOrigin = renderBox.localToGlobal(Offset.zero);
    _initialFocalPoint = details.focalPoint;

    setState(() {
      _isZooming = true;
    });

    _handleSystemUI();
    widget.onZoomStart?.call();
    _showOverlay();
  }

  void _handleScaleUpdate(ScaleUpdateDetails details) {
    if (_isReversing ||
        _activePointers < 2 ||
        _overlayKey.currentState == null) {
      return;
    }

    final clampedScale = details.scale.clamp(widget.minScale, widget.maxScale);
    final newPosition =
        _widgetOrigin! + (details.focalPoint - _initialFocalPoint!);

    _overlayKey.currentState!._updateTransform(newPosition, clampedScale);
  }

  void _handleScaleEnd(ScaleEndDetails details) async {
    if (_isReversing || !_isZooming) return;

    _isReversing = true;
    widget.onZoomEnd?.call();

    await _overlayKey.currentState?._animateToOriginal();
    _cleanupOverlay();
    _restoreSystemUI();

    if (mounted) {
      setState(() {
        _isZooming = false;
        _isReversing = false;
      });
    }
  }

  void _showOverlay() {
    if (_overlayEntry != null || _widgetSize == null || _widgetOrigin == null) {
      return;
    }

    final overlay = Overlay.of(context);
    _overlayEntry = OverlayEntry(
      builder: (context) => _PinchZoomOverlay(
        key: _overlayKey,
        image: widget.image,
        originalSize: _widgetSize!,
        originalPosition: _widgetOrigin!,
        backgroundColor: widget.zoomedBackgroundColor,
        animationDuration: widget.animationDuration,
      ),
    );

    overlay.insert(_overlayEntry!);
  }

  void _cleanupOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
    _widgetOrigin = null;
    _widgetSize = null;
    _initialFocalPoint = null;
  }

  void _storeOriginalSystemUiMode() {
    // Note: There's no direct way to query current SystemUiMode in Flutter
    // We'll assume the most common default mode
    _originalSystemUiMode = SystemUiMode.edgeToEdge;
  }

  void _handleSystemUI() {
    if (!widget.hideStatusBarWhileZooming) return;

    SystemChrome.setEnabledSystemUIMode(SystemUiMode.immersive);
  }

  void _restoreSystemUI() {
    if (!widget.hideStatusBarWhileZooming) return;

    // Restore to original mode if stored, otherwise use sensible default
    SystemChrome.setEnabledSystemUIMode(
      _originalSystemUiMode ?? SystemUiMode.edgeToEdge,
    );
  }
}

// Overlay widget for zoom effect
class _PinchZoomOverlay extends StatefulWidget {
  final Widget image;
  final Size originalSize;
  final Offset originalPosition;
  final Color backgroundColor;
  final Duration animationDuration;

  const _PinchZoomOverlay({
    super.key,
    required this.image,
    required this.originalSize,
    required this.originalPosition,
    required this.backgroundColor,
    required this.animationDuration,
  });

  @override
  State<_PinchZoomOverlay> createState() => _PinchZoomOverlayState();
}

class _PinchZoomOverlayState extends State<_PinchZoomOverlay>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<Offset> _positionAnimation;
  late Animation<double> _opacityAnimation;

  Offset _currentPosition = Offset.zero;
  double _currentScale = 1.0;
  bool _isAnimating = false;

  // Cache screen size for performance
  Size? _cachedScreenSize;

  @override
  void initState() {
    super.initState();
    _currentPosition = widget.originalPosition;
    _animationController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Cache screen size on first build
    _cachedScreenSize ??= MediaQuery.of(context).size;

    if (_isAnimating) {
      return AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return _buildOverlayContent(
            _positionAnimation.value,
            _scaleAnimation.value,
            _opacityAnimation.value,
          );
        },
      );
    }

    return _buildOverlayContent(_currentPosition, _currentScale, _getOpacity());
  }

  Widget _buildOverlayContent(Offset position, double scale, double opacity) {
    return Stack(
      children: [
        // Background with opacity animation
        Visibility(
          visible: opacity > 0.0,
          child: AnimatedOpacity(
            opacity: opacity,
            duration: const Duration(milliseconds: 100),
            child: Container(
              width: double.infinity,
              height: double.infinity,
              color: widget.backgroundColor,
            ),
          ),
        ),
        // Zoomed image with transform
        Transform.translate(
          offset: position,
          child: Transform.scale(
            scale: scale,
            child: SizedBox(
              width: widget.originalSize.width,
              height: widget.originalSize.height,
              child: widget.image,
            ),
          ),
        ),
      ],
    );
  }

  double _getOpacity() {
    // Safe opacity calculation with cached screen size
    if (_currentScale <= 1.0) return 0.0;
    if (_cachedScreenSize == null) return 1.0;

    final screenHeight = _cachedScreenSize!.height;
    final maxScale = screenHeight / widget.originalSize.height;

    if (maxScale <= 1.0) return 1.0;

    return ((_currentScale - 1.0) / (maxScale - 1.0)).clamp(0.0, 1.0);
  }

  void _updateTransform(Offset position, double scale) {
    if (_isAnimating) return;

    setState(() {
      _currentPosition = position;
      _currentScale = scale;
    });
  }

  Future<void> _animateToOriginal() async {
    if (_isAnimating) return;

    _isAnimating = true;

    _scaleAnimation = Tween<double>(begin: _currentScale, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.fastOutSlowIn,
      ),
    );

    _positionAnimation =
        Tween<Offset>(
          begin: _currentPosition,
          end: widget.originalPosition,
        ).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.fastOutSlowIn,
          ),
        );

    _opacityAnimation = Tween<double>(begin: _getOpacity(), end: 0.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.fastOutSlowIn,
      ),
    );

    await _animationController.forward(from: 0.0);
    _animationController.reset();
    _isAnimating = false;
  }
}

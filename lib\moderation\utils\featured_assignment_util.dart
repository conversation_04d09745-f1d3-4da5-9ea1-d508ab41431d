import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';

class FeaturedAssignmentUtil {
  final BuildContext context;
  final WidgetRef ref;
  final PhotoData photo;
  final VoidCallback? startLoading;
  final Function({void Function()? callback})? stopLoading;

  FeaturedAssignmentUtil({
    required this.context,
    required this.ref,
    required this.photo,
    this.startLoading,
    this.stopLoading,
  });

  Future<void> handleAssignment({
    required AssignmentActionType action,
    bool notifyAuthor = false,
  }) async {
    if (!context.mounted) return;
    startLoading?.call();

    final moderationService = ModerationService();

    final BaseResponse response = await moderationService.featuredAssignment(
      photoId: photo.id,
      actionType: action,
      notifyAuthor: notifyAuthor,
    );

    if (!context.mounted) return;

    if (!response.success) {
      startLoading?.call();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    stopLoading?.call(
      callback: () {
        if (action == AssignmentActionType.assign) {
          ref
              .read(featuredPhotoIdsProvider.notifier)
              .addItem(photo.id, reorder: true);

          ref.read(photoStoreProvider.notifier).setFeatured(photo.id, true);
        } else {
          ref.read(featuredPhotoIdsProvider.notifier).removeItem(photo.id);
          ref.read(photoStoreProvider.notifier).setFeatured(photo.id, false);
        }

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              duration: const Duration(seconds: 2),
              content: Text(response.message),
            ),
          );
        }
      },
    );
  }
}

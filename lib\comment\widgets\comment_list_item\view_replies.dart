import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/expanded_comment.dart';

class ViewReplies extends ConsumerWidget {
  final TextEditingController commentFieldController;
  final FocusNode commentFieldFocusNode;
  final bool useIcon;
  final CommentData comment;

  const ViewReplies({
    super.key,
    required this.commentFieldController,
    required this.commentFieldFocusNode,
    this.useIcon = false,
    required this.comment,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final expandedCommentId = ref.watch(
      commentActivityProvider.select((item) => item.expandedCommentId),
    );

    bool isExpanded =
        expandedCommentId != null && expandedCommentId == comment.id
        ? true
        : false;

    return comment.totalReplies > 0 || isExpanded
        ? Padding(
            padding: const EdgeInsets.only(top: 8.0),
            child: Builder(
              builder: (context) {
                if (isExpanded) {
                  return ExpandedComment(
                    commentFieldController: commentFieldController,
                    commentFieldFocusNode: commentFieldFocusNode,
                    comment: comment,
                    shouldLoadReplies: comment.totalReplies > 0,
                    onClose: () => _handleCloseReplies(ref),
                  );
                }

                return _buildCollapsedWidget(ref);
              },
            ),
          )
        : const SizedBox.shrink();
  }

  Widget _buildCollapsedWidget(WidgetRef ref) {
    String replyWording = comment.totalReplies > 1 ? "replies" : "reply";

    return InkWell(
      onTap: () {
        ref
            .read(commentActivityProvider.notifier)
            .setExpandedCommentId(comment.id);
      },
      child: Row(
        children: [
          if (useIcon)
            const Icon(
              Ionicons.arrow_undo_outline,
              size: 16.0,
              color: Colors.black,
            ),
          Padding(
            padding: EdgeInsets.only(left: useIcon ? 4.0 : 0.0),
            child: Text(
              'View ${comment.totalReplies} $replyWording',
              style: TextStyle(
                fontSize: 12.0,
                color: ref.context.colors.contentLighterColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleCloseReplies(WidgetRef ref) {
    ref.read(commentActivityProvider.notifier).setExpandedCommentId(null);
  }
}

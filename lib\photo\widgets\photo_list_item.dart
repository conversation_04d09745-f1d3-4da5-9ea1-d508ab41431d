// photo_list_item.dart

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/comment_icon_button.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/like_icon_button.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/likes_count.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_description.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_frame.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_header.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/share_photo_button.dart';
import 'package:timeago/timeago.dart' as timeago;

class PhotoListItem extends StatelessWidget {
  final int index;
  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnProfile;
  final String screenName;
  final Function? onZoomStart;
  final Function? onZoomEnd;

  const PhotoListItem({
    super.key,
    required this.index,
    this.containerWidth,
    required this.photo,
    this.isOwnProfile = false,
    required this.screenName,
    this.onZoomStart,
    this.onZoomEnd,
  });

  @override
  Widget build(BuildContext context) {
    DateTime uploadDate = DateTime.parse(photo.date);
    uploadDate = uploadDate.add(uploadDate.timeZoneOffset).toUtc();

    return Align(
      alignment: Alignment.topCenter,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 6.0,
            ),
            child: PhotoHeader(
              photo: photo,
              screenName: screenName,
              isOwnProfile: isOwnProfile,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(bottom: 10.0),
            child: PhotoFrame(
              containerWidth: containerWidth,
              photo: photo,
              isOwnPhoto: isOwnProfile,
              screenName: screenName,
              onTap: () => _handleOnPhotoTap(context),
              onZoomStart: onZoomStart,
              onZoomEnd: onZoomEnd,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              right: ScreenStyleConfig.horizontalPadding,
              left: ScreenStyleConfig.horizontalPadding,
              bottom: 8.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: LikeIconButton(
                        photoId: photo.id,
                        isLiked: photo.isLiked,
                        totalLikes: photo.totalLikes,
                        shrinkTapTarget: true,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: CommentIconButton(
                        photo: photo,
                        screenName: screenName,
                        shrinkTapTarget: true,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(right: 10.0),
                      child: SharePhotoButton(
                        photo: photo,
                        shrinkTapTarget: true,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 5.0),
                LikesCount(photoId: photo.id, totalLikes: photo.totalLikes),
                (photo.description.isNotEmpty
                    ? Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: PhotoDescription(
                          photo: photo,
                          screenName: screenName,
                        ),
                      )
                    : const SizedBox.shrink()),
                Text(
                  timeago.format(uploadDate),
                  style: TextStyle(
                    fontSize: 12.0,
                    color: context.colors.timeColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _handleOnPhotoTap(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: screenName),
      ),
    );
  }
}

import 'package:portraitmode/blog/dto/blog_post_category_data.dart';
import 'package:portraitmode/blog/dto/blog_post_image_data.dart';

class BlogPostData {
  final int id;
  final String createdAt;
  final String updatedAt;
  final String url;
  final String slug;
  final String status;
  final String title;
  final String excerpt;
  final String content;
  final BlogPostImageData? featuredImage;
  final int authorId;
  final String authorNicename;
  final String authorDisplayName;
  final String authorAvatarUrl;
  final List<BlogPostCategoryData> categories;

  BlogPostData({
    this.id = 0,
    this.createdAt = '',
    this.updatedAt = '',
    this.url = '',
    this.slug = '',
    this.status = '',
    this.title = '',
    this.excerpt = '',
    this.content = '',
    this.featuredImage,
    this.authorId = 0,
    this.authorNicename = '',
    this.authorDisplayName = '',
    this.authorAvatarUrl = '',
    this.categories = const [],
  });

  factory BlogPostData.fromMap(Map<String, dynamic> map) {
    return BlogPostData(
      id: map['id'] ?? 0,
      createdAt: map['createdAt'] ?? '',
      updatedAt: map['updatedAt'] ?? '',
      url: map['url'] ?? '',
      slug: map['slug'] ?? '',
      status: map['status'] ?? '',
      title: map['title'] ?? '',
      excerpt: map['excerpt'] ?? '',
      content: map['content'] ?? '',
      featuredImage: map['featuredImage'] != null
          ? BlogPostImageData.fromMap(map['featuredImage'])
          : null,
      authorId: map['authorId'] ?? 0,
      authorNicename: map['authorNicename'] ?? '',
      authorDisplayName: map['authorDisplayName'] ?? '',
      authorAvatarUrl: map['authorAvatarUrl'] ?? '',
      categories: map['categories'] != null
          ? List<BlogPostCategoryData>.from(
              map['categories'].map((x) => BlogPostCategoryData.fromMap(x)),
            )
          : [],
    );
  }

  @override
  String toString() {
    return '''BlogPostData(
      id: $id,
      createdAt: $createdAt,
      updatedAt: $updatedAt,
      url: $url,
      slug: $slug,
      status: $status,
      title: $title,
      excerpt: $excerpt,
      content: $content,
      featuredImage: ${featuredImage.toString()},
      authorId: $authorId,
      authorNicename: $authorNicename,
      authorDisplayName: $authorDisplayName,
      authorAvatarUrl: $authorAvatarUrl,
      categories: $categories,
    )''';
  }
}

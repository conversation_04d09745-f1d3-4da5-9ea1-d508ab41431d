import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

final class CategoryPhotoListNotifier extends Notifier<List<PhotoData>> {
  final Map<int, int> _idIndexCache = {};

  @override
  List<PhotoData> build() => [];

  void _updateState(List<PhotoData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  PhotoData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  void addItem(PhotoData newItem) {
    if (hasItem(newItem.id)) return;
    final updated = [...state, newItem];
    _updateState(updated);
  }

  void addItems(List<PhotoData> newItems) {
    final existingIds = _idIndexCache.keys.toSet();
    final filtered = newItems.where((item) => !existingIds.contains(item.id));
    if (filtered.isEmpty) return;

    final updated = [...state, ...filtered];
    _updateState(updated);
  }

  void updateItem(PhotoData newItem) {
    final index = _idIndexCache[newItem.id];
    if (index == null || index >= state.length) return;

    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  void removeItem(int id) {
    if (!_idIndexCache.containsKey(id)) return;
    final updated = state.where((item) => item.id != id).toList();
    _updateState(updated);
  }

  void replaceAll(List<PhotoData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }
}

Map categoryPhotoListProviderMap = {};

NotifierProvider<CategoryPhotoListNotifier, List<PhotoData>>
getCategoryPhotoListProvider(String categorySlug) {
  if (categoryPhotoListProviderMap.containsKey(categorySlug)) {
    return categoryPhotoListProviderMap[categorySlug];
  }

  categoryPhotoListProviderMap[categorySlug] =
      NotifierProvider.autoDispose<CategoryPhotoListNotifier, List<PhotoData>>(
        CategoryPhotoListNotifier.new,
      );

  return categoryPhotoListProviderMap[categorySlug];
}

void deleteCategoryPhotoListProvider(String categorySlug) {
  if (categoryPhotoListProviderMap.containsKey(categorySlug)) {
    categoryPhotoListProviderMap.remove(categorySlug);
  }
}

void clearCategoryPhotoListProviders() {
  categoryPhotoListProviderMap.clear();
}

void removeCategoryPhotoListItem(WidgetRef ref, int photoId) {
  if (!ref.context.mounted) return;

  // Loop through categoryPhotoListProviderMap.
  for (final categorySlug in categoryPhotoListProviderMap.keys) {
    final NotifierProvider<CategoryPhotoListNotifier, List<PhotoData>>
    provider = categoryPhotoListProviderMap[categorySlug];

    ref.read(provider.notifier).removeItem(photoId);
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/enum.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';

class BlogPostImage extends StatelessWidget {
  final String imageUrl;
  final ImageSizing imageSizing;
  final double imageWidth;
  final double imageHeight;

  const BlogPostImage({
    super.key,
    required this.imageUrl,
    this.imageSizing = ImageSizing.fixedSize,
    required this.imageWidth,
    required this.imageHeight,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double containerWidth = constraints.maxWidth;

        late double containerHeight;

        if (imageSizing == ImageSizing.fixedSize) {
          containerHeight = imageHeight;
        } else {
          containerHeight = ImageSize.computedHeight(
            parentWidth: containerWidth,
            imageWidth: imageWidth,
            imageHeight: imageHeight,
          );
        }

        return Container(
          width: containerWidth,
          height: containerHeight,
          decoration: BoxDecoration(
            image: DecorationImage(
              image: PmNetworkImageProvider(imageUrl).imageProvider,
              fit: BoxFit.cover,
            ),
          ),
        );
      },
    );
  }
}

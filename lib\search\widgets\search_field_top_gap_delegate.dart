import 'package:flutter/material.dart';

class SearchFieldTopGapDelegate extends SliverPersistentHeaderDelegate {
  SearchFieldTopGapDelegate();

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return const SizedBox.shrink();
  }

  @override
  double get maxExtent => 46.0;

  @override
  double get minExtent => 46.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => false;
}

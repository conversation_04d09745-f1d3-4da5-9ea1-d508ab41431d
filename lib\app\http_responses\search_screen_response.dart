import 'package:portraitmode/app/dto/search_screen_data.dart';

class SearchScreenResponse {
  final bool success;
  final String errorCode;
  final String message;
  final SearchScreenData? data;

  SearchScreenResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory SearchScreenResponse.fromMap(Map<String, dynamic> map) {
    return SearchScreenResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? SearchScreenData.fromMap(map['data'])
          : null,
    );
  }
}

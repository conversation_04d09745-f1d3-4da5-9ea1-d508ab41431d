import 'package:flutter/material.dart';

import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/content_util.dart';

import 'package:portraitmode/photo/dto/photo_data.dart';

class PhotoDetailDescription extends StatelessWidget {
  final PhotoData photo;
  final EdgeInsets? padding;
  final bool hasDivider;
  final double contentToDividerGap;

  const PhotoDetailDescription({
    super.key,
    required this.photo,
    this.padding,
    this.hasDivider = true,
    this.contentToDividerGap = 8.0,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding != null ? padding! : const EdgeInsets.all(0.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            parseEmojiChars(photo.description),
            style: const TextStyle(fontSize: 13.0, height: 1.5),
          ),
          if (hasDivider) SizedBox(height: contentToDividerGap),
          if (hasDivider)
            Divider(height: 1.0, color: context.colors.borderColor),
        ],
      ),
    );
  }
}

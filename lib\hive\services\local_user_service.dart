import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';

class LocalUserService {
  static LocalUserService? _instance;

  static LocalUserService get instance =>
      _instance ??= LocalUserService._internal();

  // Private constructor
  LocalUserService._internal();

  // Cache boxes to avoid repeated box access
  static Box? _userBox;

  // Cache for current settings to avoid frequent Hive reads
  LocalUserData? _cachedData;

  // Constants for keys to avoid string allocation
  static const _userIdKey = 'userId';
  static const _nicenameKey = 'nicename';
  static const _roleKey = 'role';
  static const _displayNameKey = 'displayName';
  static const _profileUrlKey = 'profileUrl';
  static const _avatarUrlKey = 'avatarUrl';
  static const _membershipTypeKey = 'membershipType';

  // Lazy initialization of boxes
  Box get _userBoxInstance {
    return _userBox ??= Hive.box('user');
  }

  LocalUserData _get() {
    // Return cached data if available and still valid
    if (_cachedData != null) {
      return _cachedData!;
    }

    // Batch read operations to minimize Hive access
    final userIdFromDb = _userBoxInstance.get(_userIdKey, defaultValue: null);

    final nicenameFromDb = _userBoxInstance.get(
      _nicenameKey,
      defaultValue: null,
    );

    final roleFromDb = _userBoxInstance.get(_roleKey, defaultValue: null);

    final displayNameFromDb = _userBoxInstance.get(
      _displayNameKey,
      defaultValue: null,
    );

    final profileUrlFromDb = _userBoxInstance.get(
      _profileUrlKey,
      defaultValue: null,
    );

    final avatarUrlFromDb = _userBoxInstance.get(
      _avatarUrlKey,
      defaultValue: null,
    );

    final membershipTypeFromDb = _userBoxInstance.get(
      _membershipTypeKey,
      defaultValue: null,
    );

    _cachedData = LocalUserData(
      userId: userIdFromDb is int ? userIdFromDb : null,
      nicename: nicenameFromDb is String ? nicenameFromDb : null,
      role: roleFromDb is String ? roleFromDb : null,
      displayName: displayNameFromDb is String ? displayNameFromDb : null,
      profileUrl: profileUrlFromDb is String ? profileUrlFromDb : null,
      avatarUrl: avatarUrlFromDb is String ? avatarUrlFromDb : null,
      membershipType: membershipTypeFromDb is String
          ? membershipTypeFromDb
          : null,
    );

    return _cachedData!;
  }

  int? get _userId => _get().userId;

  String? get _nicename => _get().nicename;

  String? get _role => _get().role;

  String? get _displayName => _get().displayName;

  String? get _profileUrl => _get().profileUrl;

  String? get _avatarUrl => _get().avatarUrl;

  String? get _membershipType => _get().membershipType;

  Future<void> _setUserId(int? value) async {
    await _userBoxInstance.put(_userIdKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.userId = value;
  }

  Future<void> _setNicename(String? value) async {
    await _userBoxInstance.put(_nicenameKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.nicename = value;
  }

  Future<void> _setRole(String? value) async {
    await _userBoxInstance.put(_roleKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.role = value;
  }

  Future<void> _setDisplayName(String? value) async {
    await _userBoxInstance.put(_displayNameKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.displayName = value;
  }

  Future<void> _setProfileUrl(String? value) async {
    await _userBoxInstance.put(_profileUrlKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.profileUrl = value;
  }

  Future<void> _setAvatarUrl(String? value) async {
    await _userBoxInstance.put(_avatarUrlKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.avatarUrl = value;
  }

  Future<void> _setMembershipType(String? value) async {
    await _userBoxInstance.put(_membershipTypeKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.membershipType = value;
  }

  Future<LocalUserData> _replace(LocalUserData newData) async {
    _invalidateCache();

    await _userBoxInstance.putAll({
      _userIdKey: newData.userId,
      _nicenameKey: newData.nicename,
      _roleKey: newData.role,
      _displayNameKey: newData.displayName,
      _profileUrlKey: newData.profileUrl,
      _avatarUrlKey: newData.avatarUrl,
      _membershipTypeKey: newData.membershipType,
    });

    _cachedData = newData;

    return newData;
  }

  // Explicit cache invalidation method
  void _invalidateCache() {
    _cachedData = null;
  }

  Future<void> _destroy() async {
    await _userBoxInstance.deleteAll(_userBoxInstance.keys);
    _invalidateCache();
  }

  // Cleanup method to be called when app is terminated (just in case needed)
  void _dispose() {
    _cachedData = null;
    _userBox = null;
  }

  // --------------------------------------------------
  // Static convenience methods that delegate to singleton instance
  // --------------------------------------------------

  static LocalUserData get() => instance._get();

  static int? get userId => instance._userId;

  static String? get nicename => instance._nicename;

  static String? get role => instance._role;

  static String? get displayName => instance._displayName;

  static String? get profileUrl => instance._profileUrl;

  static String? get avatarUrl => instance._avatarUrl;

  static String? get membershipType => instance._membershipType;

  static Future<void> setUserId(int? value) => instance._setUserId(value);

  static Future<void> setNicename(String? value) =>
      instance._setNicename(value);

  static Future<void> setRole(String? value) => instance._setRole(value);

  static Future<void> setDisplayName(String? value) =>
      instance._setDisplayName(value);

  static Future<void> setProfileUrl(String? value) =>
      instance._setProfileUrl(value);

  static Future<void> setAvatarUrl(String? value) =>
      instance._setAvatarUrl(value);

  static Future<void> setMembershipType(String? value) =>
      instance._setMembershipType(value);

  static Future<LocalUserData> replace(LocalUserData newData) =>
      instance._replace(newData);

  static void invalidateCache() => instance._invalidateCache();

  static Future<void> destroy() => instance._destroy();

  static void dispose() => instance._dispose();
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';

class ReplyTo extends ConsumerStatefulWidget {
  final String name;
  final bool hasCloseButton;
  final VoidCallback? onClose;

  const ReplyTo({
    super.key,
    required this.name,
    this.hasCloseButton = true,
    this.onClose,
  });

  @override
  ReplyToState createState() => ReplyToState();
}

class ReplyToState extends ConsumerState<ReplyTo> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: ScreenStyleConfig.horizontalPadding,
            vertical: 15.0,
          ),
          // Add border top.
          decoration: BoxDecoration(
            border: Border(
              top: BorderSide(color: context.colors.baseColor, width: 0.5),
            ),
            color: context.colors.baseColorAlt,
          ),
          child: Row(
            children: [
              const Text(
                "Replying to",
                style: TextStyle(fontSize: 13.0, fontWeight: FontWeight.w400),
              ),
              const SizedBox(width: 5.0),
              Text(
                widget.name,
                style: const TextStyle(
                  fontSize: 13.0,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
        if (widget.hasCloseButton) _buildCloseReplyTo(),
      ],
    );
  }

  Widget _buildCloseReplyTo() {
    return Positioned(
      top: 0.0,
      right: 0.0,
      child: IconButton(
        onPressed: () async {
          ref.read(commentActivityProvider.notifier).setCommentIdToReply(null);

          if (widget.onClose != null) {
            widget.onClose!();
          }
        },
        icon: const Icon(Icons.close, size: 16.0),
      ),
    );
  }
}

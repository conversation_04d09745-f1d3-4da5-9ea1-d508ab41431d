import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/services/auth_service.dart';

void showSessionEndedDialog(BuildContext context, WidgetRef ref) {
  if (!context.mounted) return;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        content: Text("Sorry, your session has expired. Please log in again."),
        actions: <Widget>[
          TextButton(
            child: const Text("Close"),
            onPressed: () async {
              await AuthService.logout(context, ref);
            },
          ),
        ],
      );
    },
  );
}

void showErrorDialog(
  BuildContext context,
  WidgetRef ref, {
  String? title,
  required String message,
  Function? onClose,
}) {
  if (!context.mounted) return;

  final String lowerCaseMsg = message.toLowerCase();

  // Users are not supposted to see the "Expired token" dialog.
  // Because "Expired token" means that the accessToken is expired.
  // It's wrong, because the accessToken should be refreshed automatically.
  //
  // There are some reports where people get into this dialog.
  // And when they are fall into this dialog, they were not able to re-login.
  // So patch is added to handle that case, to let them to re-login.
  final bool isUnexpected =
      lowerCaseMsg == "expired token" || lowerCaseMsg == "token expired";

  final String parsedMessage = isUnexpected
      ? "Sorry, your session has expired. Please log in again. We're aware of this issue and are working on a fix. Thank you for your patience!"
      : message;

  showDialog(
    context: context,
    builder: (BuildContext context) {
      return AlertDialog(
        title: title != null ? Text(title) : null,
        content: title == null
            ? Padding(
                padding: EdgeInsets.only(top: 12),
                child: Text(parsedMessage),
              )
            : Text(parsedMessage),
        actions: <Widget>[
          TextButton(
            child: const Text("Close"),
            onPressed: () async {
              if (isUnexpected) {
                await AuthService.logout(context, ref);
                return;
              }

              Navigator.of(context).pop();
              onClose?.call();
            },
          ),
        ],
      );
    },
  );
}

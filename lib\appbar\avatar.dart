import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';

class Avatar extends StatelessWidget {
  const Avatar({
    super.key,
    required this.imageUrl,
    this.size = 25.0,
    this.useBorder = false,
    this.borderColor = Colors.white,
    this.borderWidth = 1.5,
    this.onTap,
    this.isOwnAvatar = false,
  });

  final String imageUrl;
  final double size;
  final bool useBorder;
  final Color borderColor;
  final double borderWidth;
  final Function? onTap;
  final bool isOwnAvatar;

  @override
  Widget build(BuildContext context) {
    return (onTap == null
        ? _buildAvatar()
        : GestureDetector(onTap: () => onTap!(), child: _buildAvatar()));
  }

  Widget _buildAvatar() {
    final String avatarUrl = imageUrl.isEmpty
        ? AvatarConfig.defaultAvatar
        : imageUrl;

    return SizedBox(
      height: size,
      width: size,
      child: CircleAvatar(
        backgroundColor: useBorder ? borderColor : null,
        child: ClipRRect(
          borderRadius: BorderRadius.circular(size / 2),
          child: PmNetworkImage(
            url: avatarUrl,
            width: useBorder ? size - (borderWidth * 2) : size,
            height: useBorder ? size - (borderWidth * 2) : size,
          ),
        ),
      ),
    );
  }
}

import 'package:portraitmode/blog/dto/blog_post_data.dart';

class BlogPostFindResponse {
  final bool success;
  final String errorCode;
  final String message;
  final BlogPostData? data;

  BlogPostFindResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory BlogPostFindResponse.fromMap(Map<String, dynamic> map) {
    return BlogPostFindResponse(
      success: map['success'] ?? true,
      errorCode: map['error_code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? BlogPostData.fromMap(map['data'])
          : null,
    );
  }
}

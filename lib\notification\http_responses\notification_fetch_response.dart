import 'package:portraitmode/notification/dto/notification_fetch_result.dart';

class NotificationFetchResponse {
  final bool success;
  final String errorCode;
  final String message;
  final NotificationFetchResult? data;

  NotificationFetchResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory NotificationFetchResponse.fromMap(Map<String, dynamic> map) {
    NotificationFetchResult? mapData = map['data'] != null && map['data'] is Map
        ? NotificationFetchResult.fromMap(map['data'])
        : null;

    return NotificationFetchResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: mapData,
    );
  }
}

import 'dart:convert';

class AuthData {
  final String accessToken;
  final String refreshToken;
  final int id;
  final String nicename;
  final String role;
  final String email;
  final String firstName;
  final String lastName;
  final String displayName;
  final String description;
  final String website;
  final String instagram;
  final String profileUrl;
  final String avatarUrl;
  final String location;
  final String latestPhotoUrl;
  final int totalPhotos;
  final String camera;
  final String focalLength;
  bool isFollowing;
  int totalFollowing;
  int totalFollowers;
  final String membershipType;

  AuthData({
    this.accessToken = '',
    this.refreshToken = '',
    this.id = 0,
    this.nicename = '',
    this.role = 'subscriber',
    this.email = '',
    this.website = '',
    this.instagram = '',
    this.profileUrl = '',
    this.avatarUrl = '',
    this.firstName = '',
    this.lastName = '',
    this.displayName = '',
    this.description = '',
    this.location = '',
    this.latestPhotoUrl = '',
    this.totalPhotos = 0,
    this.camera = '',
    this.focalLength = '',
    this.isFollowing = false,
    this.totalFollowers = 0,
    this.totalFollowing = 0,
    this.membershipType = '',
  });

  AuthData copyWith({
    String? accessToken,
    String? refreshToken,
    int? id,
    String? nicename,
    String? role,
    String? email,
    String? firstName,
    String? lastName,
    String? displayName,
    String? description,
    String? website,
    String? instagram,
    String? profileUrl,
    String? avatarUrl,
    String? location,
    String? latestPhotoUrl,
    int? totalPhotos,
    String? camera,
    String? focalLength,
    bool? isFollowing,
    int? totalFollowing,
    int? totalFollowers,
    String? membershipType,
  }) {
    return AuthData(
      accessToken: accessToken ?? this.accessToken,
      refreshToken: refreshToken ?? this.refreshToken,
      id: id ?? this.id,
      nicename: nicename ?? this.nicename,
      role: role ?? this.role,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      website: website ?? this.website,
      instagram: instagram ?? this.instagram,
      profileUrl: profileUrl ?? this.profileUrl,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      location: location ?? this.location,
      latestPhotoUrl: latestPhotoUrl ?? this.latestPhotoUrl,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      camera: camera ?? this.camera,
      focalLength: focalLength ?? this.focalLength,
      isFollowing: isFollowing ?? this.isFollowing,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      membershipType: membershipType ?? this.membershipType,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'accessToken': accessToken,
      'refreshToken': refreshToken,
      'id': id,
      'nicename': nicename,
      'role': role,
      'email': email,
      'firstName': firstName,
      'lastName': lastName,
      'displayName': displayName,
      'profileUrl': profileUrl,
      'description': description,
      'website': website,
      'instagram': instagram,
      'avatarUrl': avatarUrl,
      'location': location,
      'latestPhotoUrl': latestPhotoUrl,
      'totalPhotos': totalPhotos,
      'camera': camera,
      'focalLength': focalLength,
      'isFollowing': isFollowing,
      'totalFollowing': totalFollowing,
      'totalFollowers': totalFollowers,
      'membershipType': membershipType,
    };
  }

  factory AuthData.fromMap(Map<String, dynamic> data) {
    return AuthData(
      accessToken: data['accessToken'] ?? '',
      refreshToken: data['refreshToken'] ?? '',
      id: data['id'] ?? 0,
      nicename: data['nicename'] ?? '',
      role: data['role'] ?? 'subscriber',
      email: data['email'] ?? '',
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      displayName: data['displayName'] ?? '',
      description: data['description'] ?? '',
      website: data['website'] ?? '',
      instagram: data['instagram'] ?? '',
      profileUrl: data['profileUrl'] ?? '',
      avatarUrl: data['avatarUrl'] ?? '',
      location: data['location'] ?? '',
      latestPhotoUrl: data['latestPhotoUrl'] ?? '',
      totalPhotos: data['totalPhotos'] ?? 0,
      camera: data['camera'] ?? '',
      focalLength: data['focalLength'] ?? '',
      isFollowing: data['isFollowing'] ?? false,
      totalFollowing: data['totalFollowing'] ?? 0,
      totalFollowers: data['totalFollowers'] ?? 0,
      membershipType: data['membershipType'] ?? '',
    );
  }

  factory AuthData.fromJson(String source) =>
      AuthData.fromMap(json.decode(source));
}

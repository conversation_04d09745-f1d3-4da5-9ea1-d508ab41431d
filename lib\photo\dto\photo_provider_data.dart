import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

@immutable
final class PhotoProviderData {
  final int id;
  final bool? featured;
  final bool? potd;
  final bool? isLiked;
  final int? totalLikes;
  final int? totalComments;

  const PhotoProviderData({
    required this.id,
    this.featured,
    this.potd,
    this.isLiked,
    this.totalLikes,
    this.totalComments,
  });

  PhotoProviderData copyWith({
    int? id,
    bool? featured,
    bool? potd,
    bool? isLiked,
    int? totalLikes,
    int? totalComments,
  }) {
    return PhotoProviderData(
      id: id ?? this.id,
      featured: featured ?? this.featured,
      potd: potd ?? this.potd,
      isLiked: isLiked ?? this.isLiked,
      totalLikes: totalLikes ?? this.totalLikes,
      totalComments: totalComments ?? this.totalComments,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'featured': featured,
      'potd': potd,
      'isLiked': isLiked,
      'totalLikes': totalLikes,
      'totalComments': totalComments,
    };
  }

  factory PhotoProviderData.fromPhotoData(PhotoData photo) {
    return PhotoProviderData(
      id: photo.id,
      featured: photo.featured,
      potd: photo.potd,
      isLiked: photo.isLiked,
      totalLikes: photo.totalLikes,
      totalComments: photo.totalComments,
    );
  }

  factory PhotoProviderData.fromMap(Map<String, dynamic> data) {
    return PhotoProviderData(
      id: data['id'],
      featured: data['featured'] ?? false,
      potd: data['potd'] ?? false,
      isLiked: data['isLiked'] ?? false,
      totalLikes: data['totalLikes'] ?? 0,
      totalComments: data['totalComments'] ?? 0,
    );
  }

  factory PhotoProviderData.fromJson(String source) =>
      PhotoProviderData.fromMap(jsonDecode(source));

  @override
  int get hashCode =>
      Object.hash(id, featured, potd, isLiked, totalLikes, totalComments);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PhotoProviderData) return false;

    return other.id == id &&
        other.featured == featured &&
        other.potd == potd &&
        other.isLiked == isLiked &&
        other.totalLikes == totalLikes &&
        other.totalComments == totalComments;
  }

  @override
  String toString() {
    return '''
PhotoProviderData(
  id: $id,
  featured: $featured,
  potd: $potd,
  isLiked: $isLiked,
  totalLikes: $totalLikes,
  totalComments: $totalComments,
)
''';
  }
}

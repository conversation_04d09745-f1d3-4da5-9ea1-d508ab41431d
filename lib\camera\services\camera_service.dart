import 'package:dio/dio.dart';

import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/camera/http_responses/camera_response.dart';

class CameraService extends BaseService {
  Future<CameraResponse> find(String cameraName) async {
    try {
      final response = await http.get('${URL.baseApiUrl}/camera/$cameraName');

      return CameraResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CameraResponse.fromMap(e.response?.data);
      }

      return CameraResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return CameraResponse(success: false, message: "Something went wrong.");
    }
  }
}

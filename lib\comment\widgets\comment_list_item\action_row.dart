import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/providers/comment_store_provider.dart';
import 'package:portraitmode/comment/utils/comment_form_util.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/like_comment_button.dart';
import 'package:timeago/timeago.dart' as timeago;

class ActionRow extends ConsumerWidget {
  const ActionRow({
    super.key,
    required this.commentFieldController,
    required this.commentFieldFocusNode,
    required this.commentContext,
    required this.comment,
  });

  final TextEditingController commentFieldController;
  final FocusNode commentFieldFocusNode;
  final BuildContext commentContext;
  final CommentData comment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isLiked =
        ref.watch(commentProvider(comment.id)).isLiked ?? comment.isLiked;

    final totalLikes =
        ref.watch(commentProvider(comment.id)).totalLikes ?? comment.totalLikes;

    DateTime commentDate = DateTime.parse(comment.dateGmt);
    // @see https://github.com/andresaraujo/timeago.dart/pull/142#issuecomment-859661123
    commentDate = commentDate.add(commentDate.timeZoneOffset).toUtc();

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 14.0),
          child: Text(
            timeago.format(commentDate),
            style: TextStyle(
              fontSize: 12.0,
              color: context.isDarkMode
                  ? AppColorsCache.dark().darkerGreyColor
                  : AppColorsCache.light().primarySwatch[300],
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(right: 14.0),
          child: InkWell(
            onTap: () {
              _handleReplyLinkTap(ref);
            },
            child: Text(
              'Reply',
              style: TextStyle(
                fontSize: 12.0,
                color: context.colors.contentLighterColor,
              ),
            ),
          ),
        ),
        const Expanded(child: SizedBox.shrink()),
        Padding(
          padding: const EdgeInsets.only(right: 12.0),
          child: LikeCommentButton(
            commentId: comment.id,
            isLiked: isLiked,
            totalLikes: totalLikes,
          ),
        ),
      ],
    );
  }

  void _handleReplyLinkTap(WidgetRef ref) async {
    final bool isReply = comment.parentId != 0;
    int? commentIdToReply;

    // If clicking a reply link of a reply (not the reply link of a top-level comment).
    if (isReply) {
      final int? expandedCommentId = ref
          .read(commentActivityProvider)
          .expandedCommentId;

      if (expandedCommentId != null) {
        commentIdToReply = expandedCommentId;
      }
    } else {
      commentIdToReply = comment.id;
    }

    String mention = '@${comment.authorNicename}';

    final String fieldText = replaceFirstMention(
      mention,
      commentFieldController.text,
    );

    if (commentFieldController.text != fieldText) {
      commentFieldController.text = fieldText;
    }

    commentFieldController.selection = TextSelection.fromPosition(
      TextPosition(offset: fieldText.length),
    );

    ref
        .read(commentActivityProvider.notifier)
        .update(
          formMode: CommentFormMode.add,
          commentIdToReply: commentIdToReply,

          /// We don't need to set `expandedComment` if we're replying to a reply.
          /// Because when replying to a reply means the parent comment is already expanded.
          expandedCommentId: isReply ? null : comment.id,
        );

    commentFieldFocusNode.requestFocus();

    await Scrollable.ensureVisible(
      commentContext,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      alignment: 0.0,
    );
  }
}

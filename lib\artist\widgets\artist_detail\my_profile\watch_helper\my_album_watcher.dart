import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';

final class MyAlbumWatcher extends ConsumerWidget {
  const MyAlbumWatcher({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List<AlbumData> albumList = ref.watch(myAlbumProvider);

    return albumList.isNotEmpty
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}

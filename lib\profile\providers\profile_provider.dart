import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';

final class ProfileNotifier extends Notifier<ProfileData> {
  @override
  ProfileData build() => const ProfileData();

  void setTotalFollowing(int totalFollowing) {
    state = state.copyWith(totalFollowing: totalFollowing);
  }

  dynamic getProp(String prop) {
    return state.toMap()[prop];
  }

  void setProp(String prop, dynamic value) {
    if (value == null) return;

    switch (prop) {
      case 'id':
        state = state.copyWith(id: value);
        break;
      case 'nicename':
        state = state.copyWith(nicename: value);
        break;
      case 'role':
        state = state.copyWith(role: value);
        break;
      case 'email':
        state = state.copyWith(email: value);
        break;
      case 'website':
        state = state.copyWith(website: value);
        break;
      case 'instagram':
        state = state.copyWith(instagram: value);
        break;
      case 'profileUrl':
        state = state.copyWith(profileUrl: value);
        break;
      case 'avatarUrl':
        state = state.copyWith(avatarUrl: value);
        break;
      case 'firstName':
        state = state.copyWith(firstName: value);
        break;
      case 'lastName':
        state = state.copyWith(lastName: value);
        break;
      case 'displayName':
        state = state.copyWith(displayName: value);
        break;
      case 'description':
        state = state.copyWith(description: value);
        break;
      case 'location':
        state = state.copyWith(location: value);
        break;
      case 'latestPhotoUrl':
        state = state.copyWith(latestPhotoUrl: value);
        break;
      case 'totalPhotos':
        state = state.copyWith(totalPhotos: value);
        break;
      case 'camera':
        state = state.copyWith(camera: value);
        break;
      case 'focalLength':
        state = state.copyWith(focalLength: value);
        break;
      case 'totalFollowers':
        state = state.copyWith(totalFollowers: value);
        break;
      case 'totalFollowing':
        state = state.copyWith(totalFollowing: value);
        break;
      case 'membershipType':
        state = state.copyWith(membershipType: value);
        break;
    }
  }

  void setProps(Map<String, dynamic> props) {
    props.forEach((key, value) {
      setProp(key, value);
    });
  }

  void replace(ProfileData data) {
    state = data;
  }

  void reset() {
    state = const ProfileData();
  }
}

final profileProvider =
    NotifierProvider.autoDispose<ProfileNotifier, ProfileData>(
      ProfileNotifier.new,
    );

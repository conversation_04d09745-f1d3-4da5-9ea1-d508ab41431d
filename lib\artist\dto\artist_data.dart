import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/album/dto/album_data.dart';
import 'package:portraitmode/app/config/config.dart';

@immutable
final class ArtistData {
  final int id;
  final String nicename;
  final String email;
  final String role;
  final String website;
  final String profileUrl;
  final String avatarUrl;
  final String firstName;
  final String lastName;
  final String displayName;
  final String description;
  final String location;
  final String latestPhotoUrl;
  final int totalPhotos;
  final String camera;
  final String focalLength;
  final bool isFollowing;
  final int totalFollowing;
  final int totalFollowers;

  /// Whether this artist is blocked by the current user
  final bool isBlocked;

  /// Whether this artist is blocking the current user
  final bool isBlocking;
  final String membershipType;
  final List<AlbumData>? albums;

  const ArtistData({
    this.id = 0,
    this.nicename = '',
    this.email = '',
    this.role = 'subscriber',
    this.website = '',
    this.profileUrl = '',
    this.avatarUrl = '',
    this.firstName = '',
    this.lastName = '',
    this.displayName = '',
    this.description = '',
    this.location = '',
    this.latestPhotoUrl = '',
    this.totalPhotos = 0,
    this.camera = '',
    this.focalLength = '',
    this.isFollowing = false,
    this.totalFollowing = 0,
    this.totalFollowers = 0,
    this.isBlocked = false,
    this.isBlocking = false,
    this.membershipType = '',
    this.albums,
  });

  ArtistData copyWith({
    int? id,
    String? nicename,
    String? email,
    String? role,
    String? website,
    String? profileUrl,
    String? avatarUrl,
    String? firstName,
    String? lastName,
    String? displayName,
    String? description,
    String? location,
    String? latestPhotoUrl,
    int? totalPhotos,
    String? camera,
    String? focalLength,
    bool? isFollowing,
    int? totalFollowing,
    int? totalFollowers,
    bool? isBlocked,
    bool? isBlocking,
    String? membershipType,
    List<AlbumData>? albums,
  }) {
    return ArtistData(
      id: id ?? this.id,
      nicename: nicename ?? this.nicename,
      email: email ?? this.email,
      role: role ?? this.role,
      website: website ?? this.website,
      profileUrl: profileUrl ?? this.profileUrl,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      displayName: displayName ?? this.displayName,
      description: description ?? this.description,
      location: location ?? this.location,
      latestPhotoUrl: latestPhotoUrl ?? this.latestPhotoUrl,
      totalPhotos: totalPhotos ?? this.totalPhotos,
      camera: camera ?? this.camera,
      focalLength: focalLength ?? this.focalLength,
      isFollowing: isFollowing ?? this.isFollowing,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      isBlocked: isBlocked ?? this.isBlocked,
      isBlocking: isBlocking ?? this.isBlocking,
      membershipType: membershipType ?? this.membershipType,
      albums: albums ?? this.albums,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nicename': nicename,
      'email': email,
      'role': role,
      'website': website,
      'profileUrl': profileUrl,
      'avatarUrl': avatarUrl,
      'firstName': firstName,
      'lastName': lastName,
      'displayName': displayName,
      'description': description,
      'location': location,
      'latestPhotoUrl': latestPhotoUrl,
      'totalPhotos': totalPhotos,
      'camera': camera,
      'focalLength': focalLength,
      'isFollowing': isFollowing,
      'totalFollowing': totalFollowing,
      'totalFollowers': totalFollowers,
      'isBlocked': isBlocked,
      'isBlocking': isBlocking,
      'membershipType': membershipType,
      'albums': albums,
    };
  }

  factory ArtistData.fromMap(Map<String, dynamic> data) {
    return ArtistData(
      id: data['id'] ?? 0,
      nicename: data['nicename'] ?? '',
      email: data['email'] ?? '',
      role: data['role'] ?? 'subscriber',
      website: data['website'] ?? '',
      profileUrl: data['profileUrl'] ?? '',
      avatarUrl: data['avatarUrl'] ?? AvatarConfig.defaultAvatar,
      firstName: data['firstName'] ?? '',
      lastName: data['lastName'] ?? '',
      displayName: data['displayName'] ?? '',
      description: data['description'] ?? '',
      location: data['location'] ?? '',
      latestPhotoUrl: data['latestPhotoUrl'] ?? '',
      totalPhotos: data['totalPhotos'] ?? 0,
      camera: data['camera'] ?? '',
      focalLength: data['focalLength'] ?? '',
      isFollowing: data['isFollowing'] ?? false,
      totalFollowing: data['totalFollowing'] ?? 0,
      totalFollowers: data['totalFollowers'] ?? 0,
      isBlocked: data['isBlocked'] ?? false,
      isBlocking: data['isBlocking'] ?? false,
      membershipType: data['membershipType'] ?? '',
      albums: data['albums'] != null && data['albums'] is List
          ? data['albums']
                .map<AlbumData>((album) => AlbumData.fromMap(album))
                .toList()
          : null,
    );
  }

  factory ArtistData.fromJson(String source) =>
      ArtistData.fromMap(json.decode(source));

  @override
  int get hashCode {
    return Object.hashAll([
      id,
      nicename,
      email,
      role,
      website,
      profileUrl,
      avatarUrl,
      firstName,
      lastName,
      displayName,
      description,
      location,
      latestPhotoUrl,
      totalPhotos,
      camera,
      focalLength,
      isFollowing,
      totalFollowing,
      totalFollowers,
      isBlocked,
      isBlocking,
      membershipType,
      albums == null ? null : Object.hashAll(albums!),
    ]);
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ArtistData) return false;

    return other.id == id &&
        other.nicename == nicename &&
        other.email == email &&
        other.role == role &&
        other.website == website &&
        other.profileUrl == profileUrl &&
        other.avatarUrl == avatarUrl &&
        other.firstName == firstName &&
        other.lastName == lastName &&
        other.displayName == displayName &&
        other.description == description &&
        other.location == location &&
        other.latestPhotoUrl == latestPhotoUrl &&
        other.totalPhotos == totalPhotos &&
        other.camera == camera &&
        other.focalLength == focalLength &&
        other.isFollowing == isFollowing &&
        other.totalFollowing == totalFollowing &&
        other.totalFollowers == totalFollowers &&
        other.isBlocked == isBlocked &&
        other.isBlocking == isBlocking &&
        other.membershipType == membershipType &&
        listEquals(other.albums, albums);
  }

  @override
  String toString() {
    return '''
ArtistData(
  id: $id,
  nicename: '$nicename',
  email: '$email',
  role: '$role',
  website: '$website',
  profileUrl: '$profileUrl',
  avatarUrl: '$avatarUrl',
  firstName: '$firstName',
  lastName: '$lastName',
  displayName: '$displayName',
  description: '$description',
  location: '$location',
  latestPhotoUrl: '$latestPhotoUrl',
  totalPhotos: '$totalPhotos',
  camera: '$camera',
  focalLength: $focalLength,
  isFollowing: $isFollowing,
  totalFollowing: $totalFollowing,
  totalFollowers: $totalFollowers,
  isBlocked: $isBlocked,
  isBlocking: $isBlocking,
  membershipType: '$membershipType',
  albums: $albums,
)
''';
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class DeletingIndicator extends StatefulWidget {
  const DeletingIndicator({super.key, this.onRetry});

  final void Function()? onRetry;

  @override
  DeletingIndicatorState createState() => DeletingIndicatorState();
}

class DeletingIndicatorState extends State<DeletingIndicator> {
  @override
  Widget build(BuildContext context) {
    return Text(
      'Deleting...',
      style: TextStyle(fontSize: 12.0, color: context.colors.darkerGreyColor),
    );
  }
}

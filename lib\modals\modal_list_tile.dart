import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/misc/loading_indicator.dart';

class ModalListTile extends StatelessWidget {
  const ModalListTile({
    super.key,
    required this.title,
    this.iconData,
    this.svgAssetPath,
    this.iconColor,
    this.textColor,
    this.fontSize = 16.0,
    this.isLoading = false,
    required this.onTap,
  });

  final String title;
  final IconData? iconData;
  final String? svgAssetPath;
  final Color? iconColor;
  final Color? textColor;
  final double fontSize;
  final bool isLoading;
  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    Widget icon = const SizedBox.shrink();

    if (iconData != null) {
      icon = Icon(
        size: 18.0,
        iconData!,
        color: iconColor ?? context.colors.brandColor,
      );
    } else if (svgAssetPath != null) {
      icon = SvgPicture.asset(
        svgAssetPath!,
        colorFilter: ColorFilter.mode(
          iconColor ?? context.colors.brandColor,
          BlendMode.srcIn,
        ),
        width: 18.0,
        height: 18.0,
      );
    }

    return ListTile(
      dense: true,
      visualDensity: const VisualDensity(horizontal: 1.0, vertical: -2.0),
      horizontalTitleGap: 8.0,
      minVerticalPadding: 0.0,
      minLeadingWidth: 20.0,
      leading: isLoading
          ? const LoadingIndicator(size: 14.0, centered: false)
          : icon,
      title: Text(
        title,
        style: TextStyle(
          color: textColor ?? context.colors.brandColor,
          fontSize: fontSize,
        ),
      ),
      onTap: () {
        onTap?.call();
      },
    );
  }
}

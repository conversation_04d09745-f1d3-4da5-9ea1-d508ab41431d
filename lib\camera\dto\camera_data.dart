import 'dart:convert';

import 'package:flutter/foundation.dart';

@immutable
class CameraData {
  final int id;
  final int latestPhotoId;
  final String name;
  final String aliasedName;
  final String slug;
  final String url;
  final int totalPhotos;
  final String latestPhotoUrl;

  const CameraData({
    this.id = 0,
    this.latestPhotoId = 0,
    this.name = '',
    this.aliasedName = '',
    this.slug = '',
    this.url = '',
    this.totalPhotos = 0,
    this.latestPhotoUrl = '',
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'latestPhotoId': latestPhotoId,
      'name': name,
      'aliasedName': aliasedName,
      'slug': slug,
      'url': url,
      'totalPhotos': totalPhotos,
      'latestPhotoUrl': latestPhotoUrl,
    };
  }

  factory CameraData.fromMap(Map<String, dynamic> map) {
    return CameraData(
      id: map['id'],
      latestPhotoId: map['latestPhotoId'],
      name: map['name'],
      aliasedName: map['aliasedName'],
      slug: map['slug'],
      url: map['url'],
      totalPhotos: map['totalPhotos'],
      latestPhotoUrl: map['latestPhotoUrl'],
    );
  }

  factory CameraData.fromJson(String source) =>
      CameraData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(
    id,
    latestPhotoId,
    name,
    aliasedName,
    slug,
    url,
    totalPhotos,
    latestPhotoUrl,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CameraData) return false;

    return other.id == id &&
        other.latestPhotoId == latestPhotoId &&
        other.name == name &&
        other.aliasedName == aliasedName &&
        other.slug == slug &&
        other.url == url &&
        other.totalPhotos == totalPhotos &&
        other.latestPhotoUrl == latestPhotoUrl;
  }
}

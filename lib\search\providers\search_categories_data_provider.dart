import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/search/dto/search_data.dart';

final class SearchCategoriesDataNotifier
    extends Notifier<CategoriesSearchData> {
  @override
  CategoriesSearchData build() => const CategoriesSearchData();

  void setOffset(int offset) {
    if (state.offset == offset) return;
    state = state.copyWith(offset: offset);
  }

  void setLoadMoreEndReached(bool loadMoreEndReached) {
    if (state.loadMoreEndReached == loadMoreEndReached) return;
    state = state.copyWith(loadMoreEndReached: loadMoreEndReached);
  }

  void replace(CategoriesSearchData data) {
    state = data;
  }

  void reset() {
    state = const CategoriesSearchData();
  }
}

final searchCategoriesDataProvider =
    NotifierProvider.autoDispose<
      SearchCategoriesDataNotifier,
      CategoriesSearchData
    >(SearchCategoriesDataNotifier.new);

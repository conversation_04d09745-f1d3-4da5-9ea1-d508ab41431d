import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';

class PhotoViewerScreen extends StatelessWidget {
  final String photoUrl;
  final double height;

  const PhotoViewerScreen({
    super.key,
    required this.photoUrl,
    required this.height,
  });

  final Color _scaffoldColor = const Color.fromRGBO(8, 7, 7, 1.0);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        automaticallyImplyLeading: true,
        backgroundColor: AppColorsCache.light().brandColor,
        useLogo: false,
        actions: const [],
      ),
      backgroundColor: _scaffoldColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        alignment: Alignment.center,
        padding: const EdgeInsets.only(bottom: LayoutConfig.bottomNavBarHeight),
        child: _buildPhoto(),
      ),
    );
  }

  Widget _buildPhoto() {
    return PhotoView(
      imageProvider: PmNetworkImageProvider(photoUrl).imageProvider,
      minScale: PhotoViewComputedScale.contained,
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/search/dto/search_data.dart';

final class SearchArtistsDataNotifier extends Notifier<ArtistsSearchData> {
  @override
  ArtistsSearchData build() => const ArtistsSearchData();

  void setLoadMoreLastId(int loadMoreLastId) {
    if (state.loadMoreLastId == loadMoreLastId) return;
    state = state.copyWith(loadMoreLastId: loadMoreLastId);
  }

  void setLastTotalPhotos(int lastTotalPhotos) {
    if (state.lastTotalPhotos == lastTotalPhotos) return;
    state = state.copyWith(lastTotalPhotos: lastTotalPhotos);
  }

  void setLoadMoreEndReached(bool loadMoreEndReached) {
    if (state.loadMoreEndReached == loadMoreEndReached) return;
    state = state.copyWith(loadMoreEndReached: loadMoreEndReached);
  }

  void updateSomeValues({int? loadMoreLastId, int? lastTotalPhotos}) {
    state = state.copyWith(
      loadMoreLastId: loadMoreLastId,
      lastTotalPhotos: lastTotalPhotos,
    );
  }

  void replace(ArtistsSearchData data) {
    state = data;
  }

  void reset() {
    state = const ArtistsSearchData();
  }
}

final searchArtistsDataProvider =
    NotifierProvider.autoDispose<SearchArtistsDataNotifier, ArtistsSearchData>(
      SearchArtistsDataNotifier.new,
    );

import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';
import 'package:ionicons/ionicons.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/asset_picker/asset_picker.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/http_responses/profile_http_response.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';
import 'package:portraitmode/profile/services/profile_service.dart';

class AvatarPicker extends ConsumerStatefulWidget {
  const AvatarPicker({super.key});

  @override
  AvatarPickerState createState() => AvatarPickerState();
}

class AvatarPickerState extends ConsumerState<AvatarPicker> {
  bool isLoading = false;
  late String avatarUrl;

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    avatarUrl = ref.watch(
      profileProvider.select((profile) => profile.avatarUrl),
    );

    return Center(
      child: (isLoading ? _buildLoadingWidget() : _buildPickerWidget()),
    );
  }

  Widget _buildPickerWidget() {
    return GestureDetector(
      onTap: _showChangeAvatarDialog,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Avatar(imageUrl: avatarUrl, size: 96.0, isOwnAvatar: true),
          SizedBox(
            width: 32.0,
            height: 32.0,
            child: Container(
              decoration: BoxDecoration(
                color: context.colors.brandColor.withValues(alpha: 0.5),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Ionicons.camera_outline,
                size: 18.0,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingWidget() {
    return Stack(
      alignment: Alignment.center,
      children: [
        Avatar(imageUrl: avatarUrl, isOwnAvatar: true, size: 96.0),
        Container(
          width: 96.0,
          height: 96.0,
          padding: const EdgeInsets.all(34.0),
          decoration: BoxDecoration(
            color: context.colors.brandColor.withValues(alpha: 0.5),
            shape: BoxShape.circle,
          ),
          child: const CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            strokeWidth: 3.0,
          ),
        ),
      ],
    );
  }

  void _showChangeAvatarDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(0.0),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                dense: true,
                horizontalTitleGap: 0.0,
                leading: const Icon(
                  Ionicons.camera_outline,
                  color: Colors.black,
                ),
                title: const Text("Take a photo"),
                onTap: () {
                  _pickImageFromCam();
                },
              ),
              ListTile(
                dense: true,
                horizontalTitleGap: 0.0,
                leading: const Icon(
                  Ionicons.image_outline,
                  color: Colors.black,
                ),
                title: AssetPicker(
                  onPick: _handleOnPick,
                  child: const Text("Choose from gallery"),
                ),
              ),
              ListTile(
                dense: true,
                horizontalTitleGap: 0.0,
                leading: Icon(
                  Ionicons.trash_outline,
                  color: context.colors.dangerColor,
                ),
                title: Text(
                  "Delete photo",
                  style: TextStyle(color: context.colors.dangerColor),
                ),
                onTap: _handleDeletePhoto,
              ),
            ],
          ),
        );
      },
    );
  }

  final _profileService = ProfileService();

  Future<void> _handleOnPick(AssetEntity? assetEntity) async {
    Navigator.pop(context);
    FocusManager.instance.primaryFocus?.unfocus();

    if (assetEntity == null) return;

    try {
      final file = await assetEntity.file;
      if (file == null) return;

      final bytes = await file.readAsBytes();

      _upload(bytes);
    } catch (e) {
      return;
    }
  }

  Future<void> _pickImageFromCam() async {
    Navigator.pop(context);
    FocusManager.instance.primaryFocus?.unfocus();

    final ImagePicker avatarPicker = ImagePicker();

    final XFile? pickedImage = await avatarPicker.pickImage(
      source: ImageSource.camera,
      maxWidth: 800.0,
      maxHeight: 800.0,
    );

    if (pickedImage == null) return;
    final bytes = await pickedImage.readAsBytes();

    _upload(bytes);
  }

  Future<void> _upload(Uint8List bytes) async {
    if (mounted) {
      setState(() {
        isLoading = true;
      });
    }

    ProfileHttpResponse response = await _profileService.uploadAvatarFromBytes(
      bytes: bytes,
    );

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }

        setState(() {
          isLoading = false;
        });
      }

      return;
    }

    ProfileData data = response.data ?? const ProfileData();

    await LocalUserService.replace(
      LocalUserData(
        userId: data.id,
        nicename: data.nicename,
        role: data.role,
        displayName: data.displayName,
        profileUrl: data.profileUrl,
        avatarUrl: data.avatarUrl,
        membershipType: data.membershipType,
      ),
    );

    ref.read(profileProvider.notifier).replace(data);

    if (mounted) {
      setState(() {
        isLoading = false;
      });
    }
  }

  Future<void> _handleDeletePhoto() async {
    Navigator.pop(context);
    FocusManager.instance.primaryFocus?.unfocus();

    if (mounted) {
      setState(() {
        isLoading = true;
      });
    }

    ProfileHttpResponse response = await _profileService.deleteAvatar();

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }

        setState(() {
          isLoading = false;
        });
      }

      return;
    }

    ProfileData data = response.data ?? const ProfileData();

    ref.read(profileProvider.notifier).replace(data);

    if (mounted) {
      setState(() {
        isLoading = false;
      });
    }
  }
}

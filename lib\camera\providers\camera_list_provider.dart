import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';

final class CameraListNotifier extends Notifier<List<CameraData>> {
  @override
  List<CameraData> build() => [];

  CameraData? getItem(int id) {
    return state.firstWhereOrNull((item) {
      return item.id == id;
    });
  }

  int getIndex(int id) {
    return state.indexWhere((item) {
      return item.id == id;
    });
  }

  bool hasItem(int id) {
    return state.any((item) {
      return item.id == id;
    });
  }

  void addItem(CameraData newItem) {
    state = [...state, newItem];
  }

  void addItems(List<CameraData> newItems) {
    state = [...state, ...newItems];
  }

  void removeItem(String slug) {
    state = state.where((item) => item.slug != slug).toList();
  }

  void replaceAll(List<CameraData> newItems) {
    state = newItems;
  }

  void clear() {
    state = [];
  }
}

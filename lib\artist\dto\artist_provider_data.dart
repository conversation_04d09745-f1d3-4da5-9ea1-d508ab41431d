import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';

@immutable
final class ArtistProviderData {
  final int id;
  final bool? isFollowing;
  final int? totalFollowing;
  final int? totalFollowers;

  /// Whether this artist is blocked by the current user
  final bool? isBlocked;

  /// Whether this artist is blocking the current user
  final bool? isBlocking;

  const ArtistProviderData({
    this.id = 0,
    this.isFollowing,
    this.totalFollowing,
    this.totalFollowers,
    this.isBlocked,
    this.isBlocking,
  });

  ArtistProviderData copyWith({
    int? id,
    bool? isFollowing,
    int? totalFollowing,
    int? totalFollowers,
    bool? isBlocked,
    bool? isBlocking,
  }) {
    return ArtistProviderData(
      id: id ?? this.id,
      isFollowing: isFollowing ?? this.isFollowing,
      totalFollowing: totalFollowing ?? this.totalFollowing,
      totalFollowers: totalFollowers ?? this.totalFollowers,
      isBlocked: isBlocked ?? this.isBlocked,
      isBlocking: isBlocking ?? this.isBlocking,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'isFollowing': isFollowing,
      'totalFollowing': totalFollowing,
      'totalFollowers': totalFollowers,
      'isBlocked': isBlocked,
      'isBlocking': isBlocking,
    };
  }

  factory ArtistProviderData.fromMap(Map<String, dynamic> data) {
    return ArtistProviderData(
      id: data['id'],
      isFollowing: data['isFollowing'] ?? false,
      totalFollowing: data['totalFollowing'] ?? 0,
      totalFollowers: data['totalFollowers'] ?? 0,
      isBlocked: data['isBlocked'] ?? false,
      isBlocking: data['isBlocking'] ?? false,
    );
  }

  factory ArtistProviderData.fromJson(String source) =>
      ArtistProviderData.fromMap(jsonDecode(source));

  factory ArtistProviderData.fromArtistData(ArtistData artist) {
    return ArtistProviderData(
      id: artist.id,
      isFollowing: artist.isFollowing,
      totalFollowing: artist.totalFollowing,
      totalFollowers: artist.totalFollowers,
      isBlocked: artist.isBlocked,
      isBlocking: artist.isBlocking,
    );
  }

  @override
  int get hashCode => Object.hash(
    id,
    isFollowing,
    totalFollowing,
    totalFollowers,
    isBlocked,
    isBlocking,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! ArtistProviderData) return false;

    return other.id == id &&
        other.isFollowing == isFollowing &&
        other.totalFollowing == totalFollowing &&
        other.totalFollowers == totalFollowers &&
        other.isBlocked == isBlocked &&
        other.isBlocking == isBlocking;
  }

  @override
  String toString() {
    return '''
ArtistProviderData(
  id: $id,
  isFollowing: $isFollowing,
  totalFollowing: $totalFollowing,
  totalFollowers: $totalFollowers,
  isBlocked: $isBlocked,
  isBlocking: $isBlocking,
)
''';
  }
}

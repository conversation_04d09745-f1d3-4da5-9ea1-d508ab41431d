import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:portraitmode/app/config/colors.dart';

class PrivacyPolicyLink extends StatelessWidget {
  PrivacyPolicyLink({super.key});

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            "By signing up you agree to our",
            style: TextStyle(
              color: context.colors.primarySwatch[400],
              fontSize: 13.0,
              fontWeight: FontWeight.w400,
            ),
          ),
          const SizedBox(height: 3.0),
          Row(
            children: [
              GestureDetector(
                onTap: () => onPrivacyPolicyLinkTap(context),
                child: Text(
                  "privacy policy",
                  style: TextStyle(
                    color: context.colors.accentColor,
                    fontSize: 13.0,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
              const SizedBox(width: 5.0),
              Text(
                "&",
                style: TextStyle(
                  color: context.colors.primarySwatch[400],
                  fontSize: 13.0,
                  fontWeight: FontWeight.w400,
                ),
              ),
              const SizedBox(width: 5.0),
              GestureDetector(
                onTap: () => onTermsOfServiceLinkTap(context),
                child: Text(
                  "terms of service.",
                  style: TextStyle(
                    color: context.colors.accentColor,
                    fontSize: 13.0,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  final InAppBrowser browser = InAppBrowser();

  final settings = InAppBrowserClassSettings(
    browserSettings: InAppBrowserSettings(
      hideDefaultMenuItems: true,
      hideToolbarBottom: true,
      hideToolbarTop: true,
      hideUrlBar: true,
    ),
    webViewSettings: InAppWebViewSettings(
      javaScriptEnabled: true,
      isInspectable: kDebugMode,
    ),
  );

  void onPrivacyPolicyLinkTap(BuildContext context) {
    browser.openUrlRequest(
      urlRequest: URLRequest(
        url: WebUri(
          "https://portraitmode.io/read-mode/?page-name=privacy-policy",
        ),
      ),
      settings: settings,
    );
  }

  void onTermsOfServiceLinkTap(BuildContext context) {
    browser.openUrlRequest(
      urlRequest: URLRequest(
        url: WebUri(
          "https://portraitmode.io/read-mode/?page-name=terms-of-service",
        ),
      ),
      settings: settings,
    );
  }
}

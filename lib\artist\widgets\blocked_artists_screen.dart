import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/http_responses/artist_list_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/providers/blocked_artists_provider.dart';
import 'package:portraitmode/artist/services/artist_list_service.dart';
import 'package:portraitmode/artist/utils/artist_util.dart';
import 'package:portraitmode/artist/widgets/artist_list_item.dart';
import 'package:portraitmode/artist/widgets/unblock_button.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class BlockedArtistsScreen extends ConsumerStatefulWidget {
  const BlockedArtistsScreen({super.key});

  @override
  BlockedArtistsScreenState createState() => BlockedArtistsScreenState();
}

class BlockedArtistsScreenState extends ConsumerState<BlockedArtistsScreen> {
  final _scrollController = ScrollController();
  final int _loadMorePerPage = LoadMoreConfig.artistsPerPage;
  int _loadMoreLastId = -1;
  int _lastTotalPhotos = -1;
  bool _loadMoreEndReached = false;

  final _artistListService = ArtistListService();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: BlockedArtistsScreen');

    final artistList = ref.watch(blockedArtistsProvider);

    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              slivers: <Widget>[
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: "Blocked artists",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  actions: const [],
                ),
                EasyLoadMore(
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  idleStatusText: "",
                  loadingStatusText: "",
                  finishedStatusText: artistList.isEmpty
                      ? 'No artists blocked.'
                      : '',
                  child: _buildSliverList(artistList),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverList(List<ArtistData> artistList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        double marginTop = index == 0 ? LayoutConfig.contentTopGap : 12.0;

        return Padding(
          padding: EdgeInsets.only(
            left: ScreenStyleConfig.horizontalPadding,
            right: ScreenStyleConfig.horizontalPadding,
            top: marginTop,
          ),
          child: Row(
            children: [
              Expanded(
                child: ArtistListItem(index: index, artist: artistList[index]),
              ),
              const SizedBox(width: 10.0),
              UnblockButton(artist: artistList[index]),
            ],
          ),
        );
      }, childCount: artistList.length),
    );
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = -1;
    _lastTotalPhotos = -1;
    _loadMoreEndReached = false;

    ArtistListResponse response = await _artistListService.fetchBlocked(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      lastTotalPhotos: _lastTotalPhotos,
    );

    _handleArtistListResponse(response, true);
  }

  Future<bool> _handleLoadMore() async {
    ArtistListResponse response = await _artistListService.fetchBlocked(
      limit: _loadMorePerPage,
      lastId: _loadMoreLastId,
      lastTotalPhotos: _lastTotalPhotos,
    );

    return _handleArtistListResponse(response, false);
  }

  bool _handleArtistListResponse(ArtistListResponse response, bool isRefresh) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (isRefresh) {
        ref
            .read(blockedArtistIdsProvider.notifier)
            .replaceAll(artistListToIdList(response.data));
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    ref
        .read(artistStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    final isFirstLoad = _loadMoreLastId == -1;
    _loadMoreLastId = response.data.last.id;
    _lastTotalPhotos = response.data.last.totalPhotos;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(blockedArtistIdsProvider.notifier)
          .replaceAll(artistListToIdList(response.data));

      return true;
    }

    if (isFirstLoad) {
      ref
          .read(blockedArtistIdsProvider.notifier)
          .replaceAll(artistListToIdList(response.data));

      return true;
    }

    ref
        .read(blockedArtistIdsProvider.notifier)
        .addItems(artistListToIdList(response.data));

    return true;
  }
}

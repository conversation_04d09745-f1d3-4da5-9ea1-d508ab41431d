import 'package:portraitmode/common/dto/common_refresh_data.dart';

class CommonRefreshResponse {
  final bool success;
  final String errorCode;
  final String message;
  final CommonRefreshData? data;

  CommonRefreshResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory CommonRefreshResponse.fromMap(Map<String, dynamic> map) {
    return CommonRefreshResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? CommonRefreshData.fromMap(map['data'])
          : null,
    );
  }
}

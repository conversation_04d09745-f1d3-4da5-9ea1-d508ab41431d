import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/common/http_responses/common_refresh_response.dart';

class CommonRefreshService extends BaseService {
  Future<CommonRefreshResponse> fetch() async {
    try {
      final response = await http.get('${URL.baseApiUrl}/common-refresh-data');

      return CommonRefreshResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommonRefreshResponse.fromMap(e.response?.data);
      }

      return CommonRefreshResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CommonRefreshResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

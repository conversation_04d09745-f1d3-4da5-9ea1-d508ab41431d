import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/youtube/http_responses/remove_youtube_channel_response.dart';

class RemoveYoutubeChannelService extends BaseService {
  Future<RemoveYoutubeChannelResponse> remove(String channelId) async {
    try {
      final response = await http.delete(
        '${URL.baseApiUrl}/youtube/channels/$channelId',
      );

      return RemoveYoutubeChannelResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return RemoveYoutubeChannelResponse.fromMap(e.response?.data);
      }

      return RemoveYoutubeChannelResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return RemoveYoutubeChannelResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

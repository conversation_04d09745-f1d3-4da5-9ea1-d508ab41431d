import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/dto/plain_album_data.dart';
import 'package:portraitmode/asset_picker/utils/album_util.dart';
import 'package:portraitmode/asset_picker/widgets/dropdown_icon_button.dart';

/// Gallery header widget.
///
/// Since we're supposed to not using AppBar in the parent widget,
/// then this widget will act like an AppBar (just visually).
///
/// This widget contains close (x) button in the left,
/// title and subtitle in the center,
/// and an arrow down button in the right.
class GalleryHeader extends StatefulWidget {
  final double height;
  final AssetPathEntity? activeAlbum;
  final void Function() onCloseButtonTap;
  final void Function() onDropdownToggle;

  const GalleryHeader({
    super.key,
    this.height = 60.0,
    this.activeAlbum,
    required this.onCloseButtonTap,
    required this.onDropdownToggle,
  });

  @override
  GalleryHeaderState createState() => GalleryHeaderState();
}

class GalleryHeaderState extends State<GalleryHeader> {
  @override
  Widget build(BuildContext context) {
    return Container(
      height: widget.height,
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 5.0),
      child: Row(
        children: [
          // Close button using rounded InkWell.
          InkWell(
            onTap: widget.onCloseButtonTap,
            borderRadius: BorderRadius.circular(40.0),
            child: const Padding(
              padding: EdgeInsets.all(8.0),
              child: Icon(Ionicons.close, color: Colors.white),
            ),
          ),

          // Title and subtitle
          Expanded(
            child: FutureBuilder<PlainAlbumData?>(
              future: _readActiveAlbum(),
              builder:
                  (
                    BuildContext context,
                    AsyncSnapshot<PlainAlbumData?> snapshot,
                  ) {
                    if (snapshot.connectionState == ConnectionState.done) {
                      if (snapshot.data == null) {
                        return const SizedBox.shrink();
                      }

                      return Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            snapshot.data!.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(height: 2.0),
                          Text(
                            '${snapshot.data!.totalPhotos} photos',
                            style: const TextStyle(
                              fontSize: 11.0,
                              color: Colors.grey,
                              fontWeight: FontWeight.w400,
                            ),
                          ),
                        ],
                      );
                    }

                    return const SizedBox.shrink();
                  },
            ),
          ),

          // Arrow down button using rounded InkWell.
          DropdownIconButton(
            dropdownOpened: false,
            onDropdownToggle: widget.onDropdownToggle,
          ),
        ],
      ),
    );
  }

  Future<PlainAlbumData?> _readActiveAlbum() async {
    if (widget.activeAlbum == null) return null;

    final albumData = await readAlbumData(widget.activeAlbum!);

    return albumData;
  }
}

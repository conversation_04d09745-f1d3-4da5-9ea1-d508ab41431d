import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:portraitmode/app/config/colors.dart';

class ReadMoreButton extends StatelessWidget {
  final EdgeInsetsGeometry padding;
  final Color? bgColor;
  final String buttonText;
  final double fontSize;
  final FontWeight fontWeight;
  final double borderRadius;
  final void Function()? onPressed;

  const ReadMoreButton({
    super.key,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0, vertical: 5.0),
    this.bgColor,
    this.buttonText = 'Read more',
    this.fontSize = 14.5,
    this.fontWeight = FontWeight.w500,
    this.borderRadius = 4.0,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return FilledButton(
      onPressed: onPressed,
      style: ButtonStyle(
        padding: WidgetStateProperty.all<EdgeInsetsGeometry>(padding),
        backgroundColor: WidgetStateProperty.all<Color>(
          bgColor ?? context.colors.accentColor,
        ),
        shape: WidgetStateProperty.all<RoundedRectangleBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
      ),
      child: Text(
        buttonText,
        style: GoogleFonts.inter(
          textStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

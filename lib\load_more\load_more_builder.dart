import 'dart:async';

import 'package:flutter/material.dart';

typedef FutureCallBack = Future<bool> Function();

enum LoadMoreBuilderStatusState { idle, loading, failed, finished }

class _BuildNotification extends Notification {}

class _RetryNotification extends Notification {}

class _StatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';
}

class _LoadingIndicatorDefaultOpts {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int delay = 16;
}

class LoadMoreBuilder extends StatefulWidget {
  /// The height of the loading widget's container/wrapper.
  final double loadingWidgetContainerHeight;

  /// The loading widget size.
  final double loadingWidgetSize;

  /// The loading widget stroke width.
  final double loadingWidgetStrokeWidth;

  /// The loading widget color.
  final Color loadingWidgetColor;

  /// The loading widget animation delay.
  final int loadingWidgetAnimationDelay;

  /// Status text to show when the load more is not triggered.
  final String idleStatusText;

  /// Status text to show when the process is loading.
  final String loadingStatusText;

  /// Status text to show when the processing is failed.
  final String failedStatusText;

  /// Status text to show when there's no more items to load.
  final String finishedStatusText;

  /// Manually turn-off the next load more.
  ///
  /// Set this to `true` to set the load more as `finished` (no more items). Default is `false`.
  ///
  /// The use-case is when there's no more items to load, you might want `LoadMoreBuilder` to not running again.
  final bool isFinished;

  /// Callback function to run during the load more process.
  ///
  /// To mark the status as success or delay, set the return to `true`.
  ///
  /// To mark the status as failed, set the return to `false`.
  final FutureCallBack onLoadMore;

  /// Whether or not the current index is the last index.
  final bool isLastIndex;

  /// Whether the child widget is a sliver widget.
  final bool isSliver;

  /// The child widget.
  final Widget child;

  const LoadMoreBuilder({
    super.key,
    this.loadingWidgetContainerHeight =
        _LoadingIndicatorDefaultOpts.containerHeight,
    this.loadingWidgetSize = _LoadingIndicatorDefaultOpts.size,
    this.loadingWidgetStrokeWidth = _LoadingIndicatorDefaultOpts.strokeWidth,
    this.loadingWidgetColor = _LoadingIndicatorDefaultOpts.color,
    this.loadingWidgetAnimationDelay = _LoadingIndicatorDefaultOpts.delay,
    this.idleStatusText = _StatusText.idle,
    this.loadingStatusText = _StatusText.loading,
    this.failedStatusText = _StatusText.failed,
    this.finishedStatusText = _StatusText.finished,
    this.isFinished = false,
    required this.onLoadMore,
    this.isLastIndex = false,
    this.isSliver = false,
    required this.child,
  });

  @override
  LoadMoreBuilderState createState() => LoadMoreBuilderState();
}

class LoadMoreBuilderState extends State<LoadMoreBuilder> {
  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLastIndex) {
      if (widget.isSliver) {
        return SliverToBoxAdapter(
          child: Column(
            children: [
              widget.child,
              const SizedBox(height: 15.0),
              _buildLoadMoreView(),
            ],
          ),
        );
      } else {
        return Column(
          children: [
            widget.child,
            const SizedBox(height: 15.0),
            _buildLoadMoreView(),
          ],
        );
      }
    }

    return widget.child;
  }

  LoadMoreBuilderStatusState status = LoadMoreBuilderStatusState.idle;

  Widget _buildLoadMoreView() {
    if (widget.isFinished == true) {
      status = LoadMoreBuilderStatusState.finished;
    } else {
      if (status == LoadMoreBuilderStatusState.finished) {
        status = LoadMoreBuilderStatusState.idle;
      }
    }

    // log('status: $status');

    return NotificationListener<_RetryNotification>(
      onNotification: _onRetry,
      child: NotificationListener<_BuildNotification>(
        onNotification: _onLoadMoreBuild,
        child: _LoadMoreStatusWidget(
          status: status,
          containerHeight: widget.loadingWidgetContainerHeight,
          size: widget.loadingWidgetSize,
          strokeWidth: widget.loadingWidgetStrokeWidth,
          color: widget.loadingWidgetColor,
          animationDelay: widget.loadingWidgetAnimationDelay,
          idleStatusText: widget.idleStatusText,
          loadingStatusText: widget.loadingStatusText,
          failedStatusText: widget.failedStatusText,
          finishedStatusText: widget.finishedStatusText,
          useSliverChild: widget.isSliver,
        ),
      ),
    );
  }

  bool _onLoadMoreBuild(_BuildNotification notification) {
    if (status == LoadMoreBuilderStatusState.idle) {
      _loadMore();
    }

    if (status == LoadMoreBuilderStatusState.loading) {
      return false;
    }

    if (status == LoadMoreBuilderStatusState.failed) {
      return false;
    }

    if (status == LoadMoreBuilderStatusState.finished) {
      return false;
    }

    return false;
  }

  void _updateStatus(LoadMoreBuilderStatusState status) {
    if (mounted) {
      setState(() => this.status = status);
    }
  }

  bool _onRetry(_RetryNotification notification) {
    _loadMore();
    return false;
  }

  void _loadMore() {
    _updateStatus(LoadMoreBuilderStatusState.loading);

    widget.onLoadMore().then((v) {
      if (v == true) {
        _updateStatus(LoadMoreBuilderStatusState.idle);
      } else {
        _updateStatus(LoadMoreBuilderStatusState.failed);
      }
    });
  }
}

class _LoadMoreStatusWidget extends StatefulWidget {
  final LoadMoreBuilderStatusState status;

  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final int animationDelay;

  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;

  final bool useSliverChild;

  const _LoadMoreStatusWidget({
    required this.status,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.animationDelay,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
    required this.useSliverChild,
  });

  @override
  State<_LoadMoreStatusWidget> createState() => _LoadMoreStatusWidgetState();
}

class _LoadMoreStatusWidgetState extends State<_LoadMoreStatusWidget> {
  final buildNotification = _BuildNotification();
  final retryNotification = _RetryNotification();

  @override
  Widget build(BuildContext context) {
    notify();

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (widget.status == LoadMoreBuilderStatusState.failed ||
            widget.status == LoadMoreBuilderStatusState.idle) {
          _notifyRetryProcess();
        }
      },
      child: Container(
        height: widget.containerHeight,
        alignment: Alignment.center,
        child: buildStatusWidget(),
      ),
    );
  }

  Widget buildStatusWidget() {
    String text = '';

    switch (widget.status) {
      case LoadMoreBuilderStatusState.idle:
        text = widget.idleStatusText;
        break;
      case LoadMoreBuilderStatusState.loading:
        text = widget.loadingStatusText;
        break;
      case LoadMoreBuilderStatusState.failed:
        text = widget.failedStatusText;
        break;
      case LoadMoreBuilderStatusState.finished:
        text = widget.finishedStatusText;
        break;
    }

    if (widget.status == LoadMoreBuilderStatusState.failed) {
      return Container(padding: const EdgeInsets.all(0.0), child: Text(text));
    }

    if (widget.status == LoadMoreBuilderStatusState.idle) {
      return Text(text);
    }

    if (widget.status == LoadMoreBuilderStatusState.loading) {
      return Container(
        alignment: Alignment.center,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: <Widget>[
            SizedBox(
              width: widget.size,
              height: widget.size,
              child: CircularProgressIndicator(
                strokeWidth: widget.strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(widget.color),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(left: 10.0),
              child: Text(text),
            ),
          ],
        ),
      );
    }

    if (widget.status == LoadMoreBuilderStatusState.finished) {
      return Text(text);
    }

    return Text(text);
  }

  void notify() async {
    Duration delay = max(
      Duration(microseconds: widget.animationDelay),
      const Duration(milliseconds: _LoadingIndicatorDefaultOpts.delay),
    );

    await Future.delayed(delay);

    if (widget.status == LoadMoreBuilderStatusState.idle) {
      _notifyBuildProcess();
    }
  }

  Duration max(Duration duration, Duration duration2) {
    if (duration > duration2) {
      return duration;
    }
    return duration2;
  }

  void _notifyBuildProcess() {
    if (!mounted) return;
    buildNotification.dispatch(context);
  }

  void _notifyRetryProcess() {
    if (!mounted) return;
    retryNotification.dispatch(context);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/notification/dto/notification_data.dart';
import 'package:portraitmode/notification/dto/notification_fetch_result.dart';
import 'package:portraitmode/notification/http_responses/notification_fetch_response.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/notification/services/notification_list_service.dart';
import 'package:portraitmode/notification/widgets/notification_list_item.dart';

class NotificationsScreen extends ConsumerStatefulWidget {
  const NotificationsScreen({super.key, required this.profileId});

  final int profileId;

  @override
  NotificationsScreenState createState() => NotificationsScreenState();
}

class NotificationsScreenState extends ConsumerState<NotificationsScreen> {
  final _scrollController = ScrollController();
  final _notificationListService = NotificationListService();

  @override
  void initState() {
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: NotificationsScreen');
    List<NotificationData> notificationList = ref.watch(
      notificationProvider.select((value) => value.notifications),
    );

    // log('Total notifications: ${notificationList.length.toString()}');

    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            edgeOffset: LayoutConfig.bottomNavBarHeight,
            onRefresh: _handleRefresh,
            child: CustomScrollView(
              controller: _scrollController,
              physics: const AlwaysScrollableScrollPhysics(),
              slivers: <Widget>[
                PmSliverAppBar(
                  scrollController: _scrollController,
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  titleText: 'Notifications',
                  actions: const [],
                ),
                notificationList.isEmpty
                    ? _buildEmptyView()
                    : _buildSliverListView(notificationList),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSliverListView(List<NotificationData> notificationList) {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        return NotificationListItem(
          index: index,
          notification: notificationList[index],
        );
      }, childCount: notificationList.length),
    );
  }

  Widget _buildEmptyView() {
    return const SliverToBoxAdapter(
      child: Column(
        children: [
          SizedBox(height: 10.0),
          Text('No notifications', style: TextStyle(fontSize: 14.0)),
        ],
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref.read(notificationProvider.notifier).reset();

    NotificationFetchResponse response = await _notificationListService.fetch();

    _handleNotificationListResponse(response, true, false);
  }

  void _handleNotificationListResponse(
    NotificationFetchResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success || response.data == null) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    NotificationFetchResult result = response.data!;

    if (isRefresh) {
      ref.read(notificationProvider.notifier).replace(result);
    }
  }
}

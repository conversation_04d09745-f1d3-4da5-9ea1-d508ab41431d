// masonry_load_more.dart

import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';
import 'package:portraitmode/load_more/enum.dart';

typedef LoadMoreCallback = Future<LoadMoreResult> Function();

enum MasonryLoadMoreStatusState { idle, loading, failed, finished }

class MasonryLoadMoreStatusText {
  static const String idle = 'Scroll to load more';
  static const String loading = 'Loading...';
  static const String failed = 'Failed to load items';
  static const String finished = 'No more items';

  static String getText(MasonryLoadMoreStatusState state) {
    switch (state) {
      case MasonryLoadMoreStatusState.loading:
        return loading;
      case MasonryLoadMoreStatusState.failed:
        return failed;
      case MasonryLoadMoreStatusState.finished:
        return finished;
      default:
        return idle;
    }
  }
}

class MasonryLoadMoreDefaults {
  static const double containerHeight = 60.0;
  static const double size = 24.0;
  static const double strokeWidth = 3.0;
  static const Color color = Colors.blue;
  static const int scrollThreshold = 200;
  static const int debounceMilliseconds = 500;
  static const int maxRetries = 3;
  static const int minScrollThreshold = 50;
  static const int maxScrollThreshold = 1000;
  static const int minDebounceMs = 100;
  static const int maxDebounceMs = 3000;
}

class MasonryLoadMore extends StatefulWidget {
  final GlobalKey? masonryKey;

  /// Optional external scroll controller.
  ///
  /// If supplied, you must not dispose it manually while the widget is active.
  /// Let MasonryLoadMore manage the listener lifecycle.
  final ScrollController? scrollController;

  final double loadingWidgetContainerHeight;
  final double loadingWidgetSize;
  final double loadingWidgetStrokeWidth;
  final Color loadingWidgetColor;
  final int scrollThreshold;
  final int debounceMilliseconds;
  final int maxRetries;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final bool isFinished;
  final bool runOnEmptyResult;
  final bool showLoadMoreIndicator;
  final bool enableAutoLoadMore;
  final LoadMoreCallback onLoadMore;
  final ScrollPhysics? physics;
  final EdgeInsets? padding;
  final int crossAxisCount;
  final double mainAxisSpacing;
  final double crossAxisSpacing;
  final int itemsCount;
  final Widget Function(BuildContext, int) itemBuilder;

  const MasonryLoadMore({
    super.key,
    this.masonryKey,
    this.scrollController,
    this.loadingWidgetContainerHeight = MasonryLoadMoreDefaults.containerHeight,
    this.loadingWidgetSize = MasonryLoadMoreDefaults.size,
    this.loadingWidgetStrokeWidth = MasonryLoadMoreDefaults.strokeWidth,
    this.loadingWidgetColor = MasonryLoadMoreDefaults.color,
    this.scrollThreshold = MasonryLoadMoreDefaults.scrollThreshold,
    this.debounceMilliseconds = MasonryLoadMoreDefaults.debounceMilliseconds,
    this.maxRetries = MasonryLoadMoreDefaults.maxRetries,
    this.idleStatusText = MasonryLoadMoreStatusText.idle,
    this.loadingStatusText = MasonryLoadMoreStatusText.loading,
    this.failedStatusText = MasonryLoadMoreStatusText.failed,
    this.finishedStatusText = MasonryLoadMoreStatusText.finished,
    this.isFinished = false,
    this.runOnEmptyResult = false,
    this.showLoadMoreIndicator = true,
    this.enableAutoLoadMore = true,
    required this.onLoadMore,
    this.physics,
    this.padding,
    required this.crossAxisCount,
    required this.mainAxisSpacing,
    required this.crossAxisSpacing,
    required this.itemsCount,
    required this.itemBuilder,
  }) : assert(crossAxisCount > 0, 'crossAxisCount must be positive'),
       assert(itemsCount >= 0, 'itemsCount must be non-negative'),
       assert(
         loadingWidgetContainerHeight > 0,
         'loadingWidgetContainerHeight must be positive',
       ),
       assert(loadingWidgetSize > 0, 'loadingWidgetSize must be positive'),
       assert(
         loadingWidgetStrokeWidth > 0,
         'loadingWidgetStrokeWidth must be positive',
       ),
       assert(
         scrollThreshold >= MasonryLoadMoreDefaults.minScrollThreshold &&
             scrollThreshold <= MasonryLoadMoreDefaults.maxScrollThreshold,
         'scrollThreshold must be between ${MasonryLoadMoreDefaults.minScrollThreshold} and ${MasonryLoadMoreDefaults.maxScrollThreshold}',
       ),
       assert(
         debounceMilliseconds >= MasonryLoadMoreDefaults.minDebounceMs &&
             debounceMilliseconds <= MasonryLoadMoreDefaults.maxDebounceMs,
         'debounceMilliseconds must be between ${MasonryLoadMoreDefaults.minDebounceMs} and ${MasonryLoadMoreDefaults.maxDebounceMs}',
       ),
       assert(maxRetries >= 0, 'maxRetries must be non-negative'),
       assert(mainAxisSpacing >= 0, 'mainAxisSpacing must be non-negative'),
       assert(crossAxisSpacing >= 0, 'crossAxisSpacing must be non-negative');

  @override
  MasonryLoadMoreState createState() => MasonryLoadMoreState();
}

class MasonryLoadMoreState extends State<MasonryLoadMore> {
  late ScrollController _activeScrollController;
  ScrollController? _internalScrollController;
  MasonryLoadMoreStatusState _status = MasonryLoadMoreStatusState.idle;
  Timer? _debounceTimer;
  bool _isDisposed = false;
  bool _isLoadingInProgress = false;
  bool _hasScrollListener = false;
  int _retryCount = 0;
  int _loadMoreCallCount = 0;

  // Track empty load more
  bool _hasTriggeredEmptyLoad = false;

  // Atomic timer operations
  final Object _timerLock = Object();

  bool get _shouldShowLoadMore =>
      widget.showLoadMoreIndicator && !_isFinishedState;

  bool get _isFinishedState =>
      widget.isFinished || _status == MasonryLoadMoreStatusState.finished;

  int get _totalItemCount => widget.itemsCount + (_shouldShowLoadMore ? 1 : 0);

  @override
  void initState() {
    super.initState();
    _initializeScrollController();
    _scheduleEmptyLoadIfNeeded();
  }

  void _initializeScrollController() {
    if (widget.scrollController != null) {
      _activeScrollController = widget.scrollController!;
      _internalScrollController = null;
    } else {
      _internalScrollController = ScrollController();
      _activeScrollController = _internalScrollController!;
    }

    _updateScrollListener();
  }

  void _updateScrollListener() {
    final shouldHaveListener = widget.enableAutoLoadMore && !_isFinishedState;

    if (shouldHaveListener && !_hasScrollListener) {
      _addScrollListener();
    } else if (!shouldHaveListener && _hasScrollListener) {
      _removeScrollListener();
    }
  }

  void _addScrollListener() {
    if (!_hasScrollListener && _activeScrollController.hasClients) {
      try {
        _activeScrollController.addListener(_scrollListener);
        _hasScrollListener = true;
      } catch (e) {
        debugPrint('Warning: Could not add scroll listener: $e');
      }
    }
  }

  void _removeScrollListener() {
    if (_hasScrollListener) {
      try {
        _activeScrollController.removeListener(_scrollListener);
        _hasScrollListener = false;
      } catch (e) {
        debugPrint('Warning: Could not remove scroll listener: $e');
        // Don't set _hasScrollListener to false if removal failed
        // This prevents memory leaks by ensuring we don't lose track of attached listeners
      }
    }
  }

  void _scheduleEmptyLoadIfNeeded() {
    if (widget.itemsCount == 0 &&
        widget.runOnEmptyResult &&
        !_isFinishedState &&
        !_hasTriggeredEmptyLoad) {
      _hasTriggeredEmptyLoad = true;
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && !_isDisposed && !_isLoadingInProgress) {
          _triggerLoadMore();
        }
      });
    }
  }

  @override
  void didUpdateWidget(MasonryLoadMore oldWidget) {
    super.didUpdateWidget(oldWidget);

    // Handle scroll controller changes
    _handleScrollControllerChange(oldWidget);

    // Handle auto load more changes
    if (oldWidget.enableAutoLoadMore != widget.enableAutoLoadMore) {
      _updateScrollListener();
    }

    // Update status if isFinished changed
    if (oldWidget.isFinished != widget.isFinished) {
      if (widget.isFinished) {
        _safeUpdateStatus(MasonryLoadMoreStatusState.finished);
      }
      _updateScrollListener();
    }

    // Reset empty load trigger when item count changes from 0 to something else
    if (oldWidget.itemsCount == 0 && widget.itemsCount > 0) {
      _hasTriggeredEmptyLoad = false;
    }

    // Reset retry count if maxRetries changed
    if (oldWidget.maxRetries != widget.maxRetries) {
      _retryCount = 0;
    }
  }

  void _handleScrollControllerChange(MasonryLoadMore oldWidget) {
    if (oldWidget.scrollController != widget.scrollController) {
      _removeScrollListener();

      // Handle all scroll controller transition scenarios
      if (oldWidget.scrollController == null &&
          widget.scrollController != null) {
        // Internal -> External
        _internalScrollController?.dispose();
        _internalScrollController = null;
      } else if (oldWidget.scrollController != null &&
          widget.scrollController == null) {
        // External -> Internal (need to create new internal controller)
        _internalScrollController = ScrollController();
      }
      // External -> External: no disposal needed

      _initializeScrollController();
    }
  }

  @override
  void dispose() {
    _isDisposed = true;
    _cancelTimerSafely();
    _removeScrollListener();

    // Only dispose internal controller
    _internalScrollController?.dispose();
    _internalScrollController = null;

    super.dispose();
  }

  void _cancelTimerSafely() {
    synchronized(_timerLock, () {
      _debounceTimer?.cancel();
      _debounceTimer = null;
    });
  }

  void _scrollListener() {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    if (!_activeScrollController.hasClients) {
      return;
    }

    try {
      final position = _activeScrollController.position;
      if (!position.hasContentDimensions ||
          !position.hasPixels ||
          !position.hasViewportDimension) {
        return;
      }

      final maxScroll = position.maxScrollExtent;
      final currentScroll = position.pixels;
      final threshold = widget.scrollThreshold.toDouble();

      if (maxScroll > 0 &&
          currentScroll >= (maxScroll - threshold) &&
          _status == MasonryLoadMoreStatusState.idle) {
        _triggerLoadMore();
      }
    } catch (e) {
      debugPrint('Error in scroll listener: $e');
    }
  }

  void _triggerLoadMore() {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    // Prevent multiple simultaneous calls
    final currentCallCount = ++_loadMoreCallCount;

    _cancelTimerSafely();

    synchronized(_timerLock, () {
      if (_debounceTimer != null) {
        _debounceTimer!.cancel();
      }

      _debounceTimer = Timer(
        Duration(milliseconds: widget.debounceMilliseconds),
        () {
          // Verify this is still the latest call
          if (currentCallCount == _loadMoreCallCount &&
              !_isDisposed &&
              mounted &&
              !_isLoadingInProgress &&
              !_isFinishedState) {
            _performLoadMore();
          }
        },
      );
    });
  }

  Future<void> _performLoadMore() async {
    if (_isDisposed || _isLoadingInProgress || _isFinishedState) {
      return;
    }

    _isLoadingInProgress = true;
    _safeUpdateStatus(MasonryLoadMoreStatusState.loading);

    try {
      final result = await widget.onLoadMore();

      if (!_isDisposed && mounted) {
        _isLoadingInProgress = false;

        switch (result) {
          case LoadMoreResult.success:
            _retryCount = 0; // Reset retry count on success
            _safeUpdateStatus(MasonryLoadMoreStatusState.idle);
            break;
          case LoadMoreResult.finished:
            _safeUpdateStatus(MasonryLoadMoreStatusState.finished);
            break;
          case LoadMoreResult.failed:
            _retryCount++;
            if (_retryCount >= widget.maxRetries) {
              _safeUpdateStatus(MasonryLoadMoreStatusState.finished);
            } else {
              _safeUpdateStatus(MasonryLoadMoreStatusState.failed);
            }
            break;
        }
      }
    } catch (e) {
      debugPrint('MasonryLoadMore error: $e');

      if (!_isDisposed && mounted) {
        _isLoadingInProgress = false;
        _retryCount++;

        if (_retryCount >= widget.maxRetries) {
          _safeUpdateStatus(MasonryLoadMoreStatusState.finished);
        } else {
          _safeUpdateStatus(MasonryLoadMoreStatusState.failed);
        }
      }
    }
  }

  void _safeUpdateStatus(MasonryLoadMoreStatusState newStatus) {
    if (_isDisposed || !mounted || _status == newStatus) {
      return;
    }

    setState(() {
      _status = newStatus;
    });
  }

  /// Public method to manually trigger load more
  void loadMore() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status != MasonryLoadMoreStatusState.loading &&
        !_isFinishedState) {
      _triggerLoadMore();
    }
  }

  /// Public method to retry loading after failure
  void retry() {
    if (!_isDisposed &&
        !_isLoadingInProgress &&
        _status == MasonryLoadMoreStatusState.failed) {
      _triggerLoadMore();
    }
  }

  /// Public method to reset the load more state
  void reset() {
    if (!_isDisposed && !_isLoadingInProgress) {
      _hasTriggeredEmptyLoad = false;
      _retryCount = 0;
      _loadMoreCallCount = 0;
      _safeUpdateStatus(MasonryLoadMoreStatusState.idle);
    }
  }

  /// Get current retry count
  int get retryCount => _retryCount;

  /// Get current status
  MasonryLoadMoreStatusState get status => _status;

  /// Check if loading is in progress
  bool get isLoading => _isLoadingInProgress;

  @override
  Widget build(BuildContext context) {
    return _buildMasonry();
  }

  Widget _buildMasonry() {
    // Handle empty items case
    if (widget.itemsCount == 0) {
      if (widget.runOnEmptyResult && widget.showLoadMoreIndicator) {
        return Center(child: _buildLoadMoreView());
      }
      return const SizedBox.shrink();
    }

    return MasonryGridView.count(
      key: widget.masonryKey,
      controller: _activeScrollController,
      physics: widget.physics,
      padding: widget.padding,
      crossAxisCount: widget.crossAxisCount,
      mainAxisSpacing: widget.mainAxisSpacing,
      crossAxisSpacing: widget.crossAxisSpacing,
      itemBuilder: (context, index) {
        // Regular item
        if (index < widget.itemsCount) {
          return widget.itemBuilder(context, index);
        }

        // Load more indicator item
        return _shouldShowLoadMore
            ? _buildLoadMoreView()
            : const SizedBox.shrink();
      },
      itemCount: _totalItemCount,
    );
  }

  Widget _buildLoadMoreView() {
    final currentStatus = _isFinishedState
        ? MasonryLoadMoreStatusState.finished
        : _status;

    return MasonryLoadMoreView(
      status: currentStatus,
      containerHeight: widget.loadingWidgetContainerHeight,
      size: widget.loadingWidgetSize,
      strokeWidth: widget.loadingWidgetStrokeWidth,
      color: widget.loadingWidgetColor,
      idleStatusText: widget.idleStatusText,
      loadingStatusText: widget.loadingStatusText,
      failedStatusText: widget.failedStatusText,
      finishedStatusText: widget.finishedStatusText,
      retryCount: _retryCount,
      maxRetries: widget.maxRetries,
      onRetry: retry,
    );
  }
}

class MasonryLoadMoreView extends StatelessWidget {
  final MasonryLoadMoreStatusState status;
  final double containerHeight;
  final double size;
  final double strokeWidth;
  final Color color;
  final String idleStatusText;
  final String loadingStatusText;
  final String failedStatusText;
  final String finishedStatusText;
  final int retryCount;
  final int maxRetries;
  final VoidCallback? onRetry;

  const MasonryLoadMoreView({
    super.key,
    required this.status,
    required this.containerHeight,
    required this.size,
    required this.strokeWidth,
    required this.color,
    required this.idleStatusText,
    required this.loadingStatusText,
    required this.failedStatusText,
    required this.finishedStatusText,
    required this.retryCount,
    required this.maxRetries,
    this.onRetry,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if ((status == MasonryLoadMoreStatusState.failed ||
                status == MasonryLoadMoreStatusState.idle) &&
            onRetry != null) {
          onRetry!();
        }
      },
      child: Container(
        height: containerHeight,
        alignment: Alignment.center,
        child: _buildStatusWidget(),
      ),
    );
  }

  Widget _buildStatusWidget() {
    switch (status) {
      case MasonryLoadMoreStatusState.idle:
        return Text(
          idleStatusText,
          style: TextStyle(color: Colors.grey[600]),
          textAlign: TextAlign.center,
        );

      case MasonryLoadMoreStatusState.loading:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: size,
              height: size,
              child: CircularProgressIndicator(
                strokeWidth: strokeWidth,
                valueColor: AlwaysStoppedAnimation<Color>(color),
              ),
            ),
            const SizedBox(width: 12),
            Text(loadingStatusText, style: TextStyle(color: Colors.grey[600])),
          ],
        );

      case MasonryLoadMoreStatusState.failed:
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.error_outline, color: Colors.red[400], size: size),
            const SizedBox(height: 8),
            Text(
              failedStatusText,
              style: TextStyle(color: Colors.red[600]),
              textAlign: TextAlign.center,
            ),
            if (maxRetries > 0) ...[
              const SizedBox(height: 4),
              Text(
                'Retry $retryCount/$maxRetries',
                style: TextStyle(color: Colors.grey[500], fontSize: 11),
              ),
            ],
            const SizedBox(height: 4),
            Text(
              'Tap to retry',
              style: TextStyle(color: Colors.grey[500], fontSize: 12),
            ),
          ],
        );

      case MasonryLoadMoreStatusState.finished:
        return Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.check_circle_outline,
              color: Colors.green[400],
              size: size * 0.8,
            ),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                finishedStatusText,
                style: TextStyle(color: Colors.grey[600]),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
    }
  }
}

// Utility function for synchronized access (simplified mutex)
T synchronized<T>(Object lock, T Function() callback) {
  // In a real implementation, you might want to use a proper mutex
  // For now, this is a placeholder that executes the callback directly
  return callback();
}

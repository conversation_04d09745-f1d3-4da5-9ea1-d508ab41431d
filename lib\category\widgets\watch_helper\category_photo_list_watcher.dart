import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/providers/category_photo_list_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class CategoryPhotoListWatcher extends ConsumerWidget {
  final String slug;

  const CategoryPhotoListWatcher({super.key, required this.slug});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    late final NotifierProvider<CategoryPhotoListNotifier, List<PhotoData>>
    provider = getCategoryPhotoListProvider(slug);

    final List<PhotoData> photoList = ref.watch(provider);

    return photoList.isNotEmpty
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}

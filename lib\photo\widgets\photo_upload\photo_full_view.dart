import 'dart:io';

import 'package:flutter/material.dart';
import 'package:photo_view/photo_view.dart';

class PhotoFullView extends StatelessWidget {
  const PhotoFullView({super.key, required this.photoFile});

  final File photoFile;

  final Color _scaffoldColor = const Color.fromRGBO(8, 7, 7, 1.0);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: _scaffoldColor,
      body: Container(
        width: double.infinity,
        height: double.infinity,
        alignment: Alignment.center,
        child: _buildPhoto(),
      ),
    );
  }

  Widget _buildPhoto() {
    return PhotoView(
      imageProvider: FileImage(File(photoFile.path)),
      minScale: PhotoViewComputedScale.contained,
    );
  }
}

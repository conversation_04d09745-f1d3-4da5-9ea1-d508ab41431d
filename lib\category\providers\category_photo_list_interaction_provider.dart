import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/dto/category_photo_list_interaction_data.dart';

final class CategoryPhotoListInteractionNotifier
    extends Notifier<CategoryPhotoListInteractionData> {
  @override
  CategoryPhotoListInteractionData build() =>
      CategoryPhotoListInteractionData();

  void setLoadMoreLastId(int lastId) {
    state = state.copyWith(loadMoreLastId: lastId);
  }

  void setLastItemSeenId(int lastItemSeemId) {
    state = state.copyWith(lastItemSeenId: lastItemSeemId);
  }

  void replace(CategoryPhotoListInteractionData data) {
    state = data;
  }

  void reset() {
    state = CategoryPhotoListInteractionData();
  }
}

Map categoryPhotoListInteractionProviderMap = {};

NotifierProvider<
  CategoryPhotoListInteractionNotifier,
  CategoryPhotoListInteractionData
>
getCategoryPhotoListInteractionProvider(String slug) {
  if (categoryPhotoListInteractionProviderMap.containsKey(slug)) {
    return categoryPhotoListInteractionProviderMap[slug];
  }

  categoryPhotoListInteractionProviderMap[slug] =
      NotifierProvider.autoDispose<
        CategoryPhotoListInteractionNotifier,
        CategoryPhotoListInteractionData
      >(CategoryPhotoListInteractionNotifier.new);

  return categoryPhotoListInteractionProviderMap[slug];
}

void deleteCategoryPhotoListInteractionProvider(String slug) {
  if (categoryPhotoListInteractionProviderMap.containsKey(slug)) {
    categoryPhotoListInteractionProviderMap.remove(slug);
  }
}

void clearCategoryPhotoListInteractionProviders() {
  categoryPhotoListInteractionProviderMap.clear();
}

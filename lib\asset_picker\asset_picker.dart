import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/services/album_service.dart';
import 'package:portraitmode/asset_picker/services/asset_service.dart';
import 'package:portraitmode/asset_picker/widgets/gallery_screen.dart';

class AssetPicker extends StatefulWidget {
  final int perPage;
  final int gridAxisCount;
  final Function()? onTap;
  final Function(AssetEntity?)? onPick;
  final Widget child;

  const AssetPicker({
    super.key,
    this.perPage = 33,
    this.gridAxisCount = 3,
    this.onTap,
    this.onPick,
    required this.child,
  });

  @override
  AssetPickerState createState() => AssetPickerState();
}

class AssetPickerState extends State<AssetPicker> {
  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(onTap: _handleOnTap, child: widget.child);
  }

  void _handleOnTap() async {
    widget.onTap?.call();

    late final PermissionState permissionState;

    try {
      permissionState = await PhotoManager.requestPermissionExtend();
    } catch (e) {
      permissionState = PermissionState.notDetermined;
    }

    log('permissionState: $permissionState');

    if (!permissionState.isAuth && !permissionState.hasAccess) {
      await PhotoManager.openSetting();
      return;
    }

    final albumEntityList = await AlbumService().fetch();

    if (albumEntityList.isEmpty) {
      return;
    }

    final assetEntityList = await AssetService().fetch(
      pathEntity: albumEntityList[0],
      page: 0,
      perPage: widget.perPage,
    );

    // Navigate to EntityGridScreen widget using animation from bottom to top.
    final AssetEntity? entity = !mounted
        ? null
        : await Navigator.of(context).push<AssetEntity?>(
            PageRouteBuilder<AssetEntity?>(
              pageBuilder:
                  (
                    BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation,
                  ) {
                    return GalleryScreen(
                      gridAxisCount: widget.gridAxisCount,
                      gridItemSize: 220,
                      gridImageQuality: 90,
                      perPage: widget.perPage,
                      initialPage: 1,
                      initialAlbumEntityList: albumEntityList,
                      initialAssetEntityList: assetEntityList,
                    );
                  },
              transitionsBuilder:
                  (
                    BuildContext context,
                    Animation<double> animation,
                    Animation<double> secondaryAnimation,
                    Widget child,
                  ) {
                    const begin = Offset(0.0, 1.0);
                    const end = Offset.zero;
                    const curve = Curves.ease;

                    var tweeen = Tween(
                      begin: begin,
                      end: end,
                    ).chain(CurveTween(curve: curve));

                    return SlideTransition(
                      position: animation.drive(tweeen),
                      child: child,
                    );
                  },
            ),
          );

    widget.onPick?.call(entity);
  }
}

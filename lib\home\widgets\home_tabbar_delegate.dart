import 'package:flutter/material.dart';
import 'package:portraitmode/home/<USER>/home_tabbar.dart';

class HomeTabbarDelegate extends SliverPersistentHeaderDelegate {
  final TabController? controller;

  HomeTabbarDelegate({this.controller});

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return HomeTabbar(controller: controller);
  }

  @override
  double get maxExtent => 45.0;

  @override
  double get minExtent => 45.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/widget/blog_post_list_item_text_mode.dart';

class BlogPostListSlider extends StatelessWidget {
  final List<BlogPostData> postList;
  final bool isLoading;

  const BlogPostListSlider({
    super.key,
    this.postList = const [],
    this.isLoading = false,
  });

  final double _sliderItemGap = 13.0;
  final double _heightReducer = 0.0;

  @override
  Widget build(BuildContext context) {
    int totalItems = !isLoading ? postList.length : 5;

    double screenWidth = MediaQuery.sizeOf(context).width;
    double sliderItemWidth = (screenWidth / 1.1) - _sliderItemGap;
    double itemHeight =
        sliderItemWidth - (sliderItemWidth / 1.5) - _heightReducer;
    double sliderItemHeight = (itemHeight * 1.8) + _sliderItemGap;

    return !isLoading && postList.isEmpty
        ? const SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.only(
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                  bottom: 10.0,
                ),
                child: Text(
                  "From the blog",
                  style: TextStyle(
                    fontSize: SliderConfig.sliderTitleFontSize,
                    fontWeight: SliderConfig.sliderTitleFontWeight,
                  ),
                ),
              ),
              SizedBox(
                // height: sliderItemHeight,
                child: SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  // Use for loop not ListView
                  child: IntrinsicHeight(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: List.generate(totalItems, (index) {
                        return Padding(
                          padding: EdgeInsets.only(
                            left: _sliderItemGap,
                            right: index == totalItems - 1 ? _sliderItemGap : 0,
                          ),
                          child: SizedBox(
                            width: sliderItemWidth,
                            child: !isLoading
                                ? BlogPostListItemTextMode(
                                    post: postList[index],
                                    borderRadius: PhotoStyleConfig.borderRadius,
                                  )
                                : _buildLoadingItem(
                                    sliderItemHeight,
                                    context.colors.baseColorAlt,
                                  ),
                          ),
                        );
                      }),
                    ),
                  ),
                ),
              ),
            ],
          );
  }

  Widget _buildLoadingItem(double height, Color color) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(PhotoStyleConfig.borderRadius),
      ),
    );
  }
}

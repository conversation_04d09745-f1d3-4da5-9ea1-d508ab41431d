import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/category_detail_screen_masonry_mode.dart';
import 'package:portraitmode/category/widgets/watch_helper/category_photo_list_interaction_watcher.dart';
import 'package:portraitmode/category/widgets/watch_helper/category_photo_list_watcher.dart';

class CategoryListItem extends StatelessWidget {
  const CategoryListItem({
    super.key,
    required this.category,
    this.showCount = false,
    this.borderRadius,
    this.isSquare = false,
    this.heightReducer = 0.0,
  });

  final CategoryData category;
  final bool showCount;
  final double? borderRadius;
  final bool isSquare;
  final double heightReducer;

  @override
  Widget build(BuildContext context) {
    // log('Building category card with name is ${category.displayName}');

    double borderRadiusValue = borderRadius == null
        ? PhotoStyleConfig.borderRadius
        : borderRadius!;

    return LayoutBuilder(
      builder: (BuildContext layoutBuildContext, BoxConstraints constraints) {
        double itemWidth = constraints.maxWidth;
        // double itemWidth = double.infinity;
        double itemHeight = isSquare
            ? itemWidth
            : itemWidth - (itemWidth / 6) - heightReducer;
        // double itemHeight = 100;

        return SizedBox(
          width: itemWidth,
          height: itemHeight,
          child: GestureDetector(
            onTap: () => _onTap(context),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (category.latestPhotoUrl.isNotEmpty)
                  ClipRRect(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    child: PmNetworkImage(
                      url: category.latestPhotoUrl,
                      width: itemWidth,
                      height: itemHeight,
                    ),
                  ),
                Container(
                  height: itemHeight,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.all(
                      Radius.circular(borderRadiusValue),
                    ),
                    // color: Colors.black.withValues(alpha: 0.5),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.6),
                        Colors.black.withValues(alpha: 0.2),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Center(
                    child: Text(
                      category.name,
                      style: const TextStyle(
                        fontSize: 12.0,
                        fontWeight: FontWeight.w700,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
                CategoryPhotoListInteractionWatcher(slug: category.slug),
                CategoryPhotoListWatcher(slug: category.slug),
              ],
            ),
          ),
        );
      },
    );
  }

  void _onTap(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            CategoryDetailScreenMasonryMode(category: category),
      ),
    );
  }
}

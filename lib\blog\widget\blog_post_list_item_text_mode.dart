import 'package:date_format/date_format.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';

import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/widget/read_more_button.dart';

class BlogPostListItemTextMode extends StatelessWidget {
  final BlogPostData post;
  final double? borderRadius;

  const BlogPostListItemTextMode({
    super.key,
    required this.post,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    final date = DateTime.parse(post.createdAt);
    final formattedDate = formatDate(date, [dd, ' ', MM, ' ', yyyy]);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24.0, vertical: 22.0),
      // height: containerHeight,
      decoration: BoxDecoration(
        borderRadius: borderRadius != null
            ? BorderRadius.all(Radius.circular(borderRadius!))
            : null,
        color: context.colors.lightColor,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () => _handleReadMoreTap(context, post.url),
            child: Text(
              post.title,
              maxLines: 3,
              style: TextStyle(
                fontSize: 19,
                height: 1.3,
                fontWeight: FontWeight.w600,
                color: context.colors.brandColor,
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ),
          const SizedBox(height: 10.0),
          Wrap(
            crossAxisAlignment: WrapCrossAlignment.center,
            children: [
              if (post.authorAvatarUrl.isNotEmpty)
                Avatar(
                  imageUrl: post.authorAvatarUrl,
                  onTap: () => _gotoArtistDetailScreen(context),
                ),
              const SizedBox(width: 8.0),
              GestureDetector(
                onTap: () => _gotoArtistDetailScreen(context),
                child: Text(
                  post.authorDisplayName,
                  style: TextStyle(
                    fontSize: 13.0,
                    color: context.colors.contentLighterColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              const SizedBox(width: 4.0),
              Text(
                "|",
                style: TextStyle(
                  fontSize: 13.0,
                  color: context.colors.contentLighterColor,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 4.0),
              GestureDetector(
                onTap: () => _handleReadMoreTap(context, post.url),
                child: Text(
                  formattedDate,
                  style: TextStyle(
                    fontSize: 13.0,
                    color: context.colors.contentLighterColor,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 10.0),
          Text(
            post.excerpt,
            maxLines: 15,
            style: TextStyle(
              height: 1.5,
              fontSize: 14.5,
              color: context.colors.contentLighterColor,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 18.0),
          const Expanded(child: SizedBox.shrink()),
          ReadMoreButton(
            onPressed: () => _handleReadMoreTap(context, post.url),
          ),
        ],
      ),
    );
  }

  void _gotoArtistDetailScreen(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: post.authorId,
            nicename: post.authorNicename,
            displayName: post.authorDisplayName,
            avatarUrl: post.authorAvatarUrl,
          ),
        ),
      ),
    );
  }

  void _handleReadMoreTap(BuildContext context, String url) {
    final InAppBrowser browser = InAppBrowser();

    final settings = InAppBrowserClassSettings(
      browserSettings: InAppBrowserSettings(
        hideDefaultMenuItems: true,
        hideToolbarBottom: true,
        hideToolbarTop: true,
        hideUrlBar: true,
      ),
      webViewSettings: InAppWebViewSettings(
        javaScriptEnabled: true,
        isInspectable: kDebugMode,
      ),
    );

    browser.openUrlRequest(
      urlRequest: URLRequest(url: WebUri(url)),
      settings: settings,
    );
  }
}

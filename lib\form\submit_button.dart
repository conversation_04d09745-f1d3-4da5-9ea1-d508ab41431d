import 'package:easy_loading_button/easy_loading_button.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:portraitmode/app/config/colors.dart';

class SubmitButton extends StatelessWidget {
  final Color? bgColor;
  final String buttonText;
  final double width;
  final double height;
  final double fontSize;
  final FontWeight fontWeight;
  final Function? onPressed;

  const SubmitButton({
    super.key,
    this.bgColor,
    this.buttonText = '',
    this.width = 150.0,
    this.height = 40.0,
    this.fontSize = 16.0,
    this.fontWeight = FontWeight.w700,
    this.onPressed,
  });

  @override
  Widget build(BuildContext context) {
    return EasyButton(
      type: EasyButtonType.elevated,
      idleStateWidget: Text(
        buttonText,
        style: GoogleFonts.inter(
          textStyle: TextStyle(
            fontSize: fontSize,
            fontWeight: fontWeight,
            color: Colors.white,
          ),
        ),
      ),
      loadingStateWidget: const CircularProgressIndicator(
        strokeWidth: 3.0,
        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
      ),
      useEqualLoadingStateWidgetDimension: true,
      useWidthAnimation: false,
      elevation: 0.0,
      width: width,
      height: height,
      borderRadius: 4.0,
      contentGap: 10.0,
      buttonColor: bgColor ?? context.colors.accentColor,
      onPressed: onPressed,
    );
  }
}

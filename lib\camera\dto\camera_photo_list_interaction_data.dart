import 'package:flutter/foundation.dart';

@immutable
class CameraPhotoListInteractionData {
  final int loadMoreLastId;
  final int lastItemSeenId;

  const CameraPhotoListInteractionData({
    this.loadMoreLastId = 0,
    this.lastItemSeenId = 0,
  });

  CameraPhotoListInteractionData copyWith({
    int? loadMoreLastId,
    int? lastItemSeenId,
  }) {
    return CameraPhotoListInteractionData(
      loadMoreLastId: loadMoreLastId ?? this.loadMoreLastId,
      lastItemSeenId: lastItemSeenId ?? this.lastItemSeenId,
    );
  }

  Map<String, dynamic> toMap() {
    return {'loadMoreLastId': loadMoreLastId, 'lastItemSeenId': lastItemSeenId};
  }

  factory CameraPhotoListInteractionData.fromMap(Map<String, dynamic> data) {
    return CameraPhotoListInteractionData(
      loadMoreLastId: data['loadMoreLastId'],
      lastItemSeenId: data['lastItemSeenId'],
    );
  }
}

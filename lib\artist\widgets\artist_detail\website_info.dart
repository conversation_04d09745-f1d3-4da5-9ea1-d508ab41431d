import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:url_launcher/url_launcher.dart';

class WebsiteInfo extends StatelessWidget {
  const WebsiteInfo({super.key, required this.website, this.useIcon = false});

  final String website;
  final bool useIcon;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        if (useIcon)
          Icon(
            Ionicons.link_outline,
            size: 15.0,
            color: context.colors.primarySwatch[200],
          ),
        if (useIcon) const SizedBox(width: 4.0),
        Expanded(
          child: InkWell(
            onTap: () async {
              await _handleWebsiteTap(context, website);
            },
            child: Text(
              website,
              style: TextStyle(color: context.colors.primarySwatch[200]),
              overflow: TextOverflow.ellipsis,
              maxLines: 2,
            ),
          ),
        ),
      ],
    );
  }

  Future<void> _handleWebsiteTap(BuildContext context, String website) async {
    Uri url = Uri.parse(website);

    if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            duration: Duration(seconds: 2),
            content: Text('Could not launch website'),
          ),
        );
      }
    }
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/hive/services/local_settings_service.dart';

class ThemeManager extends ValueNotifier<ThemeMode> {
  static ThemeManager? _instance;
  static ThemeManager get instance => _instance ??= ThemeManager._();

  ThemeManager._() : super(ThemeMode.system) {
    loadThemeFromHive();
  }

  ThemeMode get themeMode => value;

  ThemeMode get materialThemeMode {
    switch (value) {
      case ThemeMode.light:
        return ThemeMode.light;
      case ThemeMode.dark:
        return ThemeMode.dark;
      case ThemeMode.system:
        return ThemeMode.system;
    }
  }

  Future<void> loadThemeFromHive() async {
    value = LocalSettingsService.themeMode;
  }

  Future<void> setThemeMode(ThemeMode mode) async {
    if (value == mode) return;
    await LocalSettingsService.setThemeMode(mode);
    value = mode;
  }

  // Convenience methods
  Future<void> setLightTheme() => setThemeMode(ThemeMode.light);
  Future<void> setDarkTheme() => setThemeMode(ThemeMode.dark);
  Future<void> setSystemTheme() => setThemeMode(ThemeMode.system);
}

// Extension for easy access
extension ThemeManagerExtension on BuildContext {
  ThemeManager get themeManager => ThemeManager.instance;
}

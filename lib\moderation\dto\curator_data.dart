class CuratorData {
  final int id;
  final String nicename;
  final String displayName;
  final String avatarUrl;
  final String profileUrl;

  CuratorData({
    this.id = 0,
    this.nicename = "",
    this.displayName = "",
    this.avatarUrl = "",
    this.profileUrl = "",
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'nicename': nicename,
      'displayName': displayName,
      'avatarUrl': avatarUrl,
      'profileUrl': profileUrl,
    };
  }

  // Create copyWith
  CuratorData copyWith({
    int? id,
    String? nicename,
    String? displayName,
    String? avatarUrl,
    String? profileUrl,
  }) {
    return CuratorData(
      id: id ?? this.id,
      nicename: nicename ?? this.nicename,
      displayName: displayName ?? this.displayName,
      avatarUrl: avatarUrl ?? this.avatarUrl,
      profileUrl: profileUrl ?? this.profileUrl,
    );
  }

  factory CuratorData.fromMap(Map<String, dynamic> data) {
    return CuratorData(
      id: data['id'] ?? 0,
      nicename: data['nicename'] ?? "",
      displayName: data['displayName'] ?? "",
      avatarUrl: data['avatarUrl'] ?? "",
      profileUrl: data['profileUrl'] ?? "",
    );
  }
}

// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/app/http_responses/initial_data_response.dart';

// Internal packages.
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class AppService extends BaseService {
  Future<InitialDataResponse> fetchInitialData() async {
    try {
      final response = await http.get('${URL.baseApiUrl}/initial-data');

      InitialDataResponse initialDataResponse = InitialDataResponse.fromMap(
        response.data,
      );

      return initialDataResponse;
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return InitialDataResponse.fromMap(e.response?.data);
      }

      return InitialDataResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return InitialDataResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

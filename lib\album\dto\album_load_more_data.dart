import 'package:portraitmode/photo/dto/photo_data.dart';

class AlbumLoadMoreData {
  AlbumLoadMoreData({
    this.slug = '',
    this.lastId = 0,
    this.endReached = false,
    this.photos = const [],
  });

  String slug;
  int lastId;
  bool endReached;
  List<PhotoData> photos;

  AlbumLoadMoreData copyWith({
    String? slug,
    int? lastId,
    bool? endReached,
    List<PhotoData>? photos,
  }) {
    return AlbumLoadMoreData(
      slug: slug ?? this.slug,
      lastId: lastId ?? this.lastId,
      endReached: endReached ?? this.endReached,
      photos: photos ?? this.photos,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'slug': slug,
      'lastId': lastId,
      'endReached': endReached,
      'photos': photos.map((x) => x.toMap()).toList(),
    };
  }

  factory AlbumLoadMoreData.fromMap(Map<String, dynamic> data) {
    return AlbumLoadMoreData(
      slug: data['slug'],
      lastId: data['lastId'],
      endReached: data['endReached'],
      photos: data['photos'],
    );
  }
}

import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/home/<USER>/following_photos_tab_content.dart';
import 'package:portraitmode/home/<USER>/home_app_bar_delegate.dart';
import 'package:portraitmode/home/<USER>/home_tabbar_delegate.dart';
import 'package:portraitmode/home/<USER>/latest_photos_tab_content.dart';

enum SwipeDirection { left, right, none }

class HomeScreen extends ConsumerStatefulWidget {
  final int? initialIndex;
  final Function(Function)? onInit;

  const HomeScreen({super.key, this.initialIndex, this.onInit});

  @override
  HomeScreenState createState() => HomeScreenState();
}

class HomeScreenState extends ConsumerState<HomeScreen>
    with TickerProviderStateMixin {
  // final _scrollController = ScrollController();

  late TabController _tabController;

  double? _previousAnimationValue;
  SwipeDirection _swipeDirection = SwipeDirection.none;
  double dividerBarWidth = 2.0;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(
      length: 2,
      vsync: this,
      initialIndex: widget.initialIndex ?? 0,
    );

    _tabController.animation!.addListener(_handleTabSwipe);
    widget.onInit?.call(_scrollToTop);
  }

  @override
  void dispose() {
    _tabController.animation!.removeListener(_handleTabSwipe);

    _tabController.dispose();
    // _scrollController.dispose();
    super.dispose();
  }

  void _handleTabSwipe() {
    if (_previousAnimationValue == null) {
      _previousAnimationValue = _tabController.animation!.value;
      return;
    }

    if (_tabController.animation!.value % 1 == 0) {
      log("Swipe completed");
      if (mounted) {
        setState(() {
          _swipeDirection = SwipeDirection.none;
        });
      }
    } else {
      SwipeDirection direction = SwipeDirection.none;

      if (_tabController.animation!.value > _previousAnimationValue!) {
        // Handle right swipe
        // log("Swiping to the right");
        direction = SwipeDirection.right;
      } else if (_tabController.animation!.value < _previousAnimationValue!) {
        // Handle left swipe
        // log("Swiping to the left");
        direction = SwipeDirection.left;
      }

      if (mounted && direction != _swipeDirection) {
        setState(() {
          _swipeDirection = direction;
        });
      }
    }

    _previousAnimationValue = _tabController.animation!.value;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      body: Padding(
        padding: const EdgeInsets.only(top: 0.0),
        child: NestedScrollView(
          // controller: _scrollController,
          floatHeaderSlivers: true,
          headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
            return [
              SliverOverlapAbsorber(
                handle: NestedScrollView.sliverOverlapAbsorberHandleFor(
                  context,
                ),
              ),
              SliverPersistentHeader(
                pinned: true,
                floating: false,
                delegate: HomeAppBarDelegate(),
              ),
              SliverPersistentHeader(
                pinned: true,
                floating: false,
                delegate: HomeTabbarDelegate(controller: _tabController),
              ),
            ];
          },
          body: TabBarView(
            controller: _tabController,
            children: [
              Stack(
                children: [
                  LatestPhotosTabContent(key: globalLatestPhotosTabContentKey),
                  if (_swipeDirection == SwipeDirection.right)
                    Positioned(
                      top: 0.0,
                      right: 0.0,
                      child: Container(
                        width: dividerBarWidth,
                        height: MediaQuery.of(context).size.height,
                        color: context.colors.dividerColor,
                      ),
                    ),
                ],
              ),
              Stack(
                children: [
                  FollowingPhotosTabContent(
                    key: globalFollowingPhotosTabContentKey,
                  ),
                  if (_swipeDirection == SwipeDirection.left)
                    Positioned(
                      top: 0.0,
                      left: 0.0,
                      child: Container(
                        width: dividerBarWidth,
                        height: MediaQuery.of(context).size.height,
                        color: context.colors.dividerColor,
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _scrollToTop() async {
    final int currentTabIndex = _tabController.index;

    log("Scrolling to top of tab index $currentTabIndex");

    if (currentTabIndex == 0) {
      final latestPhotosTabContentState =
          globalLatestPhotosTabContentKey.currentState;

      latestPhotosTabContentState?.scrollToTop();
      return;
    }

    if (currentTabIndex == 1) {
      final followingPhotosTabContentState =
          globalFollowingPhotosTabContentKey.currentState;

      followingPhotosTabContentState?.scrollToTop();
      return;
    }
  }
}

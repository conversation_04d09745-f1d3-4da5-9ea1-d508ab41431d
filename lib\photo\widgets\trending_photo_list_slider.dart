import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/potd/widgets/potd_item.dart';

class TrendingPhotoListSlider extends StatelessWidget {
  final String title;
  final List<PhotoData> photoList;
  final bool isLoading;

  const TrendingPhotoListSlider({
    super.key,
    this.title = 'Currently trending',
    this.photoList = const [],
    this.isLoading = false,
  });

  final double _sliderItemGap = 13.0;
  final double _heightReducer = 0.0;

  @override
  Widget build(BuildContext context) {
    int totalItems = !isLoading ? photoList.length : 5;

    double screenWidth = MediaQuery.sizeOf(context).width;

    double sliderItemWidth = (screenWidth / 1.3) - _sliderItemGap;

    double itemHeight =
        sliderItemWidth - (sliderItemWidth / 1.5) - _heightReducer;

    double sliderItemHeight = (itemHeight * 1.8) + _sliderItemGap;

    return !isLoading && photoList.isEmpty
        ? const SizedBox.shrink()
        : Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  bottom: 10.0,
                  left: ScreenStyleConfig.horizontalPadding,
                  right: ScreenStyleConfig.horizontalPadding,
                ),
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: SliderConfig.sliderTitleFontSize,
                    fontWeight: SliderConfig.sliderTitleFontWeight,
                  ),
                ),
              ),
              SizedBox(
                height: sliderItemHeight + 30.0,
                child: ListView.builder(
                  cacheExtent:
                      (sliderItemWidth * totalItems) +
                      (_sliderItemGap * totalItems),
                  scrollDirection: Axis.horizontal,
                  itemCount: totalItems,
                  itemBuilder: (BuildContext context, int index) {
                    // Check if photoList[index] exists.
                    if (!isLoading && index >= photoList.length) {
                      return const SizedBox.shrink();
                    }

                    return Padding(
                      padding: EdgeInsets.only(
                        left: _sliderItemGap,
                        right: index == totalItems - 1 ? _sliderItemGap : 0,
                      ),
                      child: SizedBox(
                        width: sliderItemWidth,
                        child: !isLoading
                            ? PotdItem(
                                photo: photoList[index],
                                text:
                                    'Photo by @${photoList[index].authorNicename}',
                                width: sliderItemWidth,
                                height: sliderItemHeight,
                                screenName: 'SearchScreen',
                              )
                            : Container(
                                height: sliderItemHeight,
                                decoration: BoxDecoration(
                                  color: context.colors.baseColorAlt,
                                  borderRadius: BorderRadius.circular(
                                    PhotoStyleConfig.borderRadius,
                                  ),
                                ),
                              ),
                      ),
                    );
                  },
                ),
              ),
            ],
          );
  }
}

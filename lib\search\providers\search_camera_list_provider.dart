import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';

final class SearchCameraListNotifier extends Notifier<List<CameraData>> {
  final Map<int, int> _idIndexCache = {};

  @override
  List<CameraData> build() => [];

  void _updateState(List<CameraData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  CameraData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  void addItem(CameraData newItem) {
    if (hasItem(newItem.id)) return;
    final updated = [...state, newItem];
    _updateState(updated);
  }

  void addItemThenReorder(CameraData newItem) {
    if (hasItem(newItem.id)) return;
    final updated = [...state, newItem];
    updated.sort((a, b) => b.id.compareTo(a.id));
    _updateState(updated);
  }

  void addItems(List<CameraData> newItems) {
    final existingIds = _idIndexCache.keys.toSet();
    final filtered = newItems.where((item) => !existingIds.contains(item.id));
    if (filtered.isEmpty) return;

    final updated = [...state, ...filtered];
    _updateState(updated);
  }

  void updateItem(CameraData newItem) {
    final index = _idIndexCache[newItem.id];
    if (index == null || index >= state.length) return;

    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  void removeItem(int id) {
    if (!_idIndexCache.containsKey(id)) return;
    final updated = state.where((item) => item.id != id).toList();
    _updateState(updated);
  }

  void replaceAll(List<CameraData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }
}

final searchCameraListProvider =
    NotifierProvider.autoDispose<SearchCameraListNotifier, List<CameraData>>(
      SearchCameraListNotifier.new,
    );

class SystemStatusHistoryData {
  final String headline;
  final String description;
  final String date;
  final bool resolved;

  SystemStatusHistoryData({
    this.headline = '',
    this.description = '',
    this.date = '',
    this.resolved = false,
  });

  factory SystemStatusHistoryData.fromMap(Map<String, dynamic> map) {
    return SystemStatusHistoryData(
      headline: map['headline'] ?? '',
      description: map['description'] ?? '',
      date: map['date'] ?? '',
      resolved: map['resolved'] ?? false,
    );
  }
}

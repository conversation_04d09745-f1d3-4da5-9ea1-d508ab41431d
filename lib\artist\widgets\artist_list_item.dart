// Core packages
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
// Extension packages

// Custom packages
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/utils/artist_util.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/artist/widgets/follow_button.dart';

final class ArtistListItem extends ConsumerWidget {
  const ArtistListItem({
    super.key,
    required this.index,
    required this.artist,
    this.isFollowingScreen = false,
    this.isOwnProfile = false,
  });

  final int index;
  final ArtistData artist;
  final bool isFollowingScreen;
  final bool isOwnProfile;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Avatar(
          imageUrl: artist.avatarUrl,
          size: 65.0,
          useBorder: true,
          borderColor: context.colors.scaffoldColor,
          isOwnAvatar: isOwnProfile,
          onTap: () => _handleArtistTap(context),
        ),
        const SizedBox(width: 10.0),
        Expanded(
          child: GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () => _handleArtistTap(context),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  artist.displayName,
                  style: const TextStyle(
                    fontSize: 14.0,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 3.0),
                Text(
                  '${artist.totalPhotos.toString()} photos',
                  style: TextStyle(
                    fontSize: 13.0,
                    color: context.isDarkMode
                        ? AppColorsCache.dark().darkerGreyColor
                        : AppColorsCache.light().primarySwatch[300],
                  ),
                ),
                const SizedBox(height: 2.0),
                Text(
                  artist.location,
                  style: TextStyle(
                    fontSize: 13.0,
                    color: context.isDarkMode
                        ? AppColorsCache.dark().darkerGreyColor
                        : AppColorsCache.light().primarySwatch[300],
                  ),
                ),
              ],
            ),
          ),
        ),
        if (isFollowingScreen) const SizedBox(width: 10.0),
        if (isFollowingScreen)
          FollowButton(
            artistId: artist.id,
            isFollowing: artist.isFollowing,
            onFollowStatusChanged: (action, data) => handleFollowStatusChanged(
              ref: ref,
              action: action,
              artist: artist,
              data: data,
            ),
          ),
      ],
    );
  }

  void _handleArtistTap(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: artist.id,
            nicename: artist.nicename,
            displayName: artist.displayName,
            profileUrl: artist.profileUrl,
            avatarUrl: artist.avatarUrl,
            membershipType: artist.membershipType,
          ),
        ),
      ),
    );
  }
}

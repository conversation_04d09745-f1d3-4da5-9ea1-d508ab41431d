class ArtistPartialData {
  final int id;
  final String nicename;
  final String displayName;
  final String profileUrl;
  final String avatarUrl;
  final String membershipType;

  ArtistPartialData({
    this.id = 0,
    this.nicename = '',
    this.displayName = '',
    this.profileUrl = '',
    this.avatarUrl =
        'https://portraitmode.io/wp-content/themes/page-builder-framework-child/images/portraitmode-logo-circle-256x256.png',
    this.membershipType = '',
  });
}

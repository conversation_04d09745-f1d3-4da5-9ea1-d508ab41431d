import 'package:portraitmode/youtube/dto/youtube_channel_thumbnail_data.dart';

class YoutubeChannelData {
  final String id;
  final String title;
  final String description;
  final String customUrl;
  final String publishedAt;
  final List<YoutubeChannelThumbnailData> thumbnails;

  YoutubeChannelData({
    this.id = '',
    this.title = '',
    this.description = '',
    this.customUrl = '',
    this.publishedAt = '',
    this.thumbnails = const [],
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'customUrl': customUrl,
      'publishedAt': publishedAt,
      'thumbnails': thumbnails.map((e) => e.toMap()).toList(),
    };
  }

  factory YoutubeChannelData.fromMap(Map<String, dynamic> data) {
    return YoutubeChannelData(
      id: data['id'] ?? '',
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      customUrl: data['customUrl'] ?? '',
      publishedAt: data['publishedAt'] ?? '',
      thumbnails: data['thumbnails'] is List
          ? data['thumbnails']
                .map<YoutubeChannelThumbnailData>(
                  (thumbnail) => YoutubeChannelThumbnailData.fromMap(thumbnail),
                )
                .toList()
          : [],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

/// Widget that only rebuilds when theme brightness changes.
/// Use this to wrap widgets that need to respond to theme changes.
class ThemeColorBuilder extends StatelessWidget {
  final Widget Function(BuildContext context, AppColors colors) builder;

  const ThemeColorBuilder({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    // This widget will only rebuild when Theme.of(context).brightness changes
    // which happens when MaterialApp's theme/darkTheme changes.
    return builder(context, context.colors);
  }
}

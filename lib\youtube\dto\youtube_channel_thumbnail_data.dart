class YoutubeChannelThumbnailData {
  final String url;
  final int width;
  final int height;

  YoutubeChannelThumbnailData({this.url = '', this.width = 0, this.height = 0});

  Map<String, dynamic> toMap() {
    return {'url': url, 'width': width, 'height': height};
  }

  factory YoutubeChannelThumbnailData.fromMap(Map<String, dynamic> data) {
    return YoutubeChannelThumbnailData(
      url: data['url'] ?? '',
      width: data['width'] ?? 0,
      height: data['height'] ?? 0,
    );
  }
}

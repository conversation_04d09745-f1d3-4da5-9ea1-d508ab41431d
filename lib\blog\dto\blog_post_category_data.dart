class BlogPostCategoryData {
  final int id;
  final String slug;
  final String name;
  final String url;

  BlogPostCategoryData({
    this.id = 0,
    this.slug = '',
    this.name = '',
    this.url = '',
  });

  factory BlogPostCategoryData.fromMap(Map<String, dynamic> map) {
    return BlogPostCategoryData(
      id: map['id'] ?? 0,
      slug: map['slug'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {'id': id, 'slug': slug, 'name': name, 'url': url};
  }
}

import 'package:portraitmode/artist/dto/artist_data.dart';

class ArtistResponse {
  final bool success;
  final String errorCode;
  final String message;
  final ArtistData? data;

  ArtistResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory ArtistResponse.fromMap(Map<String, dynamic> map) {
    final Map<String, dynamic>? mapData = map['data'] is List
        ? null
        : map['data'];

    return ArtistResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: mapData != null ? ArtistData.fromMap(map['data']) : null,
    );
  }
}

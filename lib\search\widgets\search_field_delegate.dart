import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/search/widgets/search_field.dart';

class SearchFieldDelegate extends SliverPersistentHeaderDelegate {
  final TextEditingController controller;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final String hintText;
  final double verticalPadding;

  SearchFieldDelegate({
    required this.controller,
    this.onChanged,
    this.onSubmitted,
    this.hintText = 'Search...',
    this.verticalPadding = 18.0,
  });

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.only(
          right: ScreenStyleConfig.horizontalPadding,
        ),
        child: Row(
          children: [
            BackButton(onPressed: () => Navigator.pop(context)),
            Expanded(
              child: SearchField(
                controller: controller,
                onChanged: onChanged,
                onSubmitted: onSubmitted,
                hintText: hintText,
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  double get maxExtent => SearchConfig.searchFieldHeight + verticalPadding;

  @override
  double get minExtent => SearchConfig.searchFieldHeight + verticalPadding;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => false;
}

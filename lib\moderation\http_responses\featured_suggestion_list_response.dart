import 'package:portraitmode/moderation/dto/featured_suggestion_photo_data.dart';

class FeaturedSuggestionListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<FeaturedSuggestionPhotoData> data;

  FeaturedSuggestionListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory FeaturedSuggestionListResponse.fromMap(Map<String, dynamic> map) {
    return FeaturedSuggestionListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<FeaturedSuggestionPhotoData>(
                  (data) => FeaturedSuggestionPhotoData.fromMap(data),
                )
                .toList()
          : [],
    );
  }
}

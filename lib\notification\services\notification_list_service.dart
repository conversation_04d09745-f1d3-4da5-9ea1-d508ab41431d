import 'package:dio/dio.dart';
import 'package:portraitmode/notification/http_responses/notification_fetch_response.dart';
import 'package:portraitmode/base/base_service.dart';

class NotificationListService extends BaseService {
  NotificationListService() : super();

  Future<NotificationFetchResponse> fetch({
    String status = 'all',
    int limit = 50,
  }) async {
    try {
      final response = await http.get(
        '$apiUrl/notifications/$status/${limit.toString()}',
      );

      return NotificationFetchResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return NotificationFetchResponse.fromMap(e.response?.data);
      }

      return NotificationFetchResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    }
  }
}

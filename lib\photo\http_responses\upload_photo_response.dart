import 'package:portraitmode/photo/dto/photo_data.dart';

class UploadPhotoResponse {
  final bool success;
  final String errorCode;
  final String message;
  final PhotoData? data;

  UploadPhotoResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory UploadPhotoResponse.fromMap(Map<String, dynamic> map) {
    return UploadPhotoResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? PhotoData.fromMap(map['data'])
          : null,
    );
  }
}

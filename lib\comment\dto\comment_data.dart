import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/comment/enum.dart';

@immutable
final class CommentData {
  final int id;
  final int parentId;
  final int postId;
  final int status;
  final String date;
  final String dateGmt;
  final int authorId;
  final String authorNicename;
  final String authorProfileUrl;
  final String authorDisplayName;
  final String authorAvatarUrl;
  final String authorMembershipType;
  final String content;
  final bool isLiked;
  final int totalLikes;
  final int totalReplies;
  final List<CommentData> replies;

  /// This property is only available in the app version.
  final CommentSubmissionStatus submissionStatus;

  const CommentData({
    this.id = 0,
    this.parentId = 0,
    this.postId = 0,
    this.status = 1,
    this.date = '',
    this.dateGmt = '',
    this.authorId = 0,
    this.authorNicename = '',
    this.authorProfileUrl = '',
    this.authorDisplayName = '',
    this.authorAvatarUrl = '',
    this.authorMembershipType = '',
    this.content = '',
    this.isLiked = false,
    this.totalLikes = 0,
    this.totalReplies = 0,
    this.replies = const [],
    this.submissionStatus = CommentSubmissionStatus.submitted,
  });

  CommentData copyWith({
    int? id,
    int? parentId,
    int? postId,
    int? status,
    String? date,
    String? dateGmt,
    int? authorId,
    String? authorNicename,
    String? authorProfileUrl,
    String? authorDisplayName,
    String? authorAvatarUrl,
    String? authorMembershipType,
    String? content,
    bool? isLiked,
    int? totalLikes,
    int? totalReplies,
    List<CommentData>? replies,
    CommentSubmissionStatus? submissionStatus,
  }) {
    return CommentData(
      id: id ?? this.id,
      parentId: parentId ?? this.parentId,
      postId: postId ?? this.postId,
      status: status ?? this.status,
      date: date ?? this.date,
      dateGmt: dateGmt ?? this.dateGmt,
      authorId: authorId ?? this.authorId,
      authorNicename: authorNicename ?? this.authorNicename,
      authorProfileUrl: authorProfileUrl ?? this.authorProfileUrl,
      authorDisplayName: authorDisplayName ?? this.authorDisplayName,
      authorAvatarUrl: authorAvatarUrl ?? this.authorAvatarUrl,
      authorMembershipType: authorMembershipType ?? this.authorMembershipType,
      content: content ?? this.content,
      isLiked: isLiked ?? this.isLiked,
      totalLikes: totalLikes ?? this.totalLikes,
      totalReplies: totalReplies ?? this.totalReplies,
      replies: replies ?? this.replies,
      submissionStatus: submissionStatus ?? this.submissionStatus,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'parentId': parentId,
      'postId': postId,
      'status': status,
      'date': date,
      'dateGmt': dateGmt,
      'authorId': authorId,
      'authorNicename': authorNicename,
      'authorProfileUrl': authorProfileUrl,
      'authorDisplayName': authorDisplayName,
      'authorAvatarUrl': authorAvatarUrl,
      'authorMembershipType': authorMembershipType,
      'content': content,
      'isLiked': isLiked,
      'totalLikes': totalLikes,
      'totalReplies': totalReplies,
      'replies': replies.map((x) => x.toMap()).toList(),
    };
  }

  factory CommentData.fromMap(Map<String, dynamic> data) {
    return CommentData(
      id: data['id'] ?? 0,
      parentId: data['parentId'] ?? 0,
      postId: data['postId'] ?? 0,
      status: data['status'] ?? 1,
      date: data['date'] ?? '',
      dateGmt: data['dateGmt'] ?? '',
      authorId: data['authorId'] ?? 0,
      authorNicename: data['authorNicename'] ?? '',
      authorProfileUrl: data['authorProfileUrl'] ?? '',
      authorDisplayName: data['authorDisplayName'] ?? '',
      authorAvatarUrl: data['authorAvatarUrl'] ?? AvatarConfig.defaultAvatar,
      authorMembershipType: data['authorMembershipType'] ?? '',
      content: data['content'] ?? '',
      isLiked: data['isLiked'] ?? false,
      totalLikes: data['totalLikes'] ?? 0,
      totalReplies: data['totalReplies'] ?? 0,
      replies:
          data['replies'] != null &&
              data['replies'] is List &&
              data['replies'].length > 0
          ? List<CommentData>.from(
              data['replies'].map((x) => CommentData.fromMap(x)),
            )
          : [],
    );
  }

  factory CommentData.fromJson(String source) =>
      CommentData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(
    id,
    parentId,
    postId,
    status,
    date,
    dateGmt,
    authorId,
    authorNicename,
    authorProfileUrl,
    authorDisplayName,
    authorAvatarUrl,
    authorMembershipType,
    content,
    isLiked,
    totalLikes,
    totalReplies,
    replies,
    submissionStatus,
  );

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CommentData) return false;

    return other.id == id &&
        other.parentId == parentId &&
        other.postId == postId &&
        other.status == status &&
        other.date == date &&
        other.dateGmt == dateGmt &&
        other.authorId == authorId &&
        other.authorNicename == authorNicename &&
        other.authorProfileUrl == authorProfileUrl &&
        other.authorDisplayName == authorDisplayName &&
        other.authorAvatarUrl == authorAvatarUrl &&
        other.authorMembershipType == authorMembershipType &&
        other.content == content &&
        other.isLiked == isLiked &&
        other.totalLikes == totalLikes &&
        other.totalReplies == totalReplies &&
        listEquals(other.replies, replies) &&
        other.submissionStatus == submissionStatus;
  }

  @override
  String toString() {
    return 'CommentData(\n'
        '\tid: $id, \n'
        '\tparentId: $parentId, \n'
        '\tpostId: $postId, \n'
        '\tstatus: $status, \n'
        '\tdate: $date, \n'
        '\tdateGmt: $dateGmt, \n'
        '\tauthorId: $authorId, \n'
        '\tauthorNicename: $authorNicename, \n'
        '\tauthorProfileUrl: $authorProfileUrl, \n'
        '\tauthorDisplayName: $authorDisplayName, \n'
        '\tauthorAvatarUrl: $authorAvatarUrl, \n'
        '\tauthorMembershipType: $authorMembershipType, \n'
        '\tcontent: $content, \n'
        '\tisLiked: $isLiked, \n'
        '\ttotalLikes: $totalLikes, \n'
        '\ttotalReplies: $totalReplies, \n'
        '\treplies: $replies, \n'
        '\tsubmissionStatus: $submissionStatus, \n'
        ')';
  }
}

import 'package:portraitmode/auth/dto/auth_data.dart';

class AuthResponse {
  final bool success;
  final String message;
  final AuthData? data;

  AuthResponse({this.success = true, this.message = '', this.data});

  factory AuthResponse.fromMap(Map<String, dynamic> map) {
    bool success = map['success'] ?? false;

    return AuthResponse(
      success: success,
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map && success
          ? AuthData.fromMap(map['data'])
          : null,
    );
  }
}

class RequestPasswordResetResponse {
  final bool success;
  final String message;

  RequestPasswordResetResponse({this.success = true, this.message = ''});

  factory RequestPasswordResetResponse.fromMap(Map<String, dynamic> map) {
    return RequestPasswordResetResponse(
      success: map['success'] ?? false,
      message: map['message'] ?? '',
    );
  }
}

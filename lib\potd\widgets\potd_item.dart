import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/enum.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';

class PotdItem extends StatelessWidget {
  final ImageSizing sizing;
  final double? width;
  final double? height;
  final double? containerWidth;
  final bool useBorderRadius;
  final PhotoData photo;
  final String? text;
  final bool isOwnProfile;
  final bool showLabel;
  final String screenName;
  final Function? onZoomStart;
  final Function? onZoomEnd;

  const PotdItem({
    super.key,
    this.sizing = ImageSizing.fixedSize,
    this.width,
    this.height,
    this.containerWidth,
    this.useBorderRadius = true,
    required this.photo,
    this.text,
    this.isOwnProfile = false,
    this.showLabel = true,
    required this.screenName,
    this.onZoomStart,
    this.onZoomEnd,
  });

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        return GestureDetector(
          onTap: () => _handleOnPhotoTap(context),
          child: useBorderRadius
              ? _buildRoundedFrame(context, constraints)
              : _buildFrameContent(context, constraints),
        );
      },
    );
  }

  Widget _buildRoundedFrame(BuildContext context, BoxConstraints constraints) {
    return ClipRRect(
      borderRadius: BorderRadius.circular(PhotoStyleConfig.borderRadius),
      child: _buildFrameContent(context, constraints),
    );
  }

  Widget _buildFrameContent(BuildContext context, BoxConstraints constraints) {
    final double constraintMaxWidth = constraints.maxWidth;

    final double wrapperWidth = sizing == ImageSizing.fixedSize
        ? (width ?? constraintMaxWidth)
        : (containerWidth ?? constraintMaxWidth);

    const double fallbackHeight = 350.0;

    late final double wrapperHeight;

    if (sizing == ImageSizing.fixedSize) {
      wrapperHeight = height ?? fallbackHeight;
    } else {
      wrapperHeight = photo.width != 0 && photo.height != 0
          ? ImageSize.computedHeight(
              parentWidth: wrapperWidth,
              imageWidth: photo.width.toDouble(),
              imageHeight: photo.height.toDouble(),
            )
          : fallbackHeight;
    }

    return Column(
      children: [
        Container(
          width: wrapperWidth,
          height: wrapperHeight,
          decoration: BoxDecoration(
            color: context.colors.baseColorAlt,
            borderRadius: useBorderRadius
                ? BorderRadius.circular(PhotoStyleConfig.borderRadius)
                : null,
            image: photo.url.isNotEmpty
                ? DecorationImage(
                    image: PmNetworkImageProvider(photo.url).imageProvider,
                    fit: BoxFit.cover,
                  )
                : null,
          ),
        ),
        if (showLabel)
          Padding(
            padding: const EdgeInsets.only(top: 7.0),
            child: Text(
              text ?? 'Photo of the day by @${photo.authorNicename}',
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
            ),
          ),
      ],
    );
  }

  void _handleOnPhotoTap(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailScreen(photo: photo, originScreenName: screenName),
      ),
    );
  }
}

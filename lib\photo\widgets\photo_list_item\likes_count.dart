import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class LikesCount extends ConsumerStatefulWidget {
  final int photoId;
  final int totalLikes;
  final EdgeInsetsGeometry padding;

  const LikesCount({
    super.key,
    required this.photoId,
    required this.totalLikes,
    this.padding = const EdgeInsets.only(bottom: 5.0),
  });

  @override
  LikesCountState createState() => LikesCountState();
}

class LikesCountState extends ConsumerState<LikesCount> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Color? textColor = Theme.of(context).textTheme.bodySmall?.color;

    return widget.totalLikes > 0
        ? Padding(
            padding: widget.padding,
            child: Row(
              children: [
                Text(
                  widget.totalLikes.toString(),
                  style: TextStyle(
                    fontSize: 12.0,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
                const SizedBox(width: 3.0),
                Text(
                  widget.totalLikes > 1 ? 'Likes' : 'Like',
                  style: TextStyle(
                    fontSize: 12.0,
                    fontWeight: FontWeight.w600,
                    color: textColor,
                  ),
                ),
              ],
            ),
          )
        : const SizedBox.shrink();
  }
}

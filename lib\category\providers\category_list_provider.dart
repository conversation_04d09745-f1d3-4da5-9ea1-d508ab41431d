import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/category/dto/category_data.dart';

final class CategoryListNotifier extends Notifier<List<CategoryData>> {
  @override
  List<CategoryData> build() => [];

  CategoryData? getItem(int id) {
    return state.firstWhereOrNull((item) {
      return item.id == id;
    });
  }

  int getIndex(int id) {
    return state.indexWhere((item) {
      return item.id == id;
    });
  }

  bool hasItem(int id) {
    return state.any((item) {
      return item.id == id;
    });
  }

  void addItem(CategoryData newItem) {
    state = [...state, newItem];
  }

  void addItems(List<CategoryData> newItems) {
    state = [...state, ...newItems];
  }

  void removeItem(String slug) {
    state = state.where((item) => item.slug != slug).toList();
  }

  void replaceAll(List<CategoryData> newItems) {
    state = newItems;
  }

  void clear() {
    state = [];
  }
}

/// The `categoryListProvider` is currently not being used.
final categoryListProvider =
    NotifierProvider.autoDispose<CategoryListNotifier, List<CategoryData>>(
      CategoryListNotifier.new,
    );

List<CategoryData> globalCategoryList = [];

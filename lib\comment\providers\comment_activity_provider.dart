import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/dto/comment_activity_provider_data.dart';
import 'package:portraitmode/comment/enum.dart';

final class CommentActivityNotifier
    extends Notifier<CommentActivityProviderData> {
  @override
  CommentActivityProviderData build() => const CommentActivityProviderData();

  void setFormMode(CommentFormMode mode) {
    state = state.copyWithFormMode(mode);
  }

  void setCommentIdToEdit(int? commentId) {
    state = state.copyWithCommentIdToEdit(commentId);
  }

  void setCommentIdToReply(int? commentId) {
    state = state.copyWithCommentIdToReply(commentId);
  }

  void setExpandedCommentId(int? commentId) {
    state = state.copyWithExpandedComment(commentId);
  }

  void update({
    CommentFormMode? formMode,
    int? commentIdToEdit,
    int? commentIdToReply,
    int? expandedCommentId,
  }) {
    state = CommentActivityProviderData(
      formMode: formMode ?? state.formMode,
      commentIdToEdit: commentIdToEdit ?? state.commentIdToEdit,
      commentIdToReply: commentIdToReply ?? state.commentIdToReply,
      expandedCommentId: expandedCommentId ?? state.expandedCommentId,
    );
  }

  void reset() {
    state = const CommentActivityProviderData();
  }
}

final commentActivityProvider =
    NotifierProvider.autoDispose<
      CommentActivityNotifier,
      CommentActivityProviderData
    >(CommentActivityNotifier.new);

import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:portraitmode/app/config/config.dart';

class PmCacheManager extends CacheManager with ImageCacheManager {
  static const key = ImageCacheConfig.cacheKey;

  static final PmCacheManager _instance = PmCacheManager._();

  factory PmCacheManager() {
    return _instance;
  }

  PmCacheManager._()
    : super(Config(key, stalePeriod: ImageCacheConfig.duration));
}

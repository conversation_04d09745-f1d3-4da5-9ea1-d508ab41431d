// delete_account_screen.dart
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/auth/widgets/login_screen.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class DeleteAccountScreen extends ConsumerStatefulWidget {
  const DeleteAccountScreen({super.key});

  @override
  DeleteAccountScreenState createState() => DeleteAccountScreenState();
}

class DeleteAccountScreenState extends ConsumerState<DeleteAccountScreen> {
  final _scrollController = ScrollController();
  final _emailController = TextEditingController();
  final _authService = AuthService();
  bool _isDisabled = true;

  @override
  void dispose() {
    _emailController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final Color dangerColor = context.colors.dangerColor;
    final Color dangerColorDisabled = context.colors.dangerColor.withValues(
      alpha: 0.5,
    );

    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          PmSliverAppBar(
            scrollController: _scrollController,
            titleText: 'Delete account',
            automaticallyImplyLeading: true,
            useLogo: false,
            actions: const [],
          ),
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: ScreenStyleConfig.horizontalPadding + 8.0,
              ),
              child: Column(
                children: [
                  const SizedBox(height: LayoutConfig.contentTopGap),
                  const Text(
                    "Are you sure you want to delete your PortraitMode account? This action is permanent and cannot be undone.",
                  ),
                  const SizedBox(height: 10.0),
                  const Text(
                    "Any photos, comments, or other content associated with your account will be removed.",
                  ),
                  const SizedBox(height: 10.0),
                  const Text(
                    "To proceed, please enter your email address and confirm by clicking the button below.",
                  ),
                  const SizedBox(height: 20.0),
                  SizedBox(
                    height: 50.0,
                    child: TextField(
                      controller: _emailController,
                      keyboardType: TextInputType.emailAddress,
                      decoration: const InputDecoration(
                        labelText: 'Email',
                        border: OutlineInputBorder(),
                      ),
                      onChanged: (value) {
                        setState(() {
                          _isDisabled = value.isEmpty;
                        });
                      },
                    ),
                  ),
                  const SizedBox(height: 20.0),
                  SubmitButton(
                    width: double.infinity,
                    height: 50.0,
                    bgColor: _isDisabled ? dangerColorDisabled : dangerColor,
                    buttonText: 'Delete account',
                    onPressed: _handleOnSubmit,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleOnSubmit() async {
    if (_isDisabled) return;
    FocusScope.of(context).unfocus();

    String userEmail = _emailController.text.trim();
    final email = ref.read(profileProvider).email;

    if (userEmail.isNotEmpty) {
      if (email == userEmail) {
        _deleteAccount(email);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Email address is incorrect.")),
        );
      }
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Please enter your email address.")),
      );
    }
  }

  void _deleteAccount(String email) async {
    final response = await _authService.deleteAccount(email);

    if (!response.success) {
      if (mounted) {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (mounted) {
      Navigator.of(context).pushAndRemoveUntil(
        MaterialPageRoute(builder: (context) => const LoginScreen()),
        (route) => false,
      );

      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(response.message)));
    }
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/widgets/photo_list_item/photo_frame.dart';

class PhotoMasonryItem extends StatelessWidget {
  const PhotoMasonryItem({
    super.key,
    required this.index,
    this.containerWidth,
    required this.photo,
    this.isOwnProfile = false,
    required this.screenName,
    this.isSearchScreen = false,
    this.onPhotoTap,
    this.onZoomStart,
    this.onZoomEnd,
  });

  final int index;
  final double? containerWidth;
  final PhotoData photo;
  final bool isOwnProfile;
  final String screenName;
  final bool isSearchScreen;
  final Function? onPhotoTap;
  final Function? onZoomStart;
  final Function? onZoomEnd;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        PhotoFrame(
          containerWidth: containerWidth,
          isRounded: true,
          photo: photo,
          isOwnPhoto: isOwnProfile,
          screenName: screenName,
          isSmall: true,
          showMetadata: false,
          zoomable: false,
          onZoomStart: onZoomStart,
          onZoomEnd: onZoomEnd,
          onTap: onPhotoTap,
        ),
      ],
    );
  }
}

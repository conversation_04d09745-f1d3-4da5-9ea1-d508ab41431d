import 'package:flutter_riverpod/flutter_riverpod.dart';

final class FeedbackTokensAmountNotifier extends Notifier<int> {
  @override
  int build() => 0;

  int get value => state;

  set value(int newValue) {
    state = newValue;
  }
}

final feedbackTokensAmountProvider =
    NotifierProvider.autoDispose<FeedbackTokensAmountNotifier, int>(
      FeedbackTokensAmountNotifier.new,
    );

int globalFeedbackTokensAmount = 0;

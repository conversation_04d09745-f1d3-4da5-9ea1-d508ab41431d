// pm_network_image.dart

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/utils/cache_util.dart';

class PmNetworkImage extends StatelessWidget {
  final String url;
  final double? width;
  final double? height;
  final Alignment alignment;
  final BoxFit fit;
  final Widget? loadingWidget;
  final LoadingErrorWidgetBuilder? errorBuilder;

  const PmNetworkImage({
    super.key,
    required this.url,
    this.width,
    this.height,
    this.alignment = Alignment.center,
    this.fit = BoxFit.cover,
    this.loadingWidget,
    this.errorBuilder,
  });

  @override
  Widget build(BuildContext context) {
    return CachedNetworkImage(
      imageUrl: url,
      alignment: alignment,
      fit: fit,
      width: width,
      height: height,
      cacheManager: PmCacheManager(),
      fadeInDuration: Duration.zero,
      fadeOutDuration: Duration.zero,
      placeholder: (context, url) {
        return loadingWidget ??
            Container(
              width: width,
              height: height,
              color: context.colors.lightColor,
            );
      },
      errorWidget:
          errorBuilder ??
          (context, url, error) {
            return Container(
              width: width,
              height: height,
              color: context.colors.lightColor,
            );
          },
    );
  }
}

class PmNetworkImageProvider {
  final String url;

  const PmNetworkImageProvider(this.url);

  ImageProvider<Object> get imageProvider => CachedNetworkImageProvider(url);

  CachedNetworkImageProvider get cachedNetworkImage =>
      CachedNetworkImageProvider(url);

  NetworkImage get networkImage => NetworkImage(url);
}

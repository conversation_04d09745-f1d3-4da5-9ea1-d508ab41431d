import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/youtube/http_responses/save_youtube_channels_response.dart';

class SaveYoutubeChannelsService extends BaseService {
  Future<SaveYoutubeChannelsResponse> save(String accessToken) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/youtube/channels',
        data: {'access_token': accessToken},
      );

      return SaveYoutubeChannelsResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return SaveYoutubeChannelsResponse.fromMap(e.response?.data);
      }

      return SaveYoutubeChannelsResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return SaveYoutubeChannelsResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

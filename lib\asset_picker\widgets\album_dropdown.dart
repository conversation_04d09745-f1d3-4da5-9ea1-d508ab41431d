import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/dto/plain_album_data.dart';
import 'package:portraitmode/asset_picker/utils/album_util.dart';
import 'package:portraitmode/asset_picker/widgets/album_list_view.dart';

class AlbumDropdown extends StatefulWidget {
  final double galleryHeaderHeight;
  final int gridImageQuality;
  final int gridItemSize;
  final double panelMaxHeight;
  final Animation<double> animation;
  final List<AssetPathEntity> albums;
  final Function(AssetPathEntity) onSelect;

  const AlbumDropdown({
    super.key,
    required this.galleryHeaderHeight,
    required this.gridImageQuality,
    required this.gridItemSize,
    required this.panelMaxHeight,
    required this.animation,
    required this.albums,
    required this.onSelect,
  });

  @override
  AlbumDropdownState createState() => AlbumDropdownState();
}

class AlbumDropdownState extends State<AlbumDropdown> {
  List<PlainAlbumData> _plainAlbumList = [];

  @override
  void initState() {
    _readAlbums();

    super.initState();
  }

  @override
  void dispose() {
    _plainAlbumList = [];
    super.dispose();
  }

  Future<void> _readAlbums() async {
    final plainAlbums = <PlainAlbumData>[];

    for (final album in widget.albums) {
      final plainAlbum = await readAlbumData(album);
      plainAlbums.add(plainAlbum);
    }

    if (mounted) {
      setState(() {
        _plainAlbumList.addAll(plainAlbums);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.animation,
      builder: (BuildContext context, Widget? child) {
        final offsetY =
            widget.galleryHeaderHeight +
            (widget.panelMaxHeight - widget.galleryHeaderHeight) *
                (1 - widget.animation.value);

        return Visibility(
          visible: widget.animation.value > 0.0,
          child: Transform.translate(
            offset: Offset(0.0, offsetY),
            child: child,
          ),
        );
      },
      child: Container(
        width: double.infinity,
        height: widget.panelMaxHeight,
        padding: const EdgeInsets.only(bottom: 50.0),
        color: Colors.black,
        child: AlbumListView(
          albums: widget.albums,
          plainAlbums: _plainAlbumList,
          onAlbumSelected: widget.onSelect,
          thumbnailQuality: widget.gridImageQuality,
          thumbnailSize: widget.gridItemSize,
        ),
      ),
    );
  }
}

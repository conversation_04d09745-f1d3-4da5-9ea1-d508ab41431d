// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class CategoryListService extends BaseService {
  Future<CategoryListResponse> fetch({
    int? limit,
    int? offset,
    bool hideEmpty = false,
    bool trending = false,
  }) async {
    int hideEmptyParam = hideEmpty ? 1 : 0;
    late String url;

    if (trending) {
      url = '${URL.baseApiUrl}/trending-categories';
    } else {
      url = '${URL.baseApiUrl}/categories/${hideEmptyParam.toString()}';
    }

    if (limit != null && offset != null) {
      url += '/${limit.toString()}/${offset.toString()}';
    } else {
      url += '/0/0';
    }

    try {
      final response = await http.get(url);

      return CategoryListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CategoryListResponse.fromMap(e.response?.data);
      }

      return CategoryListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CategoryListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<CategoryListResponse> fetchUnfollowed({
    int? limit,
    int? offset,
    bool hideEmpty = true,
  }) async {
    final int hideEmptyParam = hideEmpty ? 1 : 0;
    String url =
        '${URL.baseApiUrl}/unfollowed-categories/${hideEmptyParam.toString()}';

    if (limit != null && offset != null) {
      url += '/${limit.toString()}/${offset.toString()}';
    } else {
      if (limit != null) {
        url += '/${limit.toString()}/-1';
      } else {
        url += '/-1/-1';
      }
    }

    try {
      final response = await http.get(url);

      return CategoryListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CategoryListResponse.fromMap(e.response?.data);
      }

      return CategoryListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CategoryListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<CategoryListResponse> fetchSpecific({
    List<int> categoryIds = const [],
  }) async {
    const String url = '${URL.baseApiUrl}/specific-categories';

    try {
      final response = await http.post(
        url,
        data: {'ids': categoryIds.join(',')},
      );

      return CategoryListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CategoryListResponse.fromMap(e.response?.data);
      }

      return CategoryListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CategoryListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<CategoryListResponse> search({
    String keyword = '',
    int? limit,
    int? offset,
    bool hideEmpty = false,
  }) async {
    String url = '${URL.baseApiUrl}/search/categories/$keyword';
    int hideEmptyParam = hideEmpty ? 1 : 0;

    if (limit != null && offset != null) {
      url =
          '${URL.baseApiUrl}/search/categories/${hideEmptyParam.toString()}/$keyword/${limit.toString()}/${offset.toString()}';
    }

    try {
      final response = await http.get(url);

      return CategoryListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CategoryListResponse.fromMap(e.response?.data);
      }

      return CategoryListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CategoryListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

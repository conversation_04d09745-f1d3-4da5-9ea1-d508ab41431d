import 'package:dio/dio.dart';
import 'package:portraitmode/base/pm_http.dart';
import 'package:portraitmode/app/config/url.dart';

class BaseService {
  String apiUrl = URL.baseApiUrl;

  final Dio http = PmHttp.instance();

  bool errorHasData(DioException e) {
    if (e.response != null) {
      if (e.response?.data != null && e.response?.data is Map) {
        if (e.response?.data?.containsKey('message')) {
          return true;
        }
      }
    }

    return false;
  }

  String getDioExceptionMsg({required DioException e}) {
    String msg = e.message ?? 'There was problem on the server.';

    if (e.response != null) {
      if (e.response?.data != null && e.response?.data is Map) {
        if (e.response?.data?.containsKey('message')) {
          msg = e.response?.data['message'];
        }
      }
    }

    return msg;
  }
}

// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/blog/responses/blog_post_fetch_response.dart';

// Internal packages.
import 'package:portraitmode/blog/responses/blog_post_find_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class BlogPostService extends BaseService {
  Future<BlogPostFindResponse> find(int id) async {
    try {
      final response = await http.get('${URL.baseApiUrl}/blog-post/$id');

      return BlogPostFindResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BlogPostFindResponse.fromMap(e.response?.data);
      }

      return BlogPostFindResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return BlogPostFindResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<BlogPostFindResponse> findBySlug(String slug) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/blog-post/by-slug/$slug',
      );

      return BlogPostFindResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BlogPostFindResponse.fromMap(e.response?.data);
      }

      return BlogPostFindResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return BlogPostFindResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<BlogPostFetchResponse> fetch({
    int perPage = 10,
    int page = 1,
    String? search,
    int? categoryId,
    List<int>? categoryIds,
    int? authorId,
    String? status,
    String? order,
    String? orderBy,
  }) async {
    try {
      String url = '${URL.baseApiUrl}/blog-posts/$perPage/$page';

      final response = await http.get(url);

      return BlogPostFetchResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BlogPostFetchResponse.fromMap(e.response?.data);
      }

      return BlogPostFetchResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return BlogPostFetchResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

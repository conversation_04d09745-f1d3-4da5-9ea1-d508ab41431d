import 'package:flutter/material.dart';
import 'package:portraitmode/misc/vertical_dots.dart';

class ProfileMenuIndicator extends StatelessWidget {
  const ProfileMenuIndicator({super.key, this.onTap});

  final Function? onTap;

  @override
  Widget build(BuildContext context) {
    return IconButton(
      onPressed: () => handleOnPressed(context),
      icon: VerticalDots(
        padding: const EdgeInsets.symmetric(vertical: 13.0),
        onTap: () => handleOnPressed(context),
      ),
    );
  }

  void handleOnPressed(BuildContext context) {
    onTap?.call();
  }
}

enum FollowActionType { follow, unfollow }

extension AssignmentActionEnumExtension on FollowActionType {
  String get text {
    switch (this) {
      case FollowActionType.follow:
        return 'follow';
      case FollowActionType.unfollow:
        return 'unfollow';
    }
  }
}

enum BlockActionType { block, unblock }

extension BlockActionEnumExtension on BlockActionType {
  String get text {
    switch (this) {
      case BlockActionType.block:
        return 'block';
      case BlockActionType.unblock:
        return 'unblock';
    }
  }
}

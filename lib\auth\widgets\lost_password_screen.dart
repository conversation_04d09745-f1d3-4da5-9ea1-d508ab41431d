// Core packages.
import 'dart:io';

import 'package:flutter/material.dart';
// Internal packages.
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/auth/http_responses/auth_response.dart';
import 'package:portraitmode/auth/services/auth_service.dart';
import 'package:portraitmode/auth/widgets/login_screen.dart';
import 'package:portraitmode/auth/widgets/privacy_policy_link.dart';
import 'package:portraitmode/auth/widgets/register_screen.dart';
import 'package:portraitmode/auth/widgets/reset_password_screen.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/max_width/max_width.dart';

// Internal packages.

class LostPasswordScreen extends StatefulWidget {
  const LostPasswordScreen({super.key});

  @override
  State createState() => _LostPasswordScreen();
}

class _LostPasswordScreen extends State<LostPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final authService = AuthService();
  String? error;
  late RequestPasswordResetResponse requestResponse;

  final emailFieldController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final double screenHeight = MediaQuery.sizeOf(context).height;

    return Scaffold(
      backgroundColor: context.colors.lightColor,
      body: SingleChildScrollView(
        child: SafeArea(
          child: Center(
            child: MaxWidthBuilder(
              maxWidth: 520.0,
              height: screenHeight - MediaQuery.paddingOf(context).top,
              builder: (BuildContext context, BoxConstraints constraints) {
                double containerWidth = constraints.maxWidth;

                return Stack(
                  alignment: Alignment.topCenter,
                  children: [
                    Form(
                      key: _formKey,
                      child: SizedBox(
                        width: (containerWidth / 100) * 85,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(bottom: 25.0),
                              child: Image.asset(
                                context.isDarkMode
                                    ? "assets/logo-white.png"
                                    : "assets/logo.png",
                                width: containerWidth - (containerWidth / 3.5),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(bottom: 16.0),
                              child: Text(
                                "Please enter your email address. You will receive an email containing reset code that you will need to enter in the next step.",
                                style: TextStyle(
                                  color: context.colors.primarySwatch[400],
                                ),
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 8.0,
                                bottom: 25.0,
                              ),
                              child: PmTextField(
                                controller: emailFieldController,
                                keyboardType: TextInputType.emailAddress,
                                labelText: "Email",
                                validator: FieldValidators.emailValidator.call,
                                onChanged: (String value) {
                                  if (!mounted) return;

                                  setState(() {
                                    error = null;
                                  });
                                },
                              ),
                            ),
                            SizedBox(
                              height: 50.0,
                              width: double.infinity,
                              child: SubmitButton(
                                buttonText: "Request password reset",
                                onPressed: onFormSubmit,
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                top: 16.0,
                                bottom: 25.0,
                              ),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  GestureDetector(
                                    onTap: onLoginLinkTap,
                                    child: Text(
                                      "Login",
                                      style: TextStyle(
                                        color: context.colors.accentColor,
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 3.0),
                                  Text(
                                    "|",
                                    style: TextStyle(
                                      color: context.colors.primarySwatch[400],
                                      fontSize: 13.0,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                  const SizedBox(width: 3.0),
                                  GestureDetector(
                                    onTap: onRegisterLinkTap,
                                    child: Text(
                                      "Register",
                                      style: TextStyle(
                                        color: context.colors.accentColor,
                                        fontSize: 13.0,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 16.0),
                            error != null
                                ? Text(
                                    "$error",
                                    style: const TextStyle(color: Colors.red),
                                  )
                                : const SizedBox.shrink(),
                          ],
                        ),
                      ),
                    ),
                    Positioned(
                      bottom: Platform.isIOS ? 40.0 : 20.0,
                      child: PrivacyPolicyLink(),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    emailFieldController.dispose();
    super.dispose();
  }

  void onLoginLinkTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const LoginScreen()),
    );
  }

  void onRegisterLinkTap() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RegisterScreen()),
    );
  }

  Future<void> onFormSubmit() async {
    FocusScope.of(context).unfocus();

    if (!_formKey.currentState!.validate()) {
      return;
    }

    requestResponse = await authService.requestPasswordReset(
      emailFieldController.text,
    );

    if (requestResponse.success) {
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(
            builder: (context) =>
                ResetPasswordScreen(email: emailFieldController.text),
          ),
        );
      }

      return;
    }

    if (mounted) {
      setState(() {
        error = requestResponse.message;
      });
    }
  }
}

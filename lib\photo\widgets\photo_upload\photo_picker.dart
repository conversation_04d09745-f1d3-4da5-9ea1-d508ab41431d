import 'dart:io';

import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/asset_picker/asset_picker.dart';
import 'package:portraitmode/photo/widgets/photo_upload/photo_full_view.dart';

class PhotoPicker extends StatefulWidget {
  const PhotoPicker({
    super.key,
    required this.onInit,
    required this.onChange,
    this.defaultPhoto,
  });

  final Function(Function) onInit;
  final Function(File?) onChange;
  final File? defaultPhoto;

  @override
  PhotoPickerState createState() => PhotoPickerState();
}

class PhotoPickerState extends State<PhotoPicker> {
  File? _selectedPhoto;

  @override
  void initState() {
    widget.onInit(_removePhoto);
    super.initState();
  }

  @override
  void dispose() {
    _selectedPhoto = null;
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return (_selectedPhoto != null || widget.defaultPhoto != null
        ? _buildPreviewWidget()
        : AssetPicker(
            onTap: () {
              // FocusManager.instance.primaryFocus?.unfocus();
            },
            onPick: _handleOnPick,
            child: _buildLargePickerField(),
          ));
  }

  Widget _buildLargePickerField() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 32.0),
      decoration: BoxDecoration(
        color: context.colors.baseColorAlt,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: const Center(
        child: Column(
          children: [
            Icon(Ionicons.camera_outline, size: 24.0),
            Text('Select a photo'),
          ],
        ),
      ),
    );
  }

  Widget _buildTinyPickerField() {
    return SizedBox(
      width: 32.0,
      height: 32.0,
      child: Container(
        decoration: BoxDecoration(
          color: context.colors.brandColor.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: const Icon(
          Ionicons.camera_outline,
          size: 18.0,
          color: Colors.white,
        ),
      ),
    );
  }

  Widget _buildPreviewWidget() {
    File? previewPhoto = _selectedPhoto;
    previewPhoto = previewPhoto ?? widget.defaultPhoto;

    return Row(
      children: [
        Flexible(
          flex: 2,
          child: Stack(
            alignment: Alignment.center,
            clipBehavior: Clip.none,
            children: [
              (previewPhoto != null
                  ? Container(
                      padding: const EdgeInsets.only(top: 8.0, right: 8.0),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10.0),
                        child: Image.file(previewPhoto),
                      ),
                    )
                  : const SizedBox.shrink()),
              Positioned(
                top: 0.0,
                right: 0.0,
                // Create delete photo button.
                child: GestureDetector(
                  onTap: _handleRemovePhoto,
                  child: Container(
                    width: 26.0,
                    height: 26.0,
                    decoration: BoxDecoration(
                      color: context.colors.dangerColor,
                      borderRadius: BorderRadius.circular(15.0),
                    ),
                    child: Icon(
                      Ionicons.close,
                      size: 14.0,
                      color: context.colors.scaffoldColor,
                    ),
                  ),
                ),
              ),
              Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    AssetPicker(
                      onPick: _handleOnPick,
                      child: _buildTinyPickerField(),
                    ),
                    const SizedBox(width: 10.0),
                    SizedBox(
                      width: 32.0,
                      height: 32.0,
                      child: GestureDetector(
                        onTap: () {
                          if (previewPhoto == null) return;

                          Navigator.of(context).push(
                            MaterialPageRoute(
                              builder: (context) =>
                                  PhotoFullView(photoFile: previewPhoto!),
                            ),
                          );
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: context.colors.brandColor.withValues(
                              alpha: 0.5,
                            ),
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Ionicons.search_outline,
                            size: 18.0,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const Flexible(flex: 3, child: SizedBox.shrink()),
      ],
    );
  }

  Future<void> _handleOnPick(AssetEntity? assetEntity) async {
    if (assetEntity == null) return;

    try {
      final file = await assetEntity.file;
      if (file == null) return;

      if (mounted) {
        setState(() {
          _selectedPhoto = file;
        });
      }

      widget.onChange(file);
    } catch (e) {
      return;
    }
  }

  void _handleRemovePhoto() {
    widget.onChange(null);
    _removePhoto();
  }

  void _removePhoto() {
    if (!mounted) return;

    setState(() {
      _selectedPhoto = null;
    });
  }
}

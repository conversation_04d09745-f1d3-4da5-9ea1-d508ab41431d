import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class ReviewFeaturedSuggestionModal extends StatelessWidget {
  final PhotoData photo;
  final bool isOwnPhoto;
  final Function? onModerationDismiss;
  final Function(int)? onPhotoFeatured;

  const ReviewFeaturedSuggestionModal({
    super.key,
    required this.photo,
    this.isOwnPhoto = false,
    this.onModerationDismiss,
    this.onPhotoFeatured,
  });

  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    menuItems.addAll([
      ModalListTile(
        title: "Approve and notify the author",
        textColor: context.colors.successColor,
        iconColor: context.colors.successColor,
        iconData: Ionicons.trophy_outline,
        onTap: () {
          Navigator.pop(context);

          if (onPhotoFeatured != null) {
            onPhotoFeatured!(1);
          }
        },
      ),
      ModalListTile(
        title: "Approve without notification",
        iconData: Ionicons.trophy_outline,
        onTap: () {
          Navigator.pop(context);

          if (onPhotoFeatured != null) {
            onPhotoFeatured!(0);
          }
        },
      ),
      ModalListTile(
        title: "Dismiss this report",
        iconData: Ionicons.alert_circle_outline,
        onTap: () {
          Navigator.pop(context);

          if (onModerationDismiss != null) {
            onModerationDismiss!();
          }
        },
      ),
    ]);

    return Container(
      padding: EdgeInsets.only(
        top: 9.0,
        bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height:
              BottomSheetConfig.extraSpace +
              (BottomSheetConfig.menuItemHeight * menuItems.length),
          child: Column(children: menuItems),
        ),
      ),
    );
  }
}

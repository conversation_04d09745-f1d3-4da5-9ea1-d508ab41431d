import 'package:flutter/foundation.dart';

@immutable
class CategoryPhotoListInteractionData {
  final int loadMoreLastId;
  final int lastItemSeenId;

  const CategoryPhotoListInteractionData({
    this.loadMoreLastId = 0,
    this.lastItemSeenId = 0,
  });

  CategoryPhotoListInteractionData copyWith({
    int? loadMoreLastId,
    int? lastItemSeenId,
  }) {
    return CategoryPhotoListInteractionData(
      loadMoreLastId: loadMoreLastId ?? this.loadMoreLastId,
      lastItemSeenId: lastItemSeenId ?? this.lastItemSeenId,
    );
  }

  Map<String, dynamic> toMap() {
    return {'loadMoreLastId': loadMoreLastId, 'lastItemSeenId': lastItemSeenId};
  }

  factory CategoryPhotoListInteractionData.fromMap(Map<String, dynamic> data) {
    return CategoryPhotoListInteractionData(
      loadMoreLastId: data['loadMoreLastId'],
      lastItemSeenId: data['lastItemSeenId'],
    );
  }
}

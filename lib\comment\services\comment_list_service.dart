// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class CommentListService extends BaseService {
  Future<CommentListResponse> fetch({
    bool isRefreshAction = false,
    int? postId,
    int? status,
    int? parentId,
    int? limit,
    int? offset,
    String? keyword,
  }) async {
    postId = postId ?? 0;
    status = status ?? 1;
    parentId = parentId ?? 0;
    limit = limit ?? 0;
    offset = offset ?? 0;

    String url =
        '${URL.baseApiUrl}/comments/$postId/$status/$parentId/$limit/$offset';

    if (keyword != null && keyword.isNotEmpty) {
      url =
          '${URL.baseApiUrl}/search-comments/$keyword/$postId/$status/$parentId/$limit/$offset';
    }

    try {
      final response = await http.get(url);

      return CommentListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentListResponse.fromMap(e.response?.data);
      }

      return CommentListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CommentListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<CommentListResponse> search({
    String keyword = '',
    int? limit,
    int? offset,
  }) async {
    String url = '${URL.baseApiUrl}/search/categories/$keyword';

    if (limit != null && offset != null) {
      url =
          '${URL.baseApiUrl}/search/categories/$keyword/${limit.toString()}/${offset.toString()}';
    }

    try {
      final response = await http.get(url);

      return CommentListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentListResponse.fromMap(e.response?.data);
      }

      return CommentListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CommentListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

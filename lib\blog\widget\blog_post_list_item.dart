import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/blog/dto/blog_post_data.dart';
import 'package:portraitmode/blog/widget/blog_post_detail_screen.dart';

class BlogPostListItem extends StatelessWidget {
  final BlogPostData post;
  final double? borderRadius;

  const BlogPostListItem({super.key, required this.post, this.borderRadius});

  @override
  Widget build(BuildContext context) {
    final bool hasFeaturedImage =
        post.featuredImage != null && post.featuredImage!.mediumUrl.isNotEmpty;

    return LayoutBuilder(
      builder: (BuildContext layoutBuildContext, BoxConstraints constraints) {
        final double containerWidth = constraints.maxWidth;

        double imageWidth = 0;
        double imageHeight = 0;
        double containerHeight = 250;

        if (hasFeaturedImage) {
          imageWidth = post.featuredImage!.width.toDouble();
          imageHeight = post.featuredImage!.height.toDouble();

          containerHeight = ImageSize.computedHeight(
            parentWidth: containerWidth,
            imageWidth: imageWidth,
            imageHeight: imageHeight,
          );
        }

        return SizedBox(
          width: imageWidth,
          height: containerHeight,
          child: GestureDetector(
            onTap: () => _handleOnTap(context),
            child: Stack(
              alignment: Alignment.center,
              children: [
                if (post.featuredImage != null)
                  if (hasFeaturedImage)
                    Container(
                      width: containerWidth,
                      height: containerHeight,
                      decoration: BoxDecoration(
                        borderRadius: borderRadius != null
                            ? BorderRadius.all(Radius.circular(borderRadius!))
                            : null,
                        image: DecorationImage(
                          image: PmNetworkImageProvider(
                            post.featuredImage!.mediumUrl,
                          ).imageProvider,
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                Container(
                  height: containerHeight,
                  decoration: BoxDecoration(
                    borderRadius: borderRadius != null
                        ? BorderRadius.all(Radius.circular(borderRadius!))
                        : null,
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.black.withValues(alpha: 0.6),
                        Colors.black.withValues(alpha: 0.2),
                      ],
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 10.0),
                  child: Center(
                    child: Text(
                      post.title,
                      maxLines: 2,
                      textAlign: TextAlign.center,
                      style: const TextStyle(
                        fontSize: 20.0,
                        fontWeight: FontWeight.w600,
                        color: Colors.white,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  void _handleOnTap(BuildContext context) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlogPostDetailScreen(post: post, isLoading: true),
      ),
    );
  }
}

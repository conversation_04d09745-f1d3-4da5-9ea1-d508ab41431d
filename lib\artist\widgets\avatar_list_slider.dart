import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';

class AvatarListSlider extends StatefulWidget {
  const AvatarListSlider({super.key, this.artistList = const []});

  final List<ArtistData> artistList;

  @override
  AvatarListSliderState createState() => AvatarListSliderState();
}

class AvatarListSliderState extends State<AvatarListSlider> {
  final double _sliderItemGap = 13.0;
  late int _totalItems;
  final double _avatarSize = 168.0;

  @override
  void initState() {
    super.initState();
    _totalItems = widget.artistList.length;
  }

  late double _sliderItemWidth;

  @override
  Widget build(BuildContext context) {
    _sliderItemWidth = _avatarSize + 20.0;

    return Container(
      padding: const EdgeInsets.only(top: 20, bottom: 22),
      decoration: BoxDecoration(color: context.colors.baseColorAlt),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Padding(
            padding: EdgeInsets.only(bottom: 10.0, left: 13.0, right: 13.0),
            child: Text(
              "Discover artists",
              style: TextStyle(
                height: 1,
                fontSize: 13,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
          SizedBox(
            height: 265,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _totalItems,
              itemBuilder: (BuildContext context, int index) {
                return _buildItem(widget.artistList[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildItem(ArtistData artist, int index) {
    return Container(
      padding: const EdgeInsets.only(top: 10.0),
      margin: EdgeInsets.only(
        left: _sliderItemGap,
        right: index == _totalItems - 1 ? _sliderItemGap : 0,
      ),
      width: _sliderItemWidth,
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Avatar(
            imageUrl: artist.avatarUrl,
            size: _avatarSize,
            useBorder: false,
            onTap: () => _handleArtistTap(context, artist),
          ),
          const SizedBox(height: 10),
          Text(
            artist.displayName,
            style: const TextStyle(
              height: 1,
              fontSize: 13,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(height: 11),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 10.0),
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () => _handleArtistTap(context, artist),
              style: ElevatedButton.styleFrom(
                backgroundColor: context.colors.brandColor,
                elevation: 0,
                padding: const EdgeInsets.symmetric(
                  horizontal: 20,
                  vertical: 10,
                ),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
              child: Text(
                'View profile',
                style: TextStyle(
                  fontSize: 12,
                  color: context.isDarkMode
                      ? AppColorsCache.dark().baseColor
                      : Colors.white,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _handleArtistTap(BuildContext context, ArtistData artist) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: artist.id,
            nicename: artist.nicename,
            displayName: artist.displayName,
            profileUrl: artist.profileUrl,
            avatarUrl: artist.avatarUrl,
            membershipType: artist.membershipType,
          ),
        ),
      ),
    );
  }
}

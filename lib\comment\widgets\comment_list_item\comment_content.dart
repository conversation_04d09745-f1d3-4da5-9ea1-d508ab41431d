import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';

class CommentContent extends ConsumerWidget {
  const CommentContent({super.key, required this.comment, this.parentComment});

  final CommentData comment;
  final CommentData? parentComment;

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    List<TextSpan> textSpans = _buildTextSpans(comment.content, ref);

    return RichText(
      strutStyle: const StrutStyle(height: 1.3, leading: 0.0),
      text: TextSpan(
        style: DefaultTextStyle.of(context).style,
        children: textSpans,
      ),
    );
  }

  List<TextSpan> _buildTextSpans(String content, WidgetRef ref) {
    TextStyle linkStyle = TextStyle(
      fontSize: 13.0,
      color: ref.context.isDarkMode
          ? AppColorsCache.dark().brandColor
          : AppColorsCache.light().darkerGreyColor,
    );

    TextStyle textStyle = TextStyle(
      fontSize: 13.0,
      color: ref.context.colors.contentLighterColor,
    );

    List<TextSpan> textSpans = [];
    int index = 0;

    // Recursively convert all @ chars inside content to TextSpan.
    while (index < content.length) {
      int atCharIndex = content.indexOf('@', index);

      if (atCharIndex == -1) {
        textSpans.add(
          TextSpan(
            text: content.substring(index),
            style: textStyle,
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                _handleContentTap(ref);
              },
          ),
        );
        break;
      }

      if (atCharIndex > index) {
        textSpans.add(
          TextSpan(
            text: content.substring(index, atCharIndex),
            style: textStyle,
            recognizer: TapGestureRecognizer()
              ..onTap = () {
                _handleContentTap(ref);
              },
          ),
        );
      }

      int spaceIndex = content.indexOf(' ', atCharIndex);

      if (spaceIndex == -1) {
        spaceIndex = content.length;
      }

      String nicename = content.substring(atCharIndex + 1, spaceIndex);

      textSpans.add(
        TextSpan(
          text: '@$nicename',
          style: linkStyle,
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              _handleArtistTap(ref.context, nicename);
            },
        ),
      );

      index = spaceIndex;
    }

    return textSpans;
  }

  void _handleContentTap(WidgetRef ref) {
    FocusManager.instance.primaryFocus?.unfocus();
    ref.read(commentActivityProvider.notifier).setCommentIdToReply(null);
  }

  void _handleArtistTap(BuildContext context, String nicename) {
    // Remove trailing comma from nicename.
    if (nicename.endsWith(',')) {
      nicename = nicename.substring(0, nicename.length - 1);
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            nicename: nicename,
            displayName: nicename,
          ),
        ),
      ),
    );
  }
}

import 'dart:async';
import 'dart:ui';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';

class AssetThumbnailProvider extends ImageProvider<AssetThumbnailProvider> {
  final AssetEntity entity;
  final int size;
  final int quality;

  const AssetThumbnailProvider({
    required this.entity,
    required this.size,
    required this.quality,
  });

  @override
  ImageStreamCompleter loadImage(
    AssetThumbnailProvider key,
    ImageDecoderCallback decode,
  ) {
    final imageInfoCompleter = Completer<ImageInfo>();
    _loadAsync(key, decode, imageInfoCompleter);
    return OneFrameImageStreamCompleter(
      imageInfoCompleter.future,
      informationCollector: () sync* {
        yield ErrorDescription('Id: ${entity.id}');
      },
    );
  }

  Future<void> _loadAsync(
    AssetThumbnailProvider key,
    ImageDecoderCallback decoder,
    Completer<ImageInfo> completer,
  ) async {
    final Uint8List? bytes = await entity.thumbnailDataWithSize(
      ThumbnailSize.square(size),
      quality: quality,
    );

    if (bytes == null) {
      completer.completeError('Failed to load thumbnail');
      return;
    }

    final buffer = await ImmutableBuffer.fromUint8List(bytes);
    final descriptor = await ImageDescriptor.encoded(buffer);
    final codec = await descriptor.instantiateCodec();
    final frameInfo = await codec.getNextFrame();

    completer.complete(ImageInfo(image: frameInfo.image, scale: 1.0));
  }

  @override
  Future<AssetThumbnailProvider> obtainKey(ImageConfiguration configuration) {
    return SynchronousFuture<AssetThumbnailProvider>(this);
  }

  @override
  bool operator ==(Object other) {
    if (other.runtimeType != runtimeType) {
      return false;
    }

    if (other is AssetThumbnailProvider) {
      return other.entity.id == entity.id;
    }

    return false;
  }

  @override
  int get hashCode => entity.id.hashCode;

  @override
  String toString() => '$runtimeType("${entity.id}")';
}

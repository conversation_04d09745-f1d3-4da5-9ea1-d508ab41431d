import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/dto/camera_photo_list_interaction_data.dart';

final class CameraPhotoListInteractionNotifier
    extends Notifier<CameraPhotoListInteractionData> {
  @override
  CameraPhotoListInteractionData build() => CameraPhotoListInteractionData();

  CameraPhotoListInteractionData get data => state;

  void setLoadMoreLastId(int lastId) {
    state = state.copyWith(loadMoreLastId: lastId);
  }

  void setLastItemSeenId(int lastItemSeemId) {
    state = state.copyWith(lastItemSeenId: lastItemSeemId);
  }

  void replace(CameraPhotoListInteractionData data) {
    state = data;
  }

  void reset() {
    state = CameraPhotoListInteractionData();
  }
}

Map cameraPhotoListInteractionProviderMap = {};

NotifierProvider<
  CameraPhotoListInteractionNotifier,
  CameraPhotoListInteractionData
>
getCameraPhotoListInteractionProvider(String slug) {
  if (cameraPhotoListInteractionProviderMap.containsKey(slug)) {
    return cameraPhotoListInteractionProviderMap[slug];
  }

  cameraPhotoListInteractionProviderMap[slug] =
      NotifierProvider.autoDispose<
        CameraPhotoListInteractionNotifier,
        CameraPhotoListInteractionData
      >(CameraPhotoListInteractionNotifier.new);

  return cameraPhotoListInteractionProviderMap[slug];
}

void deleteCameraPhotoListInteractionProvider(String slug) {
  if (cameraPhotoListInteractionProviderMap.containsKey(slug)) {
    cameraPhotoListInteractionProviderMap.remove(slug);
  }
}

void clearCameraPhotoListInteractionProviders() {
  cameraPhotoListInteractionProviderMap.clear();
}

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/widgets/pm_network_image.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/notification/dto/notification_data.dart';
import 'package:portraitmode/notification/widgets/view_profile_button.dart';
import 'package:portraitmode/photo/widgets/photo_detail_by_id_screen.dart';
import 'package:timeago/timeago.dart' as timeago;
import 'package:url_launcher/url_launcher.dart';

class NotificationListItem extends StatelessWidget {
  const NotificationListItem({
    super.key,
    required this.index,
    required this.notification,
  });

  final int index;
  final NotificationData notification;
  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.centerLeft,
      padding: const EdgeInsets.only(
        top: 11.5,
        bottom: 11.5,
        // bottom: 15.0,
      ),
      decoration: BoxDecoration(
        color: notification.isRead == 0 ? context.colors.baseColorAlt : null,
        // border: Border(
        //   bottom: BorderSide(
        //     color: context.colors.dividerColor,
        //     width: 1.0,
        //   ),
        // ),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: ScreenStyleConfig.horizontalPadding,
        ),
        child: notification.type == 'custom'
            ? _buildCustomNotif(context, notification)
            : _buildRegularNotif(context, notification),
      ),
    );
  }

  Widget _buildCustomNotif(BuildContext context, NotificationData notif) {
    DateTime notifDate = DateTime.parse(notif.time);
    notifDate = notifDate.add(notifDate.timeZoneOffset).toUtc();

    return GestureDetector(
      onTap: () async {
        Uri url = Uri.parse(notif.url);

        if (!await launchUrl(url, mode: LaunchMode.externalApplication)) {
          if (context.mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                duration: Duration(seconds: 2),
                content: Text('Could not launch website'),
              ),
            );
          }
        }
      },
      child: Container(
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(
              width: 45.0,
              height: 45.0,
              // Create empty blue circle container.
              child: Container(
                decoration: BoxDecoration(
                  color: context.colors.accentColor,
                  borderRadius: BorderRadius.circular(22.5),
                ),
              ),
            ),
            const SizedBox(width: 10.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        if (notif.message.isNotEmpty)
                          TextSpan(
                            text: "New! ",
                            style: TextStyle(
                              fontSize: 14.0,
                              color: context.colors.brandColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        if (notif.message.isNotEmpty)
                          TextSpan(
                            text: notif.message,
                            style: TextStyle(
                              fontSize: 14.0,
                              color: context.isDarkMode
                                  ? AppColorsCache.dark().greyColor
                                  : AppColorsCache.light().timeColor,
                            ),
                          ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5.0),
                  Text(
                    timeago.format(notifDate),
                    style: TextStyle(
                      fontSize: 12.0,
                      color: context.colors.timeColor,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRegularNotif(BuildContext context, NotificationData notif) {
    DateTime notifDate = DateTime.parse(notif.time);
    notifDate = notifDate.add(notifDate.timeZoneOffset).toUtc();

    // Create notifMsg by removing only the first match of
    // notif.senderName word inside notif.message string.
    String notifMsg = notif.message.replaceFirst(notif.senderName, '');

    return GestureDetector(
      onTap: () {
        if (notif.type == 'follow') {
          _gotoArtistDetailScreen(
            context: context,
            artistId: notif.senderId,
            artistNicename: notif.senderNicename,
            artistDisplayName: notif.senderDisplayName,
            artistAvatarUrl: notif.senderAvatarUrl,
          );
        } else {
          _gotoPhotoDetailScreen(context: context, photoId: notif.postId);
        }
      },
      child: Container(
        color: Colors.transparent,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            if (notif.senderAvatarUrl.isNotEmpty)
              Avatar(
                imageUrl: notification.senderAvatarUrl,
                size: 46.0,
                onTap: () {
                  _gotoArtistDetailScreen(
                    context: context,
                    artistId: notif.senderId,
                    artistNicename: notif.senderNicename,
                    artistDisplayName: notif.senderDisplayName,
                    artistAvatarUrl: notif.senderAvatarUrl,
                  );
                },
              ),
            if (notif.senderAvatarUrl.isNotEmpty) const SizedBox(width: 10.0),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  RichText(
                    text: TextSpan(
                      children: [
                        TextSpan(
                          text: notif.senderName,
                          style: TextStyle(
                            fontSize: 14.0,
                            color: context.colors.brandColor,
                            fontWeight: FontWeight.w600,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              _gotoArtistDetailScreen(
                                context: context,
                                artistId: notif.senderId,
                                artistNicename: notif.senderNicename,
                                artistDisplayName: notif.senderDisplayName,
                                artistAvatarUrl: notif.senderAvatarUrl,
                              );
                            },
                        ),
                        TextSpan(
                          text: notifMsg,
                          style: TextStyle(
                            fontSize: 14.0,
                            color: context.colors.timeColor,
                          ),
                          recognizer: TapGestureRecognizer()
                            ..onTap = () {
                              if (notif.type == 'follow') {
                                _gotoArtistDetailScreen(
                                  context: context,
                                  artistId: notif.senderId,
                                  artistNicename: notif.senderNicename,
                                  artistDisplayName: notif.senderDisplayName,
                                  artistAvatarUrl: notif.senderAvatarUrl,
                                );
                              } else {
                                _gotoPhotoDetailScreen(
                                  context: context,
                                  photoId: notif.postId,
                                );
                              }
                            },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 5.0),
                  Text(
                    timeago.format(notifDate),
                    style: TextStyle(
                      fontSize: 12.0,
                      color: context.colors.timeColor,
                    ),
                  ),
                ],
              ),
            ),
            if (notif.thumbnailUrl.isNotEmpty) const SizedBox(width: 10.0),
            if (notif.thumbnailUrl.isNotEmpty)
              _buildNotifThumbnail(context, notif),
            if (notif.type == 'follow')
              ViewProfileButton(
                artistPartialData: ArtistPartialData(
                  id: notif.senderId,
                  nicename: notif.senderNicename,
                  displayName: notif.senderDisplayName,
                  profileUrl: '',
                  avatarUrl: notif.senderAvatarUrl,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotifThumbnail(BuildContext context, NotificationData notif) {
    return GestureDetector(
      onTap: () {
        _gotoPhotoDetailScreen(context: context, photoId: notif.postId);
      },
      child: PmNetworkImage(url: notif.thumbnailUrl, width: 45.0),
    );
  }

  void _gotoArtistDetailScreen({
    required BuildContext context,
    required int artistId,
    required String artistNicename,
    required String artistDisplayName,
    String artistProfileUrl = '',
    required String artistAvatarUrl,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: artistId,
            nicename: artistNicename,
            displayName: artistDisplayName,
            profileUrl: artistProfileUrl,
            avatarUrl: artistAvatarUrl,
          ),
        ),
      ),
    );
  }

  void _gotoPhotoDetailScreen({
    required BuildContext context,
    required int photoId,
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) =>
            PhotoDetailByIdScreen(photoId: photoId, isPhotoDetail: true),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/http_responses/artist_response.dart';
import 'package:portraitmode/artist/providers/artist_store_provider.dart';
import 'package:portraitmode/artist/services/artist_service.dart';
import 'package:portraitmode/artist/widgets/artist_detail/artist_not_found.dart';
import 'package:portraitmode/artist/widgets/artist_detail/artist_profile_loading.dart';
import 'package:portraitmode/artist/widgets/artist_detail/blocked_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/blocking_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile.dart';
import 'package:portraitmode/artist/widgets/artist_detail/my_profile/watch_helper/my_album_watcher.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/providers/category_list_provider.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/max_width/max_width.dart';
import 'package:portraitmode/notification/utils/notification_util.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

class ArtistDetailScreen extends ConsumerStatefulWidget {
  const ArtistDetailScreen({
    super.key,
    this.useBackButton = true,
    this.isOwnProfile,
    this.onInit,
    required this.partialData,
  });

  final bool useBackButton;
  final bool? isOwnProfile;
  final Function(Function)? onInit;
  final ArtistPartialData partialData;

  @override
  ArtistDetailScreenState createState() => ArtistDetailScreenState();
}

class ArtistDetailScreenState extends ConsumerState<ArtistDetailScreen> {
  final _scrollController = ScrollController();
  final _artistService = ArtistService();
  final _categoryListService = CategoryListService();
  bool _initialized = false;
  ArtistData? _artist;

  late bool _isOwnProfile;
  late int _profileId;

  @override
  void initState() {
    _profileId = LocalUserService.userId ?? 0;

    if (widget.isOwnProfile != null) {
      _isOwnProfile = widget.isOwnProfile!;
    } else {
      _isOwnProfile = _profileId == widget.partialData.id;
    }

    if (_isOwnProfile && widget.onInit != null) {
      widget.onInit!(_scrollToTop);
    }

    _fetchCategories();
    super.initState();
  }

  @override
  dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final useFutureBuilder = !_initialized;
    if (!_initialized) _initialized = true;

    return Column(
      children: [
        if (_isOwnProfile) const MyAlbumWatcher(),
        Expanded(
          child: MaxWidthBuilder(
            maxWidth: 768.0,
            builder: (BuildContext ctx, BoxConstraints constraints) {
              double containerWidth = constraints.maxWidth;

              return useFutureBuilder
                  ? FutureBuilder<ArtistData?>(
                      future: _fetchArtistData(),
                      builder:
                          (
                            BuildContext context,
                            AsyncSnapshot<ArtistData?> snapshot,
                          ) {
                            if (snapshot.connectionState ==
                                ConnectionState.done) {
                              _buildProfileWidget(
                                artist: snapshot.data,
                                containerWidth: containerWidth,
                              );
                            }

                            return ArtistProfileLoading(
                              artist: widget.partialData,
                            );
                          },
                    )
                  : _buildProfileWidget(
                      artist: _artist,
                      containerWidth: containerWidth,
                    );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildProfileWidget({
    required ArtistData? artist,
    required double containerWidth,
  }) {
    if (artist == null) {
      return const ArtistNotFound();
    }

    if (_isOwnProfile) {
      return MyProfile(
        useBackButton: widget.useBackButton,
        scrollController: _scrollController,
        containerWidth: containerWidth,
        artist: artist,
        onAlbumRefresh: () async {
          await _fetchCategories();
          await _fetchArtistData(isRefresh: true);
          await NotificationUtil().fetch(ref, _profileId);
        },
      );
    }

    final artistProps = ref.watch(artistFieldProvider(artist.id));
    final isBlocked = artistProps.isBlocked == true;
    final isBlocking = artistProps.isBlocking == true;

    if (isBlocked) {
      return BlockedProfile(containerWidth: containerWidth, artist: artist);
    }

    if (isBlocking) {
      return BlockingProfile(containerWidth: containerWidth, artist: artist);
    }

    return OtherProfile(
      useBackButton: widget.useBackButton,
      containerWidth: containerWidth,
      artist: artist,
      onAlbumRefresh: () async {
        await _fetchCategories();
        await _fetchArtistData(isRefresh: true);
        await NotificationUtil().fetch(ref, _profileId);
      },
    );
  }

  Future<void> _fetchCategories() async {
    final response = await _categoryListService.fetch();

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return;
    }

    // ref.read(categoryListProvider.notifier).replaceAll(response.data);

    globalCategoryList = response.data;
  }

  Future<ArtistData?> _fetchArtistData({bool isRefresh = false}) async {
    late ArtistResponse response;

    if (widget.partialData.id == 0) {
      response = await _artistService.findByNicename(
        widget.partialData.nicename,
      );
    } else {
      response = await _artistService.find(widget.partialData.id);
    }

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      return null;
    }

    if (response.data == null) {
      return null;
    }

    ArtistData? artistData = response.data;
    _artist = artistData;

    if (artistData != null) {
      if (_isOwnProfile) {
        ref.read(profileProvider.notifier).setProps({
          'nicename': artistData.nicename,
          'email': artistData.email,
          'role': artistData.role,
          'website': artistData.website,
          'profileUrl': artistData.profileUrl,
          'avatarUrl': artistData.avatarUrl,
          'firstName': artistData.firstName,
          'lastName': artistData.lastName,
          'displayName': artistData.displayName,
          'description': artistData.description,
          'location': artistData.location,
          'latestPhotoUrl': artistData.latestPhotoUrl,
          'totalPhotos': artistData.totalPhotos,
          'camera': artistData.camera,
          'focalLength': artistData.focalLength,
          'isFollowing': artistData.isFollowing,
          'totalFollowing': artistData.totalFollowing,
          'totalFollowers': artistData.totalFollowers,
          'membershipType': artistData.membershipType,
        });

        await LocalUserService.replace(
          LocalUserData(
            userId: artistData.id,
            nicename: artistData.nicename,
            role: artistData.role,
            displayName: artistData.displayName,
            profileUrl: artistData.profileUrl,
            avatarUrl: artistData.avatarUrl,
            membershipType: artistData.membershipType,
          ),
        );

        ref.read(myAlbumProvider.notifier).replaceAll(artistData.albums ?? []);
      }

      ref
          .read(artistStoreProvider.notifier)
          .updateItem(artistData, addIfNotExists: true);
    }

    return artistData;
  }

  void _scrollToTop() async {
    if (!_isOwnProfile) return;

    await _scrollController.animateTo(
      -100.0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );

    // if (_refreshIndicatorKey.currentState != null) {
    //   await _refreshIndicatorKey.currentState!.show();
    // }
  }
}

import 'package:portraitmode/photo/dto/photo_data.dart';

class PhotoDetailResponse {
  final bool success;
  final String errorCode;
  final String message;
  final PhotoData? data;

  PhotoDetailResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory PhotoDetailResponse.fromMap(Map<String, dynamic> map) {
    return PhotoDetailResponse(
      success: map['success'] ?? true,
      errorCode: map['error_code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? PhotoData.fromMap(map['data'])
          : null,
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/album/dto/album_data.dart';

final class MyAlbumNotifier extends Notifier<List<AlbumData>> {
  final Map<String, int> _slugIndexCache = {};

  @override
  List<AlbumData> build() => [];

  void _updateState(List<AlbumData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _slugIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _slugIndexCache[state[i].slug] = i;
    }
  }

  AlbumData? getItem(String slug) {
    final index = _slugIndexCache[slug];
    if (index == null || index >= state.length) return null;
    return state[index];
  }

  int getIndex(String slug) {
    return _slugIndexCache[slug] ?? -1;
  }

  bool hasItem(String slug) {
    return _slugIndexCache.containsKey(slug);
  }

  void addItem(AlbumData newItem) {
    if (hasItem(newItem.slug)) return;
    final updated = [...state, newItem];
    _updateState(updated);
  }

  void addItems(List<AlbumData> newItems) {
    final existingSlugs = _slugIndexCache.keys.toSet();
    final filtered = newItems.where(
      (item) => !existingSlugs.contains(item.slug),
    );
    if (filtered.isEmpty) return;

    final updated = [...state, ...filtered];
    _updateState(updated);
  }

  void updateItem(AlbumData newItem) {
    final index = _slugIndexCache[newItem.slug];
    if (index == null || index >= state.length) return;

    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  void incrementTotalPhotos(String albumSlug) {
    final album = getItem(albumSlug);
    if (album == null) return;

    final updatedAlbum = album.copyWith(totalPhotos: album.totalPhotos + 1);
    updateItem(updatedAlbum);
  }

  void decrementTotalPhotos(String albumSlug) {
    final album = getItem(albumSlug);
    if (album == null) return;

    final int totalPhotos = album.totalPhotos - 1;

    final updatedAlbum = album.copyWith(
      totalPhotos: totalPhotos < 0 ? 0 : totalPhotos,
    );

    updateItem(updatedAlbum);
  }

  void removeItem(String slug) {
    if (!_slugIndexCache.containsKey(slug)) return;
    final updated = state.where((item) => item.slug != slug).toList();
    _updateState(updated);
  }

  void replaceAll(List<AlbumData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }
}

final myAlbumProvider =
    NotifierProvider.autoDispose<MyAlbumNotifier, List<AlbumData>>(
      MyAlbumNotifier.new,
    );

bool globalMyAlbumSwitchToLastTab = false;

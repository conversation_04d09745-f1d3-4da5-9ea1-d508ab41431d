import 'package:form_field_validator/form_field_validator.dart';

class FieldLengthValidator extends TextFieldValidator {
  FieldLengthValidator({required this.length, required String errorText})
    : super(errorText);

  final int length;

  @override
  bool isValid(String? value) {
    return value!.length == length;
  }
}

class StartWithValidator extends TextFieldValidator {
  final String char;

  StartWithValidator(this.char, {required String errorText}) : super(errorText);

  @override
  bool get ignoreEmptyValues => false;

  @override
  bool isValid(String? value) {
    return (value!.isNotEmpty ? value.startsWith(char) : true);
  }
}

class MinWordsLengthValidator extends TextFieldValidator {
  MinWordsLengthValidator({required this.length, required String errorText})
    : super(errorText);

  final int length;

  @override
  bool get ignoreEmptyValues => true;

  @override
  bool isValid(String? value) {
    return (value!.isNotEmpty ? value.split(' ').length >= length : true);
  }
}

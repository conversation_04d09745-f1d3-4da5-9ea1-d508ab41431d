import 'package:flutter/foundation.dart';

/// Pagination state for better state management
@immutable
class PaginationState {
  final bool hasReachedEnd;
  final bool isLoadingMore;
  final Object? loadMoreError;

  const PaginationState({
    this.hasReachedEnd = false,
    this.isLoadingMore = false,
    this.loadMoreError,
  });

  PaginationState copyWith({
    bool? hasReachedEnd,
    bool? isLoadingMore,
    Object? loadMoreError,
  }) {
    return PaginationState(
      hasReachedEnd: hasReachedEnd ?? this.hasReachedEnd,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      loadMoreError: loadMoreError,
    );
  }

  bool get canLoadMore =>
      !isLoadingMore && !hasReachedEnd && loadMoreError == null;

  bool get hasLoadMoreError => loadMoreError != null;

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! PaginationState) return false;

    return runtimeType == other.runtimeType &&
        hasReachedEnd == other.hasReachedEnd &&
        isLoadingMore == other.isLoadingMore &&
        loadMoreError == other.loadMoreError;
  }

  @override
  int get hashCode => Object.hash(hasReachedEnd, isLoadingMore, loadMoreError);
}

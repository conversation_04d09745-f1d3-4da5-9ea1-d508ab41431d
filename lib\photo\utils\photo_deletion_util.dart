import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/category/providers/category_photo_list_provider.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/moderation/services/moderation_service.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/providers/archived_photos_provider.dart';
import 'package:portraitmode/photo/providers/backup/latest_photos_provider.dart';
import 'package:portraitmode/photo/providers/featured_photos_provider.dart';
import 'package:portraitmode/photo/services/delete_photo_service.dart';
import 'package:portraitmode/profile/providers/my_album_photo_list_provider.dart';
import 'package:portraitmode/profile/providers/my_album_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';

class PhotoDeletionUtil {
  final BuildContext context;
  final WidgetRef ref;
  final PhotoData photo;
  final bool isOwnPhoto;
  final VoidCallback? startLoading;
  final Function({void Function()? callback})? stopLoading;

  PhotoDeletionUtil({
    required this.context,
    required this.ref,
    required this.photo,
    required this.isOwnPhoto,
    this.startLoading,
    this.stopLoading,
  });

  Future<void> handleDeletion({required bool isPhotoReportModeration}) async {
    if (!context.mounted) return;
    startLoading?.call();

    late BaseResponse response;

    if (isPhotoReportModeration) {
      final moderationService = ModerationService();

      response = await moderationService.deleteReportedPhoto(
        photoId: photo.id,
        reason: 'none',
      );
    } else {
      final deletePhotoService = DeletePhotoService();
      response = await deletePhotoService.delete(photo.id);
    }

    if (!context.mounted) return;

    if (!response.success) {
      stopLoading?.call();

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    stopLoading?.call(
      callback: () {
        _visuallyDeletePhoto();
      },
    );
  }

  void _visuallyDeletePhoto() {
    if (!context.mounted) return;

    removeCategoryPhotoListItem(ref, photo.id);

    ref.read(latestPhotoIdsProvider.notifier).removeItem(photo.id);
    ref.read(featuredPhotoIdsProvider.notifier).removeItem(photo.id);
    ref.read(searchPhotoIdsProvider.notifier).removeItem(photo.id);

    if (isOwnPhoto) {
      ref.read(archivedPhotoIdsProvider.notifier).removeItem(photo.id);

      ref.read(myAlbumProvider.notifier).decrementTotalPhotos(photo.album);

      myAlbumPhotoListProviderMap.forEach((
        String albumSlug,
        NotifierProvider<MyAlbumPhotoListNotifier, List<PhotoData>> provider,
      ) {
        ref.read(provider.notifier).removeItem(photo.id);
      });
    }
  }
}

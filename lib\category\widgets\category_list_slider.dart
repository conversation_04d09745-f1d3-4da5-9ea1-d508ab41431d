import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/widgets/category_list_item.dart';
import 'package:portraitmode/category/widgets/slider_view_all_button.dart';
import 'package:portraitmode/search/widgets/search_screen_active.dart';

class CategoryListSlider extends StatelessWidget {
  final EdgeInsets? padding;
  final BoxDecoration? decoration;
  final String title;
  final TextStyle titleTextStyle;
  final String? viewAllText;
  final List<CategoryData> categoryList;
  final bool isLoading;

  const CategoryListSlider({
    super.key,
    this.padding,
    this.decoration,
    this.title = "Categories",
    this.titleTextStyle = const TextStyle(
      height: 1,
      fontSize: 13,
      fontWeight: FontWeight.w600,
    ),
    this.viewAllText,
    this.categoryList = const [],
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    List<List<CategoryData>> groupedList = [];

    const double sliderItemGap = 13.0;
    const double heightReducer = 0.0;

    if (!isLoading) {
      for (int i = 0; i < categoryList.length; i += 2) {
        CategoryData? secondElement = (i + 1) < categoryList.length
            ? categoryList[i + 1]
            : null;

        if (secondElement == null) {
          groupedList.add([categoryList[i]]);
          continue;
        }

        groupedList.add([categoryList[i], secondElement]);
      }
    }

    int totalItems = !isLoading ? groupedList.length : 6;

    double screenWidth = MediaQuery.sizeOf(context).width;
    double sliderItemWidth = (screenWidth / 2.7) - sliderItemGap;
    double itemHeight = sliderItemWidth - (sliderItemWidth / 6) - heightReducer;
    double sliderItemHeight = (itemHeight * 2) + sliderItemGap;

    return !isLoading && categoryList.isEmpty
        ? const SizedBox.shrink()
        : Container(
            padding: padding,
            decoration: decoration,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: 10.0,
                    left: ScreenStyleConfig.horizontalPadding,
                    right: ScreenStyleConfig.horizontalPadding,
                  ),
                  child: Row(
                    children: [
                      Text(title, style: titleTextStyle),
                      if (viewAllText != null) const Spacer(),
                      if (viewAllText != null)
                        SliderViewAllButton(
                          fontSize: 13.0,
                          viewAllText: viewAllText!,
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder: (context) =>
                                    const SearchScreenActive(initialIndex: 1),
                              ),
                            );
                          },
                        ),
                    ],
                  ),
                ),
                SizedBox(
                  height: sliderItemHeight,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: totalItems,
                    itemBuilder: (BuildContext context, int index) {
                      // Check if groupedList[index] exists.
                      if (!isLoading && index >= groupedList.length) {
                        return const SizedBox.shrink();
                      }

                      return Padding(
                        padding: EdgeInsets.only(
                          left: sliderItemGap,
                          right: index == totalItems - 1 ? sliderItemGap : 0,
                        ),
                        child: SizedBox(
                          width: sliderItemWidth,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              !isLoading
                                  ? CategoryListItem(
                                      category: groupedList[index][0],
                                      // isSquare: true,
                                      heightReducer: heightReducer,
                                    )
                                  : Container(
                                      height: itemHeight,
                                      width: double.infinity,
                                      decoration: BoxDecoration(
                                        color: context.colors.baseColorAlt,
                                        borderRadius: BorderRadius.circular(
                                          PhotoStyleConfig.borderRadius,
                                        ),
                                      ),
                                    ),
                              const SizedBox(height: sliderItemGap),
                              if (isLoading)
                                Container(
                                  height: itemHeight,
                                  width: double.infinity,
                                  decoration: BoxDecoration(
                                    color: context.colors.baseColorAlt,
                                    borderRadius: BorderRadius.circular(
                                      PhotoStyleConfig.borderRadius,
                                    ),
                                  ),
                                ),
                              if (!isLoading && groupedList[index].length > 1)
                                CategoryListItem(
                                  category: groupedList[index][1],
                                  heightReducer: heightReducer,
                                  // isSquare: true,
                                ),
                            ],
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
  }
}

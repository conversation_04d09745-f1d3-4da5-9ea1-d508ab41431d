import 'package:portraitmode/album/dto/album_data.dart';

class AlbumResponse {
  final bool success;
  final String errorCode;
  final String message;
  final AlbumData? data;

  AlbumResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory AlbumResponse.fromMap(Map<String, dynamic> map) {
    return AlbumResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? AlbumData.fromMap(map['data'])
          : null,
    );
  }
}

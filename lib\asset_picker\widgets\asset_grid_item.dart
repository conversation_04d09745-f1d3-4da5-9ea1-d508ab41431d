import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/widgets/asset_thumbnail_provider.dart';

class AssetGridItem extends StatelessWidget {
  final AssetEntity entity;
  final int size;
  final int quality;

  const AssetGridItem({
    super.key,
    required this.entity,
    required this.size,
    required this.quality,
  });

  @override
  Widget build(BuildContext context) {
    return _buildImage();
  }

  Widget _buildImage() {
    return AspectRatio(
      aspectRatio: 1,
      child: Image(
        filterQuality: FilterQuality.high,
        image: AssetThumbnailProvider(
          entity: entity,
          size: size,
          quality: quality,
        ),
        fit: BoxFit.cover,
      ),
    );
  }
}

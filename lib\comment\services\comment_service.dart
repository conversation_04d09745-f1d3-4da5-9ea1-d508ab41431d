// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
// Internal packages.
import 'package:portraitmode/comment/http_responses/comment_response.dart';

class CommentService extends BaseService {
  Future<CommentResponse> find({required int id}) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/comments/${id.toString()}',
      );

      return CommentResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentResponse.fromMap(e.response?.data);
      }

      return CommentResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return CommentResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<CommentResponse> add(CommentData commentData) async {
    String apiUrl = "${URL.baseApiUrl}/comments";

    try {
      final response = await http.post(apiUrl, data: commentData.toMap());

      return CommentResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentResponse.fromMap(e.response?.data);
      }

      return CommentResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return CommentResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<CommentResponse> edit(CommentData comment) async {
    String apiUrl = "${URL.baseApiUrl}/comments/${comment.id}";

    try {
      final response = await http.post(apiUrl, data: comment.toMap());

      return CommentResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentResponse.fromMap(e.response?.data);
      }

      return CommentResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return CommentResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<CommentResponse> delete(int commentId) async {
    String apiUrl = "${URL.baseApiUrl}/comments/$commentId";

    try {
      final response = await http.delete(apiUrl);

      return CommentResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CommentResponse.fromMap(e.response?.data);
      }

      return CommentResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return CommentResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> report({
    required int commentId,
    required String reason,
  }) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/report-comment',
        data: {'comment_id': commentId.toString(), 'reason': reason},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

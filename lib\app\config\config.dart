import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/env.dart';

class AppConfig {
  static const String appLogoPath = 'assets/logo.png';
  static const String appWhiteLogoPath = 'assets/logo-white.png';
}

class AvatarConfig {
  static const String defaultAvatar =
      'https://portraitmode.io/wp-content/themes/page-builder-framework-child/images/portraitmode-logo-circle-256x256.png';
}

class ScreenStyleConfig {
  static const double maxWidth = 768.0;
  static const double horizontalPadding = 13.0;
  static const double verticalPadding = 10.0;
}

class BoxStyleConfig {
  static const double padding = 15.0;
}

class PhotoStyleConfig {
  static const double borderRadius = 10.0;
}

class GoogleConfig {
  static const String mapApiKey = UnsafeKeysAndMustBeCanged.mapApiKey;

  static const String oAuthClientId = UnsafeKeysAndMustBeCanged.oAuthClientId;
}

class ImageSize {
  /// Get computed height based on the ratio of parent width to image width.
  static double computedHeight({
    required final double parentWidth,
    required final double imageWidth,
    required final double imageHeight,
  }) {
    final double widthRatio = parentWidth / imageWidth;
    final double height = imageHeight * widthRatio;

    return height;
  }
}

class ScreenSize {
  static double width(BuildContext context) {
    return MediaQuery.sizeOf(context).width;
  }

  static double height(BuildContext context) {
    return MediaQuery.sizeOf(context).height;
  }
}

class LayoutConfig {
  static const double contentTopGap = 13.0;

  static const double bottomNavBarHeight = 65.0;
}

class BottomSheetConfig {
  static const double menuItemHeight = 40.0;

  static const double topSpace = 9.0;

  // static const double extraSpace = 13.0 + topSpace;
  static const double extraSpace = 5.0 + topSpace;
  static const double borderRadius = 20.0;
}

class ImageCacheConfig {
  static const Duration duration = Duration(hours: 6);
  static const String cacheKey = 'pm_cache';
}

class LoadMoreConfig {
  /// The scroll threshold (as a fraction of the screen height)
  /// that triggers the "load more" action when nearing the bottom.
  ///
  /// For example: `0.2` means the load more will be triggered
  /// when the scroll position is within the last 20% of the screen.
  static const double tresholdByScreen = 0.4;

  /// The minimum pixel threshold from the bottom of the scroll view
  /// to trigger the "load more" behavior.
  static const double minTreshold = 250;

  /// The maximum pixel threshold from the bottom of the scroll view
  /// to trigger the "load more" behavior.
  static const double maxTreshold = 600;

  /// Number of items to load per page for generic lists.
  static const int itemsPerPage = 20;

  /// Number of camera items to load per page.
  static const int camerasPerPage = 20;

  /// Number of mixed feed items to load per page.
  static const int mixedFeedsPerPage = 16;

  /// Number of photo items to load per page.
  static const int photosPerPage = 15;

  /// Number of artist items to load per page.
  static const int artistsPerPage = 15;
}

class SearchConfig {
  static const double searchFieldHeight = 42.0;
}

class SliderConfig {
  static const double sliderTitleFontSize = 23;
  static const FontWeight sliderTitleFontWeight = FontWeight.w700;
}

class TabbarConfig {
  static const double screenXPadding = 8.0;
  static const double labelXPadding = 8.0;
}

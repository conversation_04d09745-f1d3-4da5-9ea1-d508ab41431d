import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class CameraPhotoListWatcher extends ConsumerWidget {
  final String slug;

  const CameraPhotoListWatcher({super.key, required this.slug});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    late final NotifierProvider<CameraPhotoListNotifier, List<PhotoData>>
    provider = getCameraPhotoListProvider(slug);

    final List<PhotoData> photoList = ref.watch(provider);

    return photoList.isNotEmpty
        ? const SizedBox.shrink()
        : const SizedBox.shrink();
  }
}

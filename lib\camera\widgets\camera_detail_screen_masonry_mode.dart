import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/camera/dto/camera_data.dart';
import 'package:portraitmode/camera/dto/camera_photo_list_interaction_data.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_interaction_provider.dart';
import 'package:portraitmode/camera/providers/camera_photo_list_provider.dart';
import 'package:portraitmode/camera/widgets/camera_detail_screen_list_mode.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/masonry_load_more.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';

class CameraDetailScreenMasonryMode extends ConsumerStatefulWidget {
  const CameraDetailScreenMasonryMode({super.key, required this.camera});

  final CameraData camera;

  @override
  CameraDetailScreenMasonryModeState createState() =>
      CameraDetailScreenMasonryModeState();
}

class CameraDetailScreenMasonryModeState
    extends ConsumerState<CameraDetailScreenMasonryMode> {
  final _scrollController = ScrollController();
  final photoListService = PhotoListService();

  late final int _profileId;

  static const _loadMorePerPage = LoadMoreConfig.camerasPerPage;
  bool _loadMoreEndReached = false;

  late final NotifierProvider<CameraPhotoListNotifier, List<PhotoData>>
  _cameraPhotoListProvider;

  late final NotifierProvider<
    CameraPhotoListInteractionNotifier,
    CameraPhotoListInteractionData
  >
  _interactionProvider;

  @override
  void initState() {
    _cameraPhotoListProvider = getCameraPhotoListProvider(widget.camera.slug);
    _interactionProvider = getCameraPhotoListInteractionProvider(
      widget.camera.slug,
    );
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // log('build screen: CameraDetailScreenMasonryMode');
    final photoList = ref.watch(_cameraPhotoListProvider);

    return Scaffold(
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: RefreshIndicator(
              onRefresh: _handleRefresh,
              child: NestedScrollView(
                headerSliverBuilder:
                    (BuildContext context, bool innerBoxIsScrolled) {
                      return [
                        SliverOverlapAbsorber(
                          handle:
                              NestedScrollView.sliverOverlapAbsorberHandleFor(
                                context,
                              ),
                        ),
                        // This PmSliverAppBar was originally inside of the
                        // SliverOverlapAbsorber under it's sliver property.
                        // I extracted it out because it was causing weird top gap.
                        PmSliverAppBar(
                          scrollController: _scrollController,
                          titleText: widget.camera.name,
                          useLogo: false,
                          automaticallyImplyLeading: true,
                        ),
                      ];
                    },
                body: MasonryLoadMore(
                  scrollController: _scrollController,
                  isFinished: _loadMoreEndReached,
                  onLoadMore: _handleLoadMore,
                  loadingWidgetColor: context.colors.baseColorAlt,
                  runOnEmptyResult: true,
                  loadingStatusText: "",
                  finishedStatusText: "",
                  padding: const EdgeInsets.symmetric(
                    vertical: 8.0,
                    horizontal: 8.0,
                  ),
                  crossAxisCount: 2,
                  mainAxisSpacing: 8.0,
                  crossAxisSpacing: 8.0,
                  itemsCount: photoList.length,
                  itemBuilder: (BuildContext masonryContext, int index) {
                    if (index >= photoList.length) {
                      return const SizedBox.shrink();
                    }

                    return PhotoMasonryItem(
                      key: ValueKey(photoList[index].id),
                      index: index,
                      photo: photoList[index],
                      isOwnProfile: photoList[index].authorId == _profileId,
                      screenName: 'camera_detail_screen',
                      onPhotoTap: () => _handleOnPhotoTap(index),
                    );
                  },
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  void _handleOnPhotoTap(int itemIndex) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CameraDetailScreenListMode(
          camera: widget.camera,
          initialScrollIndex: itemIndex,
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    ref.read(_interactionProvider.notifier).setLoadMoreLastId(0);
    _loadMoreEndReached = false;

    final PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: 0,
      cameraName: widget.camera.name,
    );

    _handlePhotosResponse(response, true, false);
  }

  Future<LoadMoreResult> _handleLoadMore() async {
    final int loadMoreLastId = ref.read(_interactionProvider).loadMoreLastId;
    final isFirstLoad = loadMoreLastId == 0;

    final PhotoListResponse response = await photoListService.fetch(
      limit: _loadMorePerPage,
      lastId: loadMoreLastId,
      cameraName: widget.camera.name,
    );

    _handlePhotosResponse(response, false, isFirstLoad);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handlePhotosResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(_interactionProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (mounted) {
      setState(() {
        if (isRefresh) {
          ref.read(_cameraPhotoListProvider.notifier).replaceAll(response.data);
        } else {
          if (isFirstLoad) {
            ref
                .read(_cameraPhotoListProvider.notifier)
                .replaceAll(response.data);
          } else {
            ref.read(_cameraPhotoListProvider.notifier).addItems(response.data);
          }
        }
      });
    }
  }
}

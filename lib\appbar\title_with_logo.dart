import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class TitleWithLogo extends StatelessWidget {
  const TitleWithLogo({super.key, this.title});

  /// The appbar title.
  final String? title;

  @override
  Widget build(BuildContext context) {
    return Row(
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        SizedBox(
          height: 35.0,
          child: Image.asset(
            (context.isDarkMode ? "assets/logo-white.png" : "assets/logo.png"),
            fit: BoxFit.cover,
          ),
        ),
        title != null
            ? Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Text(
                  title ?? '',
                  overflow: TextOverflow.ellipsis,
                  style: const TextStyle(
                    fontSize: 19.2,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              )
            : const SizedBox.shrink(),
      ],
    );
  }
}

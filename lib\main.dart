import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';
import 'package:flutter_native_splash/flutter_native_splash.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/app/utils/theme_manager.dart';
import 'package:portraitmode/app/widgets/pm_app.dart';

void main() async {
  WidgetsBinding widgetsBinding = WidgetsFlutterBinding.ensureInitialized();
  FlutterNativeSplash.preserve(widgetsBinding: widgetsBinding);

  await Hive.initFlutter();

  const FlutterSecureStorage secureStorage = FlutterSecureStorage();

  var containsEncryptionKey = await secureStorage.containsKey(
    key: 'encryptionKey',
  );

  if (!containsEncryptionKey) {
    final key = Hive.generateSecureKey();
    await secureStorage.write(
      key: 'encryptionKey',
      value: base64UrlEncode(key),
    );
  }

  final secureKey = await secureStorage.read(key: 'encryptionKey');
  final encryptionKey = base64Url.decode(secureKey!);

  await Future.wait([
    Hive.openBox('auth', encryptionCipher: HiveAesCipher(encryptionKey)),
    Hive.openBox('user'),
    Hive.openBox('theme'),
    Hive.openBox('settings'),
    PhotoManager.clearFileCache(),
    DefaultCacheManager().emptyCache(),
    // PmCacheManager().emptyCache(),
  ]);

  // Initialize theme manager early.
  await ThemeManager.instance.loadThemeFromHive();

  FlutterNativeSplash.remove();

  runApp(ProviderScope(child: PmApp()));
}

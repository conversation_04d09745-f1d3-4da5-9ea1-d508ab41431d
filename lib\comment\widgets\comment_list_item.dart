import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/avatar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/widgets/bottom_sheets/other_comment_bottom_sheet.dart';
import 'package:portraitmode/comment/widgets/bottom_sheets/own_comment_bottom_sheet.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/action_row.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/comment_content.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/deleting_indicator.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/sending_indicator.dart';
import 'package:portraitmode/comment/widgets/comment_list_item/view_replies.dart';
import 'package:portraitmode/misc/vertical_dots.dart';

class CommentListItem extends ConsumerStatefulWidget {
  final TextEditingController commentFieldController;
  final FocusNode commentFieldFocusNode;
  final CommentData comment;
  final CommentData? parentComment;
  final CommentData? topLevelParentComment;
  final bool isOwnComment;
  final String screenName;
  final VoidCallback? onEditCommentTap;
  final VoidCallback? onDeleteCommentTap;
  final Function(String)? onCommentReported;

  const CommentListItem({
    super.key,
    required this.commentFieldController,
    required this.commentFieldFocusNode,
    required this.comment,
    this.parentComment,
    this.topLevelParentComment,
    this.isOwnComment = false,
    this.screenName = '',
    this.onEditCommentTap,
    this.onDeleteCommentTap,
    this.onCommentReported,
  });

  @override
  CommentListItemState createState() => CommentListItemState();
}

class CommentListItemState extends ConsumerState<CommentListItem> {
  late final bool _isTopLevelComment;
  final double leftIndent = ScreenStyleConfig.horizontalPadding * 2;

  @override
  void initState() {
    super.initState();
    _isTopLevelComment = widget.comment.parentId == 0;
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: _handleCommentLongPress,
      child: Container(
        color: Colors.transparent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                right: widget.parentComment == null
                    ? ScreenStyleConfig.horizontalPadding
                    : 0.0,
                left: leftIndent,
                bottom: 6.0,
              ),
              child: Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(right: 8.0),
                    child: Avatar(
                      imageUrl: widget.comment.authorAvatarUrl,
                      size: 35.0,
                      onTap: () {
                        _handleArtistTap();
                      },
                    ),
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      GestureDetector(
                        onTap: () {
                          _handleArtistTap();
                        },
                        child: Text(
                          widget.comment.authorNicename,
                          style: const TextStyle(
                            fontWeight: FontWeight.w600,
                            fontSize: 13.0,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const Spacer(),
                  Padding(
                    padding: const EdgeInsets.only(left: 5.0, right: 6.0),
                    child: VerticalDots(
                      onTap: () {
                        if (widget.isOwnComment) {
                          _showOwnCommentBottomSheet();
                          return;
                        }

                        _showOtherCommentBottomSheet();
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: EdgeInsets.only(
                right: widget.parentComment == null
                    ? ScreenStyleConfig.horizontalPadding
                    : 0.0,
                left: leftIndent,
                bottom: 8.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  widget.comment.content.isNotEmpty
                      ? Padding(
                          padding: const EdgeInsets.only(bottom: 8.0),
                          child: CommentContent(
                            comment: widget.comment,
                            parentComment: widget.parentComment,
                          ),
                        )
                      : const SizedBox.shrink(),
                  _buildActionIndicator(),
                  _isTopLevelComment
                      ? ViewReplies(
                          commentFieldController: widget.commentFieldController,
                          commentFieldFocusNode: widget.commentFieldFocusNode,
                          comment: widget.comment,
                        )
                      : const SizedBox.shrink(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionIndicator() {
    if (widget.comment.submissionStatus == CommentSubmissionStatus.submitting) {
      return const SendingIndicator();
    }

    if (widget.comment.submissionStatus == CommentSubmissionStatus.deleting) {
      return const DeletingIndicator();
    }

    if (widget.comment.submissionStatus == CommentSubmissionStatus.deleted) {
      return Text(
        'Deleted',
        style: TextStyle(fontSize: 12.0, color: context.colors.dangerColor),
      );
    }

    return ActionRow(
      commentFieldController: widget.commentFieldController,
      commentFieldFocusNode: widget.commentFieldFocusNode,
      commentContext: context,
      comment: widget.comment,
    );
  }

  void _handleArtistTap() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          partialData: ArtistPartialData(
            id: widget.comment.authorId,
            nicename: widget.comment.authorNicename,
            displayName: widget.comment.authorDisplayName,
            profileUrl: widget.comment.authorProfileUrl,
            avatarUrl: widget.comment.authorAvatarUrl,
          ),
        ),
      ),
    );
  }

  void _handleCommentLongPress() {
    HapticFeedback.vibrate();

    if (widget.isOwnComment) {
      _showOwnCommentBottomSheet();
      return;
    }

    _showOtherCommentBottomSheet();
  }

  void _showOwnCommentBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return OwnCommentBottomSheet(
          comment: widget.comment,
          onEditCommentTap: widget.onEditCommentTap,
          onDeleteCommentTap: widget.onDeleteCommentTap,
        );
      },
    );
  }

  void _showOtherCommentBottomSheet() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return OtherCommentBottomSheet(
          comment: widget.comment,
          onCommentReported: _handleCommentReported,
        );
      },
    );
  }

  void _handleCommentReported(String message) {
    if (widget.onCommentReported != null) {
      widget.onCommentReported?.call(message);
      return;
    }

    if (mounted) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(message)));
    }
  }
}

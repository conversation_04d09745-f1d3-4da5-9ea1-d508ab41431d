import 'dart:typed_data';

import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';

class UploadPhotoService extends BaseService {
  Future<UploadPhotoResponse> submit({
    required Uint8List photoBytes,
    required String address,
    String? lat,
    String? lng,
    String? description,
    List<int>? categoryIds,
    bool needsFeedback = false,
  }) async {
    String apiUrl = "${URL.baseApiUrl}/photo";

    try {
      FormData formData = FormData.fromMap({
        'address': address,
        'lat': lat ?? '',
        'lng': lng ?? '',
        'description': description ?? '',
        'category_ids': categoryIds?.join(',') ?? '',
        'needs_feedback': needsFeedback ? 1 : 0,
        'photo': MultipartFile.fromBytes(photoBytes, filename: 'photo.jpg'),
      });

      final response = await http.post(
        apiUrl,
        data: formData,
        onSendProgress: (count, total) {
          // log("Uploading avatar: $count/$total");
        },
      );

      return UploadPhotoResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return UploadPhotoResponse.fromMap(e.response?.data);
      }

      return UploadPhotoResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return UploadPhotoResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

import 'package:easy_load_more/easy_load_more.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/app/dto/initial_data.dart';
import 'package:portraitmode/app/http_responses/initial_data_response.dart';
import 'package:portraitmode/app/providers/app_state_provider.dart';
import 'package:portraitmode/app/services/app_service.dart';
import 'package:portraitmode/app/utils/scroll_util.dart';
import 'package:portraitmode/artist/dto/simple_artist_data.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_list_slider.dart';
import 'package:portraitmode/common/utils/common_refresh_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/backup/latest_photos_provider.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/photo_list_item.dart';
import 'package:portraitmode/photo/widgets/trending_photo_list_slider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

final globalLatestPhotosTabContentKey =
    GlobalKey<LatestPhotosTabContentState>();

class LatestPhotosTabContent extends ConsumerStatefulWidget {
  const LatestPhotosTabContent({super.key});

  @override
  LatestPhotosTabContentState createState() => LatestPhotosTabContentState();
}

class LatestPhotosTabContentState extends ConsumerState<LatestPhotosTabContent>
    with AutomaticKeepAliveClientMixin<LatestPhotosTabContent> {
  final _scrollController = ScrollController();
  final refreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _loadMoreLastId = 0;
  int _currentPageNumber = 0;
  bool _loadMoreEndReached = false;
  List<int> _currentlyViewedPhotoIds = [];
  List<PhotoData> _trendingPhotoList = [];

  bool _initialDataFetched = false;
  List<CategoryData> _unfollowedCategoryList = [];

  final PhotoListService _photoListService = PhotoListService();
  final AppService _appService = AppService();
  final CategoryListService _categoryListService = CategoryListService();

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
    // log('initState screen: LatestPhotosTabContent');
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    bool behaveNewlyOpened = ref.watch(
      appStateProvider.select((data) => data.behaveNewlyOpened),
    );

    if (behaveNewlyOpened) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollToTop();

        if (mounted) {
          // Reset the behaveNewlyOpened flag after handling it.
          ref.read(appStateProvider.notifier).setBehaveNewlyOpened(false);
        }
      });
    }

    List<PhotoData> photoList = ref.watch(latestPhotosProvider);

    return Center(
      child: Container(
        color: context.colors.scaffoldColor,
        constraints: const BoxConstraints(maxWidth: 768.0),
        child: RefreshIndicator(
          key: refreshIndicatorKey,
          onRefresh: _handleRefresh,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: <Widget>[
              EasyLoadMore(
                isFinished: _loadMoreEndReached,
                onLoadMore: _handleLoadMore,
                loadingWidgetColor: context.colors.baseColorAlt,
                runOnEmptyResult: true,
                idleStatusText: "",
                loadingStatusText: "",
                finishedStatusText: "",
                child: SliverList(
                  delegate: SliverChildBuilderDelegate((
                    BuildContext context,
                    int index,
                  ) {
                    if (index >= photoList.length) {
                      return const SizedBox.shrink();
                    }

                    double marginTop = index == 0
                        ? LayoutConfig.contentTopGap
                        : 12.0;

                    bool isOwnProfile = photoList[index].authorId == _profileId;

                    return Container(
                      margin: EdgeInsets.only(top: marginTop),
                      child: (index == 9 || index == 18
                          ? Column(
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(bottom: 20.0),
                                  child: (index == 9
                                      ? _buildCategoriesSlider()
                                      : TrendingPhotoListSlider(
                                          photoList: _trendingPhotoList,
                                        )),
                                ),
                                _buildPhotoListItem(
                                  index,
                                  photoList[index],
                                  isOwnProfile,
                                ),
                              ],
                            )
                          : _buildPhotoListItem(
                              index,
                              photoList[index],
                              isOwnProfile,
                            )),
                    );
                  }, childCount: photoList.length),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPhotoListItem(int index, PhotoData photo, bool isOwnProfile) {
    return PhotoListItem(
      index: index,
      photo: photo,
      isOwnProfile: isOwnProfile,
      screenName: 'explore_screen',
    );
  }

  Widget _buildCategoriesSlider() {
    return CategoryListSlider(
      categoryList: _unfollowedCategoryList,
      padding: const EdgeInsets.only(top: 20.0, bottom: 22.0),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: 1.0,
          ),
          bottom: BorderSide(
            color: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt
                : AppColorsCache.light().baseColor,
            width: 1.0,
          ),
        ),
      ),
    );
  }

  void scrollToTop() async {
    // log('LatestPhotosTabContent: scrolling to top ');
    scrollListToTop(_scrollController, refreshIndicatorKey);
  }

  Future<void> _handleRefresh() async {
    _loadMoreLastId = 0;
    _loadMoreEndReached = false;

    List<dynamic> reponses = await Future.wait([
      _photoListService.fetchRecent(
        limit: _loadMorePerPage,
        lastId: _loadMoreLastId,
      ),
      _categoryListService.fetchUnfollowed(limit: 24),
      CommonRefreshUtil().fetch(ref, _profileId),
    ]);

    final PhotoListResponse photoListResponse = reponses[0];
    final CategoryListResponse categoryListResponse = reponses[1];

    if (categoryListResponse.success) {
      _unfollowedCategoryList = categoryListResponse.data;
    }

    _handlePhotoListResponse(photoListResponse, true);
  }

  Future<bool> _handleLoadMore() async {
    List<dynamic> responses = [];
    late PhotoListResponse photoListResponse;
    late PhotoListResponse trendingPhotoListResponse;
    bool shouldUpdateTrendingPhotos = false;
    final List<int> viewedPhotoIds = _currentPageNumber > 0
        ? _currentlyViewedPhotoIds
        : [];

    if (_currentPageNumber == 1) {
      shouldUpdateTrendingPhotos = true;

      responses = await Future.wait([
        _photoListService.fetchRecent(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        ),
        _photoListService.fetchTrending(limit: 12),
      ]);

      photoListResponse = responses[0];
      trendingPhotoListResponse = responses[1];
    } else {
      if (_currentPageNumber == 0 && !_initialDataFetched) {
        late InitialDataResponse initialDataResponse;

        responses = await Future.wait([
          _photoListService.fetchRecent(
            limit: _loadMorePerPage,
            lastId: _loadMoreLastId,
            viewedPhotoIds: viewedPhotoIds,
          ),
          _appService.fetchInitialData(),
          CommonRefreshUtil().fetch(ref, _profileId),
        ]);

        photoListResponse = responses[0];
        initialDataResponse = responses[1];

        if (initialDataResponse.success && initialDataResponse.data != null) {
          final InitialData initialData = initialDataResponse.data!;

          if (initialData.simpleArtistData != null) {
            ref
                .read(profileProvider.notifier)
                .setTotalFollowing(
                  initialData.simpleArtistData!.totalFollowing,
                );

            SimpleArtistData simpleArtistData = initialData.simpleArtistData!;

            await LocalUserService.replace(
              LocalUserData(
                userId: simpleArtistData.id,
                nicename: simpleArtistData.nicename,
                role: simpleArtistData.role,
                displayName: simpleArtistData.displayName,
                profileUrl: simpleArtistData.profileUrl,
                avatarUrl: simpleArtistData.avatarUrl,
                membershipType: simpleArtistData.membershipType,
              ),
            );
          }

          _unfollowedCategoryList = initialData.unfollowedCategoryList;

          if (initialData.notificationFetchResult != null) {
            ref
                .read(notificationProvider.notifier)
                .replace(initialData.notificationFetchResult!);
          }

          globalFeedbackTokensAmount = initialData.feedbackTokensAmount;

          _initialDataFetched = true;
        }
      } else {
        photoListResponse = await _photoListService.fetchRecent(
          limit: _loadMorePerPage,
          lastId: _loadMoreLastId,
          viewedPhotoIds: viewedPhotoIds,
        );
      }
    }

    _handlePhotoListResponse(photoListResponse, false);

    if (shouldUpdateTrendingPhotos &&
        mounted &&
        trendingPhotoListResponse.success &&
        trendingPhotoListResponse.data.isNotEmpty) {
      setState(() {
        _trendingPhotoList = trendingPhotoListResponse.data;
      });
    }

    return photoListResponse.success;
  }

  void _handlePhotoListResponse(PhotoListResponse response, bool isRefresh) {
    if (!response.success) {
      _currentlyViewedPhotoIds = [];

      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      _currentlyViewedPhotoIds = [];

      if (isRefresh) {
        ref.read(latestPhotoIdsProvider.notifier).replaceAll([]);
      }

      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return;
    }

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    final isFirstLoad = _loadMoreLastId == 0;
    _loadMoreLastId = response.data.last.id;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(latestPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      if (mounted) {
        setState(() {
          _currentPageNumber = 1;
        });
      }

      return;
    }

    if (isFirstLoad) {
      ref
          .read(latestPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));

      if (mounted) {
        setState(() {
          _currentPageNumber = 1;
        });
      }
    }

    ref
        .read(latestPhotoIdsProvider.notifier)
        .addItems(photoListToIdList(response.data));

    if (mounted) {
      setState(() {
        _currentPageNumber++;
      });
    }
  }
}

import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:portraitmode/app/config/colors.dart';

class AppVersion extends StatefulWidget {
  const AppVersion({super.key});

  @override
  AppVersionState createState() => AppVersionState();
}

class AppVersionState extends State<AppVersion> {
  String _version = '';

  @override
  void initState() {
    _getAppVersion();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Text(
      'PortraitMode v$_version',
      style: TextStyle(fontSize: 13, color: context.colors.darkerGreyColor),
    );
  }

  Future<void> _getAppVersion() async {
    PackageInfo packageInfo = await PackageInfo.fromPlatform();

    if (mounted) {
      setState(() {
        _version = packageInfo.version;
      });
    }
  }
}

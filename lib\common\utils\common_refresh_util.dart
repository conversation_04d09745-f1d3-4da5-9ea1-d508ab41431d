import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/common/services/common_refresh_service.dart';
import 'package:portraitmode/feedback_token/providers/feedback_token_amount_provider.dart';
import 'package:portraitmode/notification/providers/notification_provider.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';

/// The methods in this class are intended to be called without `await`.
/// So they don't block the code execution.
class CommonRefreshUtil {
  final _commonRefreshService = CommonRefreshService();

  /// Fetch `CommonRefreshData` from server.
  ///
  /// Even though this is an async method,
  /// it is intended to be called without "await".
  /// So they don't block the code execution.
  ///
  /// It's fine since `CommonRefreshData` is nice to work in the background.
  Future<void> fetch(WidgetRef ref, int artistId) async {
    final response = await _commonRefreshService.fetch();

    if (!response.success) {
      // log(response.message);
      return;
    }

    final responseData = response.data;

    if (responseData == null) {
      // log(response.message);
      return;
    }

    final feedbackTokensAmount = responseData.feedbackTokensAmount;

    // We don't need provider to store the `feedbackTokensAmount` value.
    globalFeedbackTokensAmount = feedbackTokensAmount;

    final notifFetchResult = responseData.notificationFetchResult;

    if (notifFetchResult != null) {
      // log('Total unread notifications from TabsScreen is: ${result.totalUnread.toString()}');

      ref.read(notificationProvider.notifier).replace(notifFetchResult);
    }

    final profileData = responseData.profileData;

    if (profileData != null) {
      // log('Profile data from TabsScreen is: ${profileData.toString()}');

      ref.read(profileProvider.notifier).replace(profileData);
    }
  }
}

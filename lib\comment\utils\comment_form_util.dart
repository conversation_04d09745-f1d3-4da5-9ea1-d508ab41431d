String replaceFirstMention(String mention, String commentContent) {
  String newCommentContent = commentContent;

  if (newCommentContent.indexOf(mention) != 0) {
    List<String> parts = newCommentContent.split(' ');

    if (parts.isNotEmpty && parts[0].contains('@')) {
      newCommentContent = newCommentContent.replaceFirst(parts[0], mention);
    } else {
      newCommentContent = '$mention $newCommentContent';
    }
  }

  return newCommentContent;
}

import 'package:portraitmode/youtube/dto/youtube_channel_data.dart';

class SaveYoutubeChannelsResponse {
  final bool success;
  final String? errorCode;
  final String message;
  final List<YoutubeChannelData> data;

  SaveYoutubeChannelsResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory SaveYoutubeChannelsResponse.fromMap(Map<String, dynamic> map) {
    return SaveYoutubeChannelsResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List
          ? (map['data'] as List)
                .map<YoutubeChannelData>(
                  (channel) => YoutubeChannelData.fromMap(channel),
                )
                .toList()
          : [],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';

class ViewProfileButton extends StatelessWidget {
  const ViewProfileButton({super.key, required this.artistPartialData});

  final ArtistPartialData artistPartialData;

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 30.0,
      child: ElevatedButton(
        onPressed: () {
          Navigator.push(
            context,
            MaterialPageRoute(
              builder: (context) =>
                  ArtistDetailScreen(partialData: artistPartialData),
            ),
          );
        },
        style: ElevatedButton.styleFrom(
          elevation: 0.0,
          backgroundColor: context.colors.accentColor,
          padding: const EdgeInsets.symmetric(horizontal: 12.0),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(15.0),
          ),
        ),
        child: const Text(
          'View profile',
          style: TextStyle(
            fontWeight: FontWeight.w400,
            fontSize: 12.0,
            color: Colors.white,
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class SubmissionGuidelines extends StatelessWidget {
  final String? artistDisplayName;

  const SubmissionGuidelines({super.key, this.artistDisplayName});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 8.0),
        _buildRuleBlock(
          context: context,
          title: 'Quality',
          description:
              'We maintain a high quality at PortraitMode. Make sure your image is at least 2000px in either width or height.',
        ),
        const SizedBox(height: 16.0),
        _buildRuleBlock(
          context: context,
          title: 'Categories',
          description:
              'To ensure that PortraitMode remains a valuable resource for photographers and enthusiasts alike, please use categories responsibly and accurately.',
        ),
        const SizedBox(height: 16.0),
        _buildRuleBlock(
          context: context,
          title: 'Editing',
          description:
              'Do not add visual effects or "over-edit" your photos. Avoid adding watermarks, text, logos, frames, borders, etc. to your images. Montages cannot be approved.',
        ),
        const SizedBox(height: 16.0),
        _buildRuleBlock(
          context: context,
          title: 'Photographer & permissions',
          description:
              'You must be the original photographer of the image. Avoid entering company/brand names or aliases in the name fields on your profile.',
        ),
        const SizedBox(height: 16.0),
        _buildRuleBlock(
          context: context,
          title: 'Nudity or violence',
          description:
              'PortraitMode is a place for all ages. Do not upload photos showing violence or graphic content. Photos cannot contain nudity.',
        ),
      ],
    );
  }

  Widget _buildRuleBlock({
    required BuildContext context,
    required String title,
    required String description,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 16.0),
      decoration: BoxDecoration(
        color: context.colors.baseColorAlt,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(fontSize: 14.0, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 8.0),
          Text(
            description,
            style: const TextStyle(fontSize: 14.0, height: 1.6),
          ),
        ],
      ),
    );
  }
}

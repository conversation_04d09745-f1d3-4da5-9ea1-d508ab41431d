import 'package:portraitmode/camera/dto/camera_data.dart';

class CameraResponse {
  final bool success;
  final String errorCode;
  final String message;
  final CameraData? data;

  CameraResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory CameraResponse.fromMap(Map<String, dynamic> map) {
    return CameraResponse(
      success: map['success'] ?? true,
      errorCode: map['error_code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? CameraData.fromMap(map['data'])
          : null,
    );
  }
}

import 'package:flutter/material.dart';

class ThemeScreenListTile extends StatelessWidget {
  final String title;
  final String value;
  final bool selected;
  final Function(String)? onTap;

  const ThemeScreenListTile({
    super.key,
    required this.title,
    required this.value,
    this.selected = false,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return ListTile(
      dense: true,
      visualDensity: const VisualDensity(horizontal: 0.0, vertical: -2.0),
      horizontalTitleGap: 1.0,
      minLeadingWidth: 28.0,
      minVerticalPadding: 0.0,
      title: Text(title, style: const TextStyle(fontSize: 16.0)),
      // Add radio button to the right side of the list tile
      trailing: Radio<String>(
        value: value,
        groupValue: selected ? value : 'ungrouped',
        onChanged: (String? value) {
          if (onTap == null) return;
          onTap!(value!);
        },
      ),
      onTap: () {
        if (onTap == null) return;
        onTap!(value);
      },
    );
  }
}

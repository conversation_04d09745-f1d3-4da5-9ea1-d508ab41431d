import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';

class OwnCommentBottomSheet extends ConsumerStatefulWidget {
  final CommentData comment;
  final VoidCallback? onEditCommentTap;
  final VoidCallback? onDeleteCommentTap;

  const OwnCommentBottomSheet({
    super.key,
    required this.comment,
    this.onEditCommentTap,
    this.onDeleteCommentTap,
  });

  @override
  OwnCommentBottomSheetState createState() => OwnCommentBottomSheetState();
}

class OwnCommentBottomSheetState extends ConsumerState<OwnCommentBottomSheet> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    menuItems.addAll([
      ModalListTile(
        title: "Edit comment",
        iconData: Ionicons.create_outline,
        onTap: () {
          Navigator.pop(context);
          widget.onEditCommentTap?.call();
        },
      ),
      ModalListTile(
        title: 'Delete comment',
        iconData: Ionicons.archive_outline,
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        onTap: () {
          Navigator.pop(context);
          widget.onDeleteCommentTap?.call();
        },
      ),
    ]);

    return Container(
      padding: const EdgeInsets.only(top: BottomSheetConfig.topSpace),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(BottomSheetConfig.borderRadius),
          topRight: Radius.circular(BottomSheetConfig.borderRadius),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height:
              BottomSheetConfig.extraSpace +
              (BottomSheetConfig.menuItemHeight * menuItems.length),
          child: Column(children: menuItems),
        ),
      ),
    );
  }
}

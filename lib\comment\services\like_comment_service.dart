// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/comment/http_responses/like_comment_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class LikeCommentService extends BaseService {
  Future<LikeCommentResponse> like(int photoId) async {
    return doAction(photoId, 'like');
  }

  Future<LikeCommentResponse> unlike(int photoId) async {
    return doAction(photoId, 'unlike');
  }

  Future<LikeCommentResponse> doAction(int photoId, String actionType) async {
    try {
      final response = await http.post(
        '${URL.baseApiUrl}/$actionType-comment',
        data: {'id': photoId.toString()},
      );

      return LikeCommentResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return LikeCommentResponse.fromMap(e.response?.data);
      }

      return LikeCommentResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return LikeCommentResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

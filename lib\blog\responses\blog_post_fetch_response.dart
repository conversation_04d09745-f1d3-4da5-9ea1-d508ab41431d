import 'package:portraitmode/blog/dto/blog_post_data.dart';

class BlogPostFetchResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<BlogPostData> data;

  BlogPostFetchResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory BlogPostFetchResponse.fromMap(Map<String, dynamic> map) {
    return BlogPostFetchResponse(
      success: map['success'] ?? true,
      errorCode: map['error_code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null
          ? List<BlogPostData>.from(
              map['data'].map((x) => BlogPostData.fromMap(x)),
            )
          : [],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/services/comment_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/moderation/utils/reason.dart';

class ReportCommentBottomSheet extends ConsumerStatefulWidget {
  const ReportCommentBottomSheet({
    super.key,
    required this.commentToReport,
    this.onCommentReported,
  });

  final CommentData commentToReport;
  final Function(String)? onCommentReported;

  @override
  ReportCommentBottomSheetState createState() =>
      ReportCommentBottomSheetState();
}

class ReportCommentBottomSheetState
    extends ConsumerState<ReportCommentBottomSheet> {
  final _commentService = CommentService();
  bool _isLoading = false;
  String _selectedReason = '';

  final Map<String, String> _reasons = getCommentReportingReasons();

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      maxChildSize: 0.9,
      initialChildSize: 0.35,
      expand: false,
      builder: ((context, scrollController) {
        return Container(
          padding: EdgeInsets.only(
            top: BottomSheetConfig.topSpace,
            bottom: MediaQuery.viewInsetsOf(context).bottom,
          ),
          decoration: BoxDecoration(
            color: context.colors.scaffoldColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(BottomSheetConfig.borderRadius),
              topRight: Radius.circular(BottomSheetConfig.borderRadius),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                SingleChildScrollView(
                  padding: const EdgeInsets.only(top: 0.0, bottom: 12.0),
                  controller: scrollController,
                  child: const ModalDragHandle(),
                ),
                Expanded(
                  child: ListView(
                    controller: scrollController,
                    children: [
                      for (String reason in _reasons.keys)
                        ListTile(
                          title: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                _reasons[reason]!,
                                style: const TextStyle(fontSize: 14.5),
                              ),
                              if (_selectedReason == reason)
                                const SizedBox(width: 5.0),
                              if (_selectedReason == reason)
                                Icon(
                                  Ionicons.checkmark_circle_sharp,
                                  color: context.colors.accentColor,
                                  size: 15,
                                ),
                            ],
                          ),
                          onTap: () => _handleOnTap(reason),
                          // dense: true,
                          visualDensity: const VisualDensity(vertical: -3.0),
                        ),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 8.0,
                    bottom: ScreenStyleConfig.horizontalPadding,
                    left: ScreenStyleConfig.horizontalPadding,
                    right: ScreenStyleConfig.horizontalPadding,
                  ),
                  child: SubmitButton(
                    buttonText: "Report",
                    width: double.infinity,
                    height: 40.0,
                    fontWeight: FontWeight.w600,
                    onPressed: () async {
                      await _handleOnSubmit();
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      }),
    );
  }

  Future<void> _handleOnTap(String targettedAlbumSlug) async {
    if (!mounted) return;

    setState(() {
      if (_selectedReason == targettedAlbumSlug) {
        _selectedReason = '';
      } else {
        _selectedReason = targettedAlbumSlug;
      }
    });
  }

  Future<void> _handleOnSubmit() async {
    if (_isLoading) return;

    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    BaseResponse response = await _commentService.report(
      commentId: widget.commentToReport.id,
      reason: _selectedReason,
    );

    if (!response.success) {
      if (mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }

        setState(() {
          _isLoading = false;
        });
      }

      return;
    }

    if (mounted) {
      setState(() {
        _isLoading = false;
      });

      Navigator.of(context).pop();
    }

    widget.onCommentReported?.call(response.message);
  }
}

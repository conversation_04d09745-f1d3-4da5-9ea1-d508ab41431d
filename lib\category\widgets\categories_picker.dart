import 'dart:math';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/category/dto/category_data.dart';
import 'package:portraitmode/category/http_responses/category_list_response.dart';
import 'package:portraitmode/category/services/category_list_service.dart';
import 'package:portraitmode/category/widgets/category_search_field.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class CategoriesPicker extends ConsumerStatefulWidget {
  const CategoriesPicker({
    super.key,
    this.selectedCategories = const [],
    required this.onClose,
  });

  final List<CategoryData> selectedCategories;
  final Function(List<CategoryData>) onClose;

  @override
  CategoriesPickerState createState() => CategoriesPickerState();
}

class CategoriesPickerState extends ConsumerState<CategoriesPicker> {
  final _scrollController = ScrollController();
  final _categoryListService = CategoryListService();
  final _searchFieldController = TextEditingController();

  bool _isLoading = true;
  int maxSelectedCategories = 10;
  List<CategoryData> _categories = [];
  List<CategoryData> _renderedCategories = [];
  late List<CategoryData> _selectedCategories;

  @override
  void initState() {
    _selectedCategories = widget.selectedCategories;
    _renderedCategories = _selectedCategories;
    _handleOnInit();
    super.initState();
  }

  @override
  void dispose() {
    _searchFieldController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.all(ScreenStyleConfig.horizontalPadding),
        child: SafeArea(
          child: SizedBox(
            height: 40.0,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.all(6.0),
                backgroundColor: context.colors.accentColor,
                elevation: 0.0,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(4.0),
                ),
              ),
              onPressed: () {
                widget.onClose(_selectedCategories);
                Navigator.pop(context, _selectedCategories);
              },
              child: const Text('Confirm'),
            ),
          ),
        ),
      ),
      body: SafeArea(
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 768.0),
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                PmSliverAppBar(
                  scrollController: _scrollController,
                  titleText: "Select a category",
                  useLogo: false,
                  automaticallyImplyLeading: true,
                  actions: const [],
                ),
                SliverPersistentHeader(
                  pinned: true,
                  delegate: DelegateCategorySearchField(
                    controller: _searchFieldController,
                    onChanged: _handleSearch,
                  ),
                ),
                SliverToBoxAdapter(
                  child: RefreshIndicator(
                    onRefresh: _handleRefresh,
                    child: Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: _isLoading ? _buildLoadingChips() : _buildChips(),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingChips() {
    const sizes = [60, 80, 90, 110];
    Random random = Random();
    Color loadingColor = context.colors.baseColorAlt;

    return Wrap(
      spacing: 8.0,
      runSpacing: -6.0,
      children: [
        for (var i = 0; i < 25; i++)
          Chip(
            backgroundColor: loadingColor,
            label: SizedBox(
              width: sizes[random.nextInt(sizes.length)].toDouble(),
            ),
          ),
      ],
    );
  }

  Widget _buildChips() {
    return Wrap(
      spacing: 8.0,
      runSpacing: -6.0,
      children: [
        for (final category in _renderedCategories)
          ChoiceChip(
            label: Text(category.name),
            labelStyle: TextStyle(
              color: _isSelected(category)
                  ? context.colors.accentColor
                  : context.colors.brandColor,
            ),
            elevation: 0.0,
            backgroundColor: context.colors.baseColorAlt,
            selectedColor: context.isDarkMode
                ? AppColorsCache.dark().baseColorAlt.withAlpha(70)
                : AppColorsCache.light().baseColor,
            showCheckmark: false,
            selected: _selectedCategories.any(
              (element) => element.id == category.id,
            ),
            onSelected: (bool selected) {
              _handleChipSelected(selected, category);
            },
          ),
      ],
    );
  }

  bool _isSelected(CategoryData category) {
    return _selectedCategories.any((element) => element.id == category.id);
  }

  void _handleChipSelected(bool selected, CategoryData category) {
    if (selected) {
      if (_renderedCategories.length != _categories.length) {
        _searchFieldController.clear();
      }

      if (_selectedCategories.length >= maxSelectedCategories) {
        if (mounted) {
          setState(() {
            _selectedCategories = _selectedCategories
                .where((element) => element.id != category.id)
                .toList();
          });
        }

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'You can only select up to $maxSelectedCategories categories.',
            ),
            duration: const Duration(seconds: 2),
          ),
        );

        return;
      }
    }

    if (mounted) {
      setState(() {
        if (selected) {
          _selectedCategories = [..._selectedCategories, category];
          _renderedCategories = _categories;
        } else {
          _selectedCategories = _selectedCategories
              .where((element) => element.id != category.id)
              .toList();
        }
      });
    }

    FocusScope.of(context).unfocus();
  }

  void _handleOnInit() async {
    CategoryListResponse response = await _categoryListService.fetch();
    _handleResponse(response);
  }

  void _handleSearch(String value) {
    if (value.isEmpty) {
      if (mounted) {
        setState(() {
          _renderedCategories = _categories;
        });
      }

      return;
    }

    List<CategoryData> results = _categories.where((cat) {
      return cat.name.toLowerCase().contains(value.toLowerCase());
    }).toList();

    if (mounted) {
      setState(() {
        _renderedCategories = results;
      });
    }
  }

  Future<void> _handleRefresh() async {
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    CategoryListResponse response = await _categoryListService.fetch();

    _handleResponse(response);
  }

  void _handleResponse(CategoryListResponse response) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
        return;
      }
    }

    if (mounted) {
      setState(() {
        _categories = response.data;
        _renderedCategories = response.data;
        _isLoading = false;
      });
    }
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

enum AppSnackBarType { success, info, error }

void showAppSnackBar({
  required BuildContext context,
  AppSnackBarType type = AppSnackBarType.error,
  required String message,
}) {
  ScaffoldMessenger.maybeOf(context)?.showSnackBar(
    SnackBar(
      backgroundColor: type == AppSnackBarType.error
          ? context.colors.dangerColor
          : null,
      content: Text(message, style: const TextStyle(color: Colors.white)),
    ),
  );
}

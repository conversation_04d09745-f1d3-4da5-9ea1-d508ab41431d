import 'package:flutter/material.dart';

void scrollListToTop(
  ScrollController scrollController,
  GlobalKey<RefreshIndicatorState> refreshIndicatorKey,
) async {
  await scrollController.animateTo(
    -100.0,
    duration: const Duration(milliseconds: 500),
    curve: Curves.easeInOut,
  );

  if (refreshIndicatorKey.currentState != null) {
    await refreshIndicatorKey.currentState?.show();
  }
}

double getVerticalCacheExtent(BuildContext context) {
  final double screenHeight = MediaQuery.sizeOf(context).height;

  return screenHeight * 2.5;
}

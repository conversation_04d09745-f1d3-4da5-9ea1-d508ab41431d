import 'package:portraitmode/artist/dto/artist_data.dart';

class ArtistListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<ArtistData> data;

  ArtistListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory ArtistListResponse.fromMap(Map<String, dynamic> map) {
    return ArtistListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<ArtistData>((data) => ArtistData.fromMap(data))
                .toList()
          : [],
    );
  }
}

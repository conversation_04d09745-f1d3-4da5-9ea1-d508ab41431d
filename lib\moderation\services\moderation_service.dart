// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
// Internal packages.
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/common/enum.dart';
import 'package:portraitmode/moderation/http_responses/featured_suggestion_list_response.dart';
import 'package:portraitmode/moderation/http_responses/reported_photo_list_response.dart';

class FeaturedAssignmentProps {
  final int photoId;
  final AssignmentActionType action;
  final bool notifyAuthor;

  FeaturedAssignmentProps({
    required this.photoId,
    required this.action,
    this.notifyAuthor = false,
  });
}

class ModerationService extends BaseService {
  Future<ReportedPhotoListResponse> fetchReportedPhotos({
    int limit = 15,
    int lastId = 0,
  }) async {
    final String url =
        '${URL.baseApiUrl}/photo-moderation/reported-photos/${limit.toString()}/${lastId.toString()}';

    try {
      final response = await http.get(url);

      return ReportedPhotoListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return ReportedPhotoListResponse.fromMap(e.response?.data);
      }

      return ReportedPhotoListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return ReportedPhotoListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }

  Future<FeaturedSuggestionListResponse> fetchFeaturedSuggestions({
    int limit = 15,
    int lastId = 0,
  }) async {
    final String url =
        '${URL.baseApiUrl}/photo-moderation/featured-suggestions/${limit.toString()}/${lastId.toString()}';

    try {
      final response = await http.get(url);

      return FeaturedSuggestionListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return FeaturedSuggestionListResponse.fromMap(e.response?.data);
      }

      return FeaturedSuggestionListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    }
  }

  Future<BaseResponse> dismiss({
    required int photoId,
    required String moderationType,
  }) async {
    const String url = '${URL.baseApiUrl}/photo-moderation/dismiss';

    try {
      final response = await http.put(
        url,
        data: {
          'photo_id': photoId.toString(),
          'moderation_type': moderationType,
        },
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> deleteReportedPhoto({
    required int photoId,
    required String reason,
  }) async {
    final String url =
        '${URL.baseApiUrl}/photo-moderation/reported-photo/${photoId.toString()}/$reason';

    try {
      final response = await http.delete(url);

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> featuredAssignment({
    required int photoId,
    required AssignmentActionType actionType,
    bool notifyAuthor = false,
  }) async {
    String url = '${URL.baseApiUrl}/featured-photo-assignment/$photoId';

    try {
      final response = await http.put(
        url,
        data: {'action_type': actionType.text, 'notify': notifyAuthor ? 1 : 0},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> potdAssignment({
    required int photoId,
    required AssignmentActionType actionType,
  }) async {
    String url = '${URL.baseApiUrl}/potd-assignment/$photoId';

    try {
      final response = await http.put(
        url,
        data: {'action_type': actionType.text},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> approveFeaturedSuggestion({
    required int photoId,
    required int notifyAuthor,
  }) async {
    String url = '${URL.baseApiUrl}/photo-moderation/approve-featured';

    try {
      final response = await http.put(
        url,
        data: {
          'photo_id': photoId.toString(),
          'notify': notifyAuthor.toString(),
        },
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }

  Future<BaseResponse> unfeaturePhoto({required int photoId}) async {
    String url = '${URL.baseApiUrl}/photo-moderation/unfeature';

    try {
      final response = await http.put(
        url,
        data: {'photo_id': photoId.toString()},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

// Extension packages.
import 'package:dio/dio.dart';

// Internal packages.
import 'package:portraitmode/photo/http_responses/upload_photo_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';

class EditPhotoService extends BaseService {
  Future<UploadPhotoResponse> submit({
    required int photoId,
    String? description,
    List<int>? categoryIds,
  }) async {
    String apiUrl = "${URL.baseApiUrl}/photo/edit";

    try {
      final response = await http.post(
        apiUrl,
        data: {
          'id': photoId.toString(),
          'description': description ?? '',
          'category_ids': categoryIds?.join(',') ?? '',
        },
      );

      return UploadPhotoResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return UploadPhotoResponse.fromMap(e.response?.data);
      }

      return UploadPhotoResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return UploadPhotoResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

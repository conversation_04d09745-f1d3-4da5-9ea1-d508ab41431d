import 'package:portraitmode/app/config/config.dart';

class SimpleArtistData {
  final int id;
  final String nicename;
  final String role;
  final String displayName;
  final String profileUrl;
  final String avatarUrl;
  final int totalFollowing;
  final String membershipType;

  SimpleArtistData({
    this.id = 0,
    this.nicename = '',
    this.role = '',
    this.displayName = '',
    this.profileUrl = '',
    this.avatarUrl = AvatarConfig.defaultAvatar,
    this.totalFollowing = 0,
    this.membershipType = '',
  });

  factory SimpleArtistData.fromMap(Map<String, dynamic> map) {
    return SimpleArtistData(
      id: map['id'] ?? 0,
      nicename: map['nicename'] ?? '',
      role: map['role'] ?? '',
      displayName: map['displayName'] ?? '',
      profileUrl: map['profileUrl'] ?? '',
      avatarUrl: map['avatarUrl'] ?? AvatarConfig.defaultAvatar,
      totalFollowing: map['totalFollowing'] ?? 0,
      membershipType: map['membershipType'] ?? '',
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/misc/vertical_dots.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/appbar/avatar.dart';

class ReviewPhotoHeader extends ConsumerStatefulWidget {
  final PhotoData photo;
  final bool isOwnPhoto;
  final Function? onDotsMenuTap;

  const ReviewPhotoHeader({
    super.key,
    required this.photo,
    this.isOwnPhoto = false,
    this.onDotsMenuTap,
  });

  @override
  ReviewPhotoHeaderState createState() => ReviewPhotoHeaderState();
}

class ReviewPhotoHeaderState extends ConsumerState<ReviewPhotoHeader> {
  @override
  Widget build(BuildContext context) {
    String authorNicename = widget.photo.authorNicename;
    String authorProfileUrl = widget.photo.authorProfileUrl;
    String authorDisplayName = widget.photo.authorDisplayName;
    String authorAvatarUrl = widget.photo.authorAvatarUrl;
    String authorMembershipType = widget.photo.authorMembershipType;

    return Row(
      children: [
        Padding(
          padding: const EdgeInsets.only(right: 5.0),
          child: Avatar(
            imageUrl: authorAvatarUrl,
            size: 31.0,
            onTap: () {
              _handlePhotoTap(
                context: context,
                isOwnProfile: widget.isOwnPhoto,
                authorNicename: authorNicename,
                authorProfileUrl: authorProfileUrl,
                authorDisplayName: authorDisplayName,
                authorAvatarUrl: authorAvatarUrl,
                authorMembershipType: authorMembershipType,
              );
            },
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              _handlePhotoTap(
                context: context,
                authorNicename: authorNicename,
                authorProfileUrl: authorProfileUrl,
                authorDisplayName: authorDisplayName,
                authorAvatarUrl: authorAvatarUrl,
                authorMembershipType: authorMembershipType,
              );
            },
            child: Text(
              authorDisplayName,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 13.0,
              ),
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(left: 5.0),
          child: VerticalDots(
            onTap: () {
              if (widget.onDotsMenuTap != null) {
                widget.onDotsMenuTap!();
                return;
              }
            },
          ),
        ),
      ],
    );
  }

  void _handlePhotoTap({
    required BuildContext context,
    bool isOwnProfile = false,
    String authorNicename = '',
    String authorProfileUrl = '',
    String authorDisplayName = '',
    String authorAvatarUrl = '',
    String authorMembershipType = '',
  }) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => ArtistDetailScreen(
          isOwnProfile: isOwnProfile,
          partialData: ArtistPartialData(
            id: widget.photo.authorId,
            nicename: authorNicename,
            displayName: authorDisplayName,
            profileUrl: authorProfileUrl,
            avatarUrl: authorAvatarUrl,
            membershipType: authorMembershipType,
          ),
        ),
      ),
    );
  }
}

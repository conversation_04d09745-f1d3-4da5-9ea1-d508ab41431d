import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

@immutable
final class AlbumData {
  const AlbumData({this.slug = '', this.text = '', this.totalPhotos = 0});

  final String slug;
  final String text;
  final int totalPhotos;

  AlbumData copyWith({String? slug, String? text, int? totalPhotos}) {
    return AlbumData(
      slug: slug ?? this.slug,
      text: text ?? this.text,
      totalPhotos: totalPhotos ?? this.totalPhotos,
    );
  }

  factory AlbumData.fromMap(Map<String, dynamic> data) {
    return AlbumData(
      slug: data['slug'],
      text: data['text'],
      totalPhotos: data['totalPhotos'],
    );
  }

  factory AlbumData.fromJson(String source) =>
      AlbumData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(slug, text, totalPhotos);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumData) return false;

    return other.slug == slug &&
        other.text == text &&
        other.totalPhotos == totalPhotos;
  }
}

@immutable
final class AlbumWithPhotosData {
  const AlbumWithPhotosData({
    this.slug = '',
    this.text = '',
    this.photos = const [],
  });

  final String slug;
  final String text;
  final List<PhotoData> photos;

  factory AlbumWithPhotosData.fromMap(Map<String, dynamic> data) {
    return AlbumWithPhotosData(
      slug: data['slug'],
      text: data['text'],
      photos: data['photos'],
    );
  }

  factory AlbumWithPhotosData.fromJson(String source) =>
      AlbumWithPhotosData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(slug, text, photos);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumWithPhotosData) return false;

    return other.slug == slug &&
        other.text == text &&
        listEquals(other.photos, photos);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';
import 'package:portraitmode/comment/http_responses/comment_response.dart';
import 'package:portraitmode/comment/providers/comment_activity_provider.dart';
import 'package:portraitmode/comment/services/comment_service.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';

class EditCommentUtil {
  final BuildContext context;
  final WidgetRef ref;
  final int photoId;
  final CommentData commentToEdit;
  final CommentData commentToSubmit;
  final GlobalKey targetKey;

  EditCommentUtil({
    required this.context,
    required this.ref,
    required this.photoId,
    required this.commentToEdit,
    required this.commentToSubmit,
    required this.targetKey,
  });

  Future<void> handleSubmitEvent() async {
    final bool isReply = commentToEdit.parentId != 0;

    final BuildContext? targetContext = targetKey.currentContext;

    if (targetContext != null) {
      await Scrollable.ensureVisible(
        targetContext,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
        alignment: 0.0,
      );
    }

    if (isReply) {
      final int? expandedCommentId = ref
          .read(commentActivityProvider)
          .expandedCommentId;

      /// Technically, the top-level parent has been expanded by the user.
      ///
      /// That means, we just need to expand the direct parent
      /// of the comment that user is replying to.
      if (expandedCommentId == null || commentToEdit.id != expandedCommentId) {
        ref
            .read(commentActivityProvider.notifier)
            .setExpandedCommentId(commentToEdit.parentId);
      }
    }

    final commentService = CommentService();
    final CommentResponse response = await commentService.edit(commentToSubmit);

    if (!response.success || response.data == null) {
      if (!response.success && context.mounted) {
        if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
          showSessionEndedDialog(context, ref);
        } else {
          showErrorDialog(context, ref, message: response.message);
        }
      }

      // If the submission fails, we won't restore the text back into the input field.
      // We also won't remove the comment from the list.
      //
      // Instead, we may display a failure indicator to let the user retry,
      // or leave the comment visible with a "submitting" status.
      //
      // todo: handle "retry" condition.

      return;
    }

    ref.read(commentActivityProvider.notifier).setFormMode(CommentFormMode.add);
    ref.read(commentActivityProvider.notifier).setCommentIdToEdit(null);

    if (isReply) {
      ref.read(commentActivityProvider.notifier).setCommentIdToReply(null);
    }
  }
}

void handleEditCommentTap({
  required WidgetRef ref,
  required CommentData comment,
  required TextEditingController commentFieldController,
  required FocusNode commentFieldFocusNode,
}) {
  ref.read(commentActivityProvider.notifier).setFormMode(CommentFormMode.edit);
  ref.read(commentActivityProvider.notifier).setCommentIdToEdit(comment.id);

  // If clicking a reply link of a reply (not the reply link of a top-level comment).
  if (comment.parentId != 0) {
    ref
        .read(commentActivityProvider.notifier)
        .setCommentIdToReply(comment.parentId);
  }

  commentFieldController.text = comment.content;

  commentFieldController.selection = TextSelection.fromPosition(
    TextPosition(offset: comment.content.length),
  );

  commentFieldFocusNode.requestFocus();
}

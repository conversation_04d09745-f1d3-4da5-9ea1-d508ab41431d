import 'package:flutter/material.dart';
import 'package:portraitmode/search/widgets/search_tabbar.dart';

class SearchTabbarDelegate extends SliverPersistentHeaderDelegate {
  SearchTabbarDelegate();

  @override
  Widget build(context, double shrinkOffset, bool overlapsContent) {
    return const SearchTabbar();
  }

  @override
  double get maxExtent => 45.0;

  @override
  double get minExtent => 45.0;

  @override
  bool shouldRebuild(SliverPersistentHeaderDelegate oldDelegate) => true;
}

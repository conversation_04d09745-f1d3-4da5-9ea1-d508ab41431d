import 'package:flutter/material.dart';
import 'package:photo_manager/photo_manager.dart';
import 'package:portraitmode/asset_picker/dto/plain_album_data.dart';
import 'package:portraitmode/asset_picker/widgets/asset_thumbnail_provider.dart';

class AlbumListView extends StatelessWidget {
  final List<AssetPathEntity> albums;
  final List<PlainAlbumData> plainAlbums;
  final Function(AssetPathEntity) onAlbumSelected;
  final int thumbnailSize;
  final int thumbnailQuality;

  const AlbumListView({
    super.key,
    required this.albums,
    required this.plainAlbums,
    required this.onAlbumSelected,
    required this.thumbnailSize,
    required this.thumbnailQuality,
  });

  @override
  Widget build(BuildContext context) {
    const coverSize = 60.0;

    return Container(
      color: Colors.black,
      padding: const EdgeInsets.symmetric(horizontal: 15.0),
      child: ListView.builder(
        itemCount: plainAlbums.length,
        itemBuilder: (context, index) {
          final plainAlbum = plainAlbums[index];

          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 6.0),
            child: GestureDetector(
              onTap: () => onAlbumSelected(albums[index]),
              child: Container(
                color: Colors.transparent,
                width: double.infinity,
                child: Row(
                  children: [
                    Container(
                      color: Colors.grey.withValues(alpha: 0.3),
                      width: coverSize,
                      height: coverSize,
                      child: plainAlbum.coverPhoto == null
                          ? const SizedBox.shrink()
                          : Image(
                              image: AssetThumbnailProvider(
                                entity: plainAlbum.coverPhoto!,
                                size: thumbnailSize,
                                quality: thumbnailQuality,
                              ),
                              fit: BoxFit.cover,
                            ),
                    ),
                    Expanded(
                      child: Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 12.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              plainAlbum.name,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                                fontSize: 13.0,
                              ),
                            ),
                            const SizedBox(height: 3.0),
                            Text(
                              '${plainAlbum.totalPhotos} photos',
                              style: const TextStyle(
                                color: Colors.grey,
                                fontWeight: FontWeight.w400,
                                fontSize: 11.0,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}

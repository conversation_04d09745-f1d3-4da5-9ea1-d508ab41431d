import 'package:flutter/material.dart';
import 'package:google_place/google_place.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';

class LocationPicker extends StatefulWidget {
  const LocationPicker({super.key, required this.onChanged});

  final Function(AutocompletePrediction prediction) onChanged;

  @override
  LocationPickerState createState() => LocationPickerState();
}

class LocationPickerState extends State<LocationPicker> {
  final _scrollController = ScrollController();
  final GooglePlace _googlePlace = GooglePlace(GoogleConfig.mapApiKey);

  List<AutocompletePrediction> predictions = [];
  final _searchFieldController = TextEditingController();

  @override
  void dispose() {
    _searchFieldController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: CustomScrollView(
          controller: _scrollController,
          slivers: [
            PmSliverAppBar(
              scrollController: _scrollController,
              titleText: "Select a location",
              useLogo: false,
              automaticallyImplyLeading: true,
              actions: const [],
            ),
            SliverToBoxAdapter(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16.0,
                  vertical: 8.0,
                ),
                child: PmTextField(
                  labelText: "Search...",
                  autofocus: true,
                  onChanged: (value) {
                    if (value.isNotEmpty) {
                      _search(value);
                    } else {
                      if (predictions.isNotEmpty && mounted) {
                        setState(() {
                          predictions = [];
                        });
                      }
                    }
                  },
                ),
              ),
            ),
            _buildSliverListView(),
            const SliverToBoxAdapter(child: SizedBox(height: 16.0)),
          ],
        ),
      ),
    );
  }

  Widget _buildSliverListView() {
    return SliverList(
      delegate: SliverChildBuilderDelegate((BuildContext context, int index) {
        return _buildSuggestionItem(index);
      }, childCount: predictions.length),
    );
  }

  void _search(String value) async {
    AutocompleteResponse? result = await _googlePlace.autocomplete.get(value);
    if (result == null) return;
    if (result.predictions == null) return;

    if (mounted) {
      setState(() {
        predictions = result.predictions ?? [];
      });
    }
  }

  Widget _buildSuggestionItem(int index) {
    AutocompletePrediction prediction = predictions[index];

    return ListTile(
      dense: true,
      minLeadingWidth: 20.0,
      leading: const Icon(
        Ionicons.location_outline,
        color: Colors.black,
        size: 20.0,
      ),
      title: Text(
        prediction.description ?? '',
        style: const TextStyle(fontSize: 14.0),
      ),
      onTap: () {
        widget.onChanged(prediction);
        Navigator.of(context).pop();
      },
    );
  }
}

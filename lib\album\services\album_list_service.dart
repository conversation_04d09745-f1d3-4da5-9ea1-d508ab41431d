// Extension packages.
import 'package:dio/dio.dart';
import 'package:portraitmode/base/base_service.dart';

// Internal packages.
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/album/http_responses/album_list_response.dart';

class AlbumListService extends BaseService {
  Future<AlbumListResponse> fetch({
    required int artistId,
    int limit = 100,
  }) async {
    try {
      final response = await http.get(
        '${URL.baseApiUrl}/artist/${artistId.toString()}/albums/${limit.toString()}',
      );

      return AlbumListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return AlbumListResponse.fromMap(e.response?.data);
      }

      return AlbumListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return AlbumListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

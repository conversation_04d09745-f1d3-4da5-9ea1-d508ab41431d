import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/appbar/pm_app_bar.dart';

class ArtistNotFound extends StatelessWidget {
  const ArtistNotFound({super.key, this.useBackButton = true});

  final bool useBackButton;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: PmAppBar(
        automaticallyImplyLeading: true,
        backgroundColor: context.colors.lightColor,
        titleText: 'Not found',
        useLogo: false,
        actions: const [],
      ),
      body: const Center(
        child: Text('Profile not found', style: TextStyle(fontSize: 16.0)),
      ),
    );
  }
}

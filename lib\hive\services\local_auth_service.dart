import 'package:hive_ce_flutter/hive_flutter.dart';
import 'package:portraitmode/hive/dto/local_auth_data.dart';

class LocalAuthService {
  static LocalAuthService? _instance;

  static LocalAuthService get instance =>
      _instance ??= LocalAuthService._internal();

  // Private constructor
  LocalAuthService._internal();

  // Cache boxes to avoid repeated box access
  static Box? _authBox;

  // Cache for current settings to avoid frequent Hive reads
  LocalAuthData? _cachedData;

  // Constants for keys to avoid string allocation
  static const _accessTokenKey = 'accessToken';
  static const _refreshTokenKey = 'refreshToken';

  // Lazy initialization of boxes
  Box get _authBoxInstance {
    return _authBox ??= Hive.box('auth');
  }

  LocalAuthData _get() {
    // Return cached data if available and still valid
    if (_cachedData != null) {
      return _cachedData!;
    }

    final accessTokenFromDb = _authBoxInstance.get(
      _accessTokenKey,
      defaultValue: null,
    );

    final accessToken = accessTokenFromDb is String ? accessTokenFromDb : null;

    final refreshTokenFromDb = _authBoxInstance.get(
      _refreshTokenKey,
      defaultValue: null,
    );

    final refreshToken = refreshTokenFromDb is String
        ? refreshTokenFromDb
        : null;

    _cachedData = LocalAuthData(
      isLoggedIn: accessToken != null && refreshToken != null,
      accessToken: accessToken,
      refreshToken: refreshToken,
    );

    return _cachedData!;
  }

  bool get _isLoggedIn => _get().isLoggedIn;

  String? get _accessToken => _get().accessToken;

  String? get _refreshToken => _get().refreshToken;

  Future<void> _setAccessToken(String? value) async {
    await _authBoxInstance.put(_accessTokenKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.accessToken = value;
  }

  Future<void> _setRefreshToken(String? value) async {
    await _authBoxInstance.put(_refreshTokenKey, value);

    if (_cachedData == null) {
      _get();
    }

    _cachedData!.refreshToken = value;
  }

  Future<LocalAuthData> _replace(LocalAuthData newData) async {
    _invalidateCache();

    await _authBoxInstance.putAll({
      _accessTokenKey: newData.accessToken,
      _refreshTokenKey: newData.refreshToken,
    });

    _cachedData = newData;

    return newData;
  }

  // Explicit cache invalidation method
  void _invalidateCache() {
    _cachedData = null;
  }

  Future<void> _destroy() async {
    await _authBoxInstance.deleteAll(_authBoxInstance.keys);
    _invalidateCache();
  }

  // Cleanup method to be called when app is terminated (just in case needed)
  void _dispose() {
    _cachedData = null;
    _authBox = null;
  }

  // --------------------------------------------------
  // Static convenience methods that delegate to singleton instance
  // --------------------------------------------------

  static LocalAuthData get() => instance._get();

  static bool get isLoggedIn => instance._isLoggedIn;

  static String? get accessToken => instance._accessToken;

  static String? get refreshToken => instance._refreshToken;

  static Future<void> setAccessToken(String? accessToken) =>
      instance._setAccessToken(accessToken);

  static Future<void> setRefreshToken(String? refreshToken) =>
      instance._setRefreshToken(refreshToken);

  static Future<LocalAuthData> replace(LocalAuthData newData) =>
      instance._replace(newData);

  static void invalidateCache() => instance._invalidateCache();

  static Future<void> destroy() => instance._destroy();

  static void dispose() => instance._dispose();
}

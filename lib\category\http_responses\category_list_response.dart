import 'package:portraitmode/category/dto/category_data.dart';

class CategoryListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<CategoryData> data;

  CategoryListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory CategoryListResponse.fromMap(Map<String, dynamic> map) {
    return CategoryListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<CategoryData>((data) => CategoryData.fromMap(data))
                .toList()
          : [],
    );
  }
}

import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/title_with_logo.dart';
import 'package:portraitmode/appbar/title_without_logo.dart';
import 'package:portraitmode/photo/widgets/photo_detail/profile_avatar.dart';

/// A performant SliverAppBar for PortraitMode that
/// will show a sharp border bottom when scrolled instead of a blurry shadow.
class PmSliverAppBar extends StatefulWidget {
  final bool primary;
  final bool automaticallyImplyLeading;
  final Widget? title;
  final Widget? leading;
  final List<Widget>? actions;
  final bool pinned;
  final bool floating;
  final bool snap;
  final double? expandedHeight;
  final Widget? flexibleSpace;
  final PreferredSizeWidget? bottom;
  final Color? backgroundColor;
  final Color? foregroundColor;
  final IconThemeData? iconTheme;
  final IconThemeData? actionsIconTheme;
  final bool useLogo;
  final String? titleText;
  final TextStyle? titleTextStyle;
  final bool centerTitle;
  final double borderWidth;
  final Color? borderColor;
  final ScrollController scrollController;

  const PmSliverAppBar({
    super.key,
    this.primary = true,
    this.automaticallyImplyLeading = false,
    this.title,
    this.leading,
    this.actions,
    this.pinned = true,
    this.floating = false,
    this.snap = false,
    this.expandedHeight,
    this.flexibleSpace,
    this.bottom,
    this.backgroundColor,
    this.foregroundColor,
    this.iconTheme,
    this.actionsIconTheme,
    this.useLogo = true,
    this.titleText,
    this.titleTextStyle,
    this.centerTitle = false,
    this.borderWidth = 0.8,
    this.borderColor,
    required this.scrollController,
  });

  @override
  State<PmSliverAppBar> createState() => _PmSliverAppBarState();
}

class _PmSliverAppBarState extends State<PmSliverAppBar> {
  final _isScrolledNotifier = ValueNotifier<bool>(false);

  /// We don't need to use debounce/throttle technique to do calculation here
  /// because this operation cost is cheap.
  /// Also, the widget will only updated if the value actually changes.
  ///
  /// Implementing scroll debounce/throttle will be over-engineered.
  void _onScroll() {
    final isScrolled =
        widget.scrollController.hasClients &&
        widget.scrollController.offset > 0;

    // ValueNotifier only triggers rebuild if value actually changes
    if (_isScrolledNotifier.value != isScrolled) {
      _isScrolledNotifier.value = isScrolled;
    }
  }

  @override
  void initState() {
    super.initState();
    widget.scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    widget.scrollController.removeListener(_onScroll);
    _isScrolledNotifier.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ValueListenableBuilder<bool>(
      valueListenable: _isScrolledNotifier,
      builder: (context, isScrolled, child) {
        return SliverAppBar(
          primary: widget.primary,
          leading: widget.leading,
          automaticallyImplyLeading: widget.automaticallyImplyLeading,
          title:
              widget.title ??
              (widget.useLogo || widget.titleText != null
                  ? _buildTitle()
                  : null),
          titleSpacing: widget.automaticallyImplyLeading
              ? 0.0
              : ScreenStyleConfig.horizontalPadding,
          titleTextStyle: widget.titleTextStyle,
          centerTitle: widget.centerTitle,
          actions:
              widget.actions ??
              [
                Padding(
                  padding: const EdgeInsets.only(
                    right: ScreenStyleConfig.horizontalPadding,
                  ),
                  child: ProfileAvatar(size: 32.0, toProfileScreen: true),
                ),
              ],
          pinned: widget.pinned,
          floating: widget.floating,
          snap: widget.snap,
          expandedHeight: widget.expandedHeight,
          flexibleSpace: widget.flexibleSpace,
          backgroundColor: widget.backgroundColor,
          foregroundColor: widget.foregroundColor,
          iconTheme: widget.iconTheme,
          actionsIconTheme: widget.actionsIconTheme,
          bottom:
              widget.bottom ??
              (isScrolled
                  ? PreferredSize(
                      preferredSize: Size.fromHeight(0.8),
                      child: Container(
                        height: 0.8,
                        color: context.colors.borderColor,
                      ),
                    )
                  : null),
        );
      },
    );
  }

  Widget _buildTitle() {
    return SizedBox(
      width: double.infinity,
      child: widget.useLogo
          ? TitleWithLogo(title: widget.titleText)
          : TitleWithoutLogo(title: widget.titleText ?? ''),
    );
  }
}

import 'package:collection/collection.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/enum.dart';

final class CommentStoreNotifier extends Notifier<List<CommentData>> {
  final Map<int, int> _idIndexCache = {};

  @override
  List<CommentData> build() => [];

  void _updateState(List<CommentData> newState) {
    state = newState;
    _rebuildIndexCache();
  }

  void _rebuildIndexCache() {
    _idIndexCache.clear();
    for (var i = 0; i < state.length; i++) {
      _idIndexCache[state[i].id] = i;
    }
  }

  CommentData? getItem(int id) {
    final index = _idIndexCache[id];
    if (index == null) return null;
    return state[index];
  }

  int getIndex(int id) {
    return _idIndexCache[id] ?? -1;
  }

  bool hasItem(int id) {
    return _idIndexCache.containsKey(id);
  }

  /// Uniquely adds a new item.
  /// For photo_store_provider, the order doesn't matter.
  void addItem(
    CommentData newItem, {
    bool updateIfExists = true,
    bool updateParentTotalReplies = false,
  }) {
    if (hasItem(newItem.id)) {
      // Call updateItem instead of manually writing the update code here.
      if (updateIfExists) updateItem(newItem);
      return;
    }

    final int parentId = newItem.parentId;

    final updated = [...state, newItem];

    if (parentId != 0 &&
        updateParentTotalReplies &&
        newItem.submissionStatus == CommentSubmissionStatus.submitted) {
      final parentIndex = _idIndexCache[parentId];

      // Update parent's totalReplies.
      if (parentIndex != null) {
        updated[parentIndex] = updated[parentIndex].copyWith(
          totalReplies: updated[parentIndex].totalReplies + 1,
        );
      }
    }

    _updateState(updated);
  }

  /// Uniquely adds a list of new items.
  /// For photo_store_provider, the order doesn't matter.
  ///
  /// Don't utilize `addItem` method to avoid unnecessary checking.
  void addItems(
    List<CommentData> newItems, {
    bool updateIfExists = true,
    bool updateParentTotalReplies = false,
  }) {
    final existingIds = _idIndexCache.keys.toSet();

    List<CommentData> parsedItems = [...state];

    if (updateIfExists) {
      final existingItemsToUpdate = newItems
          .where((item) => existingIds.contains(item.id))
          .toList();

      if (existingItemsToUpdate.isNotEmpty) {
        for (final item in existingItemsToUpdate) {
          final index = _idIndexCache[item.id]!;
          parsedItems[index] = item;
        }
      }
    }

    if (updateParentTotalReplies) {
      final newItemsToAdd = newItems
          .where((item) => !existingIds.contains(item.id))
          .toList();

      if (newItemsToAdd.isNotEmpty) {
        parsedItems.addAll(newItemsToAdd);

        for (final item in newItemsToAdd) {
          final parentId = item.parentId;
          final parentIndex = _idIndexCache[parentId];

          // Update parent's totalReplies.
          if (parentIndex != null &&
              item.submissionStatus == CommentSubmissionStatus.submitted) {
            parsedItems[parentIndex] = parsedItems[parentIndex].copyWith(
              totalReplies: parsedItems[parentIndex].totalReplies + 1,
            );
          }
        }
      }
    }

    _updateState(parsedItems);
  }

  /// Updates an existing item.
  void updateItem(CommentData newItem, {bool addIfNotExists = true}) {
    final index = _idIndexCache[newItem.id];

    if (index == null) {
      if (addIfNotExists) {
        // We don't update directly here, we will call addItem instead.
        // This is because we need to also update its parent's totalReplies.
        addItem(newItem, updateIfExists: false);
      }

      return;
    }

    // If the item is the same, do nothing.
    if (state[index] == newItem) return;

    final updated = [...state];
    updated[index] = newItem;
    _updateState(updated);
  }

  /// Updates a list of existing items.
  ///
  /// Doesn't utilize `updateItem` method to avoid unnecessary checking.
  void updateItems(List<CommentData> newItems, {bool addIfNotExists = true}) {
    final existingIds = _idIndexCache.keys.toSet();

    List<CommentData> parsedItems = [...state];

    final existingItemsToUpdate = newItems
        .where((item) => existingIds.contains(item.id))
        .toList();

    if (existingItemsToUpdate.isNotEmpty) {
      for (final item in existingItemsToUpdate) {
        final index = _idIndexCache[item.id]!;
        parsedItems[index] = item;
      }
    }

    if (addIfNotExists) {
      final newItemsToAdd = newItems
          .where((item) => !existingIds.contains(item.id))
          .toList();

      if (newItemsToAdd.isNotEmpty) {
        parsedItems.addAll(newItemsToAdd);

        for (final item in newItemsToAdd) {
          final parentId = item.parentId;
          final parentIndex = _idIndexCache[parentId];

          // Update parent's totalReplies.
          if (parentIndex != null &&
              item.submissionStatus == CommentSubmissionStatus.submitted) {
            parsedItems[parentIndex] = parsedItems[parentIndex].copyWith(
              totalReplies: parsedItems[parentIndex].totalReplies + 1,
            );
          }
        }
      }
    }

    _updateState(parsedItems);
  }

  void removeItem(int id, {bool updateParentTotalReplies = false}) {
    if (!_idIndexCache.containsKey(id)) return;
    final currentItem = getItem(id);

    List<CommentData> parsed = [...state];

    if (currentItem != null &&
        updateParentTotalReplies &&
        currentItem.parentId != 0 &&
        currentItem.submissionStatus == CommentSubmissionStatus.submitted) {
      final parentIndex = _idIndexCache[currentItem.parentId];

      // Update parent's totalReplies.
      if (parentIndex != null) {
        final int updatedTotalReplies = parsed[parentIndex].totalReplies - 1;

        parsed[parentIndex] = parsed[parentIndex].copyWith(
          totalReplies: updatedTotalReplies < 0 ? 0 : updatedTotalReplies,
        );
      }
    }

    parsed = parsed.where((item) => item.id != id).toList();

    _updateState(parsed);
  }

  void removeByAuthorId(int authorId, {bool updateParentTotalReplies = false}) {
    final parsed = [...state];

    final itemsToRemove = parsed
        .where((item) => item.authorId == authorId)
        .toList();

    // Loop itemsToRemove and update their parent's totalReplies.
    for (final item in itemsToRemove) {
      final parentIndex = _idIndexCache[item.parentId];

      // Update parent's totalReplies.
      if (parentIndex != null &&
          updateParentTotalReplies &&
          item.submissionStatus == CommentSubmissionStatus.submitted) {
        parsed[parentIndex] = parsed[parentIndex].copyWith(
          totalReplies: parsed[parentIndex].totalReplies - 1,
        );
      }

      // Remove the actual item from `parsed`.
      parsed.removeWhere((item) => item.id == item.id);
    }

    _updateState(parsed);
  }

  void removeByPhotoId(int photoId) {
    // We don't need to update their parent's totalReplies because
    // their parents are also part of the photo that is going to be deleted.
    final parsed = state.where((item) => item.postId != photoId).toList();

    _updateState(parsed);
  }

  void replaceAll(List<CommentData> newList) {
    _updateState(newList);
  }

  void clear() {
    _updateState([]);
  }

  // ------------------------------------------------------------
  // Additional methods tailored for comment_store_provider
  // ------------------------------------------------------------

  void setSubmissionStatus(
    int commentId,
    CommentSubmissionStatus submissionStatus,
  ) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];

    updated[index] = updated[index].copyWith(
      submissionStatus: submissionStatus,
    );

    _updateState(updated);
  }

  void setIsLiked(int commentId, bool isLiked) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(isLiked: isLiked);
    _updateState(updated);
  }

  void setTotalLikes(int commentId, int newTotalLikes) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(totalLikes: newTotalLikes);
    _updateState(updated);
  }

  void incrementTotalLikes(int commentId) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(
      totalLikes: updated[index].totalLikes + 1,
    );
    _updateState(updated);
  }

  void decrementTotalLikes(int commentId) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];
    final int updatedTotalLikes = updated[index].totalLikes - 1;

    updated[index] = updated[index].copyWith(
      totalLikes: updatedTotalLikes < 0 ? 0 : updatedTotalLikes,
    );

    _updateState(updated);
  }

  void setTotalReplies(int commentId, int newTotalReplies) {
    final index = _idIndexCache[commentId];
    if (index == null) return;

    final updated = [...state];
    updated[index] = updated[index].copyWith(totalReplies: newTotalReplies);
    _updateState(updated);
  }

  CommentData? getTopLevelParent(int commentId) {
    CommentData? current = getItem(commentId);

    while (current != null && current.parentId != 0) {
      current = getItem(current.parentId);
    }

    return current;
  }
}

final commentStoreProvider =
    NotifierProvider.autoDispose<CommentStoreNotifier, List<CommentData>>(
      CommentStoreNotifier.new,
    );

final commentProvider = Provider.family
    .autoDispose<({bool? isLiked, int? totalLikes, int? totalReplies}), int>((
      ref,
      commentId,
    ) {
      final comments = ref.watch(commentStoreProvider);
      final comment = comments.firstWhereOrNull((a) => a.id == commentId);

      return (
        isLiked: comment?.isLiked,
        totalLikes: comment?.totalLikes,
        totalReplies: comment?.totalReplies,
      );
    });

import 'package:dio/dio.dart';

import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/camera/http_responses/camera_list_response.dart';

class CameraListService extends BaseService {
  /// Fetch list of cameras.
  ///
  /// We will exclude cameras with photos less than 3.
  /// Currently, there's no straight-forward / "out of the box" way to do this.
  ///
  /// That's why, in the camera query, we will exclude cameras with photos less than 3
  /// which means, the photos returned could be less than `limit`.
  ///
  /// To prevent a very low number of photos returned, we will set the `limit` to 50.
  Future<CameraListResponse> fetch({
    int limit = 50,
    int? lastId,
    String? keyword,
    String? order,
    String? orderBy,
  }) async {
    try {
      String url = URL.baseApiUrl;

      if (keyword != null && keyword.isNotEmpty) {
        url += '/search-cameras/$keyword';
      } else {
        url += '/cameras';
      }

      url += '/$limit/${lastId ?? -1}';

      final response = await http.get(url);

      return CameraListResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return CameraListResponse.fromMap(e.response?.data);
      }

      return CameraListResponse(
        success: false,
        message: getDioExceptionMsg(e: e),
      );
    } catch (e) {
      return CameraListResponse(
        success: false,
        message: "Something went wrong.",
      );
    }
  }
}

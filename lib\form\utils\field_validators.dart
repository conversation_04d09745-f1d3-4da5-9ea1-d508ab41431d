import 'package:form_field_validator/form_field_validator.dart';
import 'package:portraitmode/form/utils/custom_validators.dart';

class FieldValidators {
  static final resetCodeValidator = MultiValidator([
    RequiredValidator(errorText: 'Reset code is required'),
    FieldLengthValidator(
      length: 6,
      errorText: 'Error code length must be 6 chars',
    ),
  ]);

  static final usernameValidator = MultiValidator([
    RequiredValidator(errorText: 'Username is required'),
    MinLengthValidator(
      4,
      errorText: 'Username too short. At least 4 chars required.',
    ),
  ]);

  static final emailValidator = MultiValidator([
    RequiredValidator(errorText: 'Email is required'),
    EmailValidator(errorText: 'Enter a valid email address'),
  ]);

  static final passwordValidator = MultiValidator([
    RequiredValidator(errorText: 'Password is required'),
    MinLengthValidator(7, errorText: 'Password length must be greater than 6'),
  ]);

  static final firstNameValidator = MultiValidator([
    RequiredValidator(errorText: 'First name is required'),
    MinLengthValidator(
      3,
      errorText: 'First name too short. At least 3 characters are required.',
    ),
  ]);

  static final lastNameValidator = MultiValidator([
    RequiredValidator(errorText: 'Last name is required'),
    MinLengthValidator(
      3,
      errorText: 'Last name too short. At least 3 characters are required.',
    ),
  ]);

  static final locationValidator = MultiValidator([
    RequiredValidator(errorText: 'Location is required'),
    MinLengthValidator(
      3,
      errorText: 'Location too short. At least 3 characters are required.',
    ),
  ]);

  static final iOSlocationValidator = MultiValidator([
    MinLengthValidator(
      3,
      errorText: 'Location too short. At least 3 characters are required.',
    ),
  ]);

  static final photoDescriptionValidator = MultiValidator([
    MinWordsLengthValidator(
      length: 5,
      errorText:
          'Description is too short. If provided, the description must be at least 5 words.',
    ),
  ]);

  static final instagramValidator = MultiValidator([
    StartWithValidator('@', errorText: "Instagram handle must start with @"),
  ]);
}

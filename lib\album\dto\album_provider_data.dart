import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/album/dto/album_data.dart';

@immutable
class AlbumProviderData {
  const AlbumProviderData({this.artistId = 0, this.albums = const []});

  final int artistId;
  final List<AlbumData>? albums;

  factory AlbumProviderData.fromMap(Map<String, dynamic> data) {
    return AlbumProviderData(
      artistId: data['artistId'],
      albums: data['albums'] ?? [],
    );
  }

  factory AlbumProviderData.fromJson(String source) =>
      AlbumProviderData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(artistId, albums);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumProviderData) return false;

    return other.artistId == artistId && listEquals(other.albums, albums);
  }
}

@immutable
class AlbumWithPhotosProviderData {
  const AlbumWithPhotosProviderData({
    this.artistId = 0,
    this.albums = const [],
  });

  final int artistId;
  final List<AlbumWithPhotosData>? albums;

  factory AlbumWithPhotosProviderData.fromMap(Map<String, dynamic> data) {
    return AlbumWithPhotosProviderData(
      artistId: data['artistId'],
      albums: data['albums'] ?? [],
    );
  }

  factory AlbumWithPhotosProviderData.fromJson(String source) =>
      AlbumWithPhotosProviderData.fromMap(json.decode(source));

  @override
  int get hashCode => Object.hash(artistId, albums);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! AlbumWithPhotosProviderData) return false;

    return other.artistId == artistId && listEquals(other.albums, albums);
  }
}

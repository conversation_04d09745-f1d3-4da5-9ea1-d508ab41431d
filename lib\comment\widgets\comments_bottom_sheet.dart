import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/comment/http_responses/comment_list_response.dart';
import 'package:portraitmode/comment/providers/comment_list_provider.dart';
import 'package:portraitmode/comment/services/comment_list_service.dart';
import 'package:portraitmode/comment/utils/delete_comment_util.dart';
import 'package:portraitmode/comment/utils/edit_comment_util.dart';
import 'package:portraitmode/comment/widgets/comment_form.dart';
import 'package:portraitmode/comment/widgets/comment_list_item.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/load_more_builder.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

class CommentsBottomSheet extends ConsumerStatefulWidget {
  final PhotoData photo;

  const CommentsBottomSheet({super.key, required this.photo});

  @override
  CommentsBottomSheetState createState() => CommentsBottomSheetState();
}

class CommentsBottomSheetState extends ConsumerState<CommentsBottomSheet> {
  final _commentListService = CommentListService();
  final _commentFieldController = TextEditingController();
  final _focusNode = FocusNode();
  final _bottomSheetController = DraggableScrollableController();
  final _photoDescriptionKey = GlobalKey();

  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;
  int _pageIndex = 0;
  bool _loadMoreEndReached = false;

  final Map<int, GlobalKey> _activeCommentKeys = {};
  int? _activeCommentId;

  late int _profileId;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_handleFocusChange);
    _profileId = LocalUserService.userId ?? 0;
  }

  void _handleFocusChange() {
    if (_focusNode.hasFocus) {
      log('Field is focused, scroll the DraggableScrollableSheet up');
      // Field is focused, scroll the DraggableScrollableSheet up
      _bottomSheetController.animateTo(
        0.9,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  @override
  void dispose() {
    _commentFieldController.dispose();
    _focusNode.removeListener(_handleFocusChange);
    _bottomSheetController.dispose();
    _focusNode.dispose();
    _activeCommentKeys.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      controller: _bottomSheetController,
      maxChildSize: 0.9,
      initialChildSize: 0.6,
      expand: false,
      builder: (BuildContext context, ScrollController scrollController) {
        return Container(
          padding: EdgeInsets.only(
            top: BottomSheetConfig.topSpace,
            bottom: MediaQuery.viewInsetsOf(context).bottom,
          ),
          decoration: BoxDecoration(
            color: context.colors.scaffoldColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(BottomSheetConfig.borderRadius),
              topRight: Radius.circular(BottomSheetConfig.borderRadius),
            ),
          ),
          child: SafeArea(
            child: Column(
              children: [
                const SingleChildScrollView(
                  // controller: scrollController,
                  child: Column(
                    children: [
                      ModalDragHandle(),
                      Padding(
                        padding: EdgeInsets.only(top: 12.0, bottom: 16.0),
                        child: Text(
                          "Comments",
                          style: TextStyle(
                            fontSize: 15.0,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                      Divider(height: 1.5),
                    ],
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    controller: scrollController,
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Column(
                      children: [
                        Builder(builder: (context) => _buildCommentList()),
                      ],
                    ),
                  ),
                ),
                CommentForm(
                  photoId: widget.photo.id,
                  fieldController: _commentFieldController,
                  focusNode: _focusNode,
                  photoDescriptionKey: _photoDescriptionKey,
                  activeCommentKey: _activeCommentKeys[_activeCommentId],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCommentList() {
    final List<CommentData> commentList = ref.watch(
      commentListProvider(widget.photo.id),
    );

    // log('loadMoreEndReached: "$_loadMoreEndReached" and commentList.length: "${commentList.length}"');

    if (commentList.isEmpty) {
      return LoadMoreBuilder(
        isFinished: _loadMoreEndReached ? true : false,
        onLoadMore: _handleLoadMore,
        loadingWidgetColor: context.colors.baseColorAlt,
        idleStatusText: "",
        loadingStatusText: "",
        finishedStatusText: 'No comments found',
        isLastIndex: true,
        child: const SizedBox.shrink(),
      );
    }

    return Column(
      children: commentList.map((comment) {
        final int index = commentList.indexOf(comment);
        final bool isLastIndex = index == (commentList.length - 1);
        double marginBottom = isLastIndex ? 0 : 12.0;

        return LoadMoreBuilder(
          isFinished: _loadMoreEndReached,
          onLoadMore: _handleLoadMore,
          loadingWidgetColor: context.colors.baseColorAlt,
          idleStatusText: "",
          loadingStatusText: "",
          finishedStatusText: commentList.isEmpty ? 'No comments found' : '',
          isLastIndex: isLastIndex,
          child: _buildCommentListItem(comment, index, marginBottom),
        );
      }).toList(),
    );
  }

  Widget _buildCommentListItem(
    CommentData comment,
    int index,
    double marginBottom,
  ) {
    final bool isOwnComment = comment.authorId == _profileId;

    return Container(
      margin: EdgeInsets.only(bottom: marginBottom),
      child: CommentListItem(
        commentFieldController: _commentFieldController,
        commentFieldFocusNode: _focusNode,
        comment: comment,
        isOwnComment: isOwnComment,
        onEditCommentTap: isOwnComment
            ? () {
                setState(() {
                  _activeCommentId = comment.id;
                  _activeCommentKeys[comment.id] = GlobalKey();
                });

                handleEditCommentTap(
                  ref: ref,
                  comment: comment,
                  commentFieldController: _commentFieldController,
                  commentFieldFocusNode: _focusNode,
                );
              }
            : null,
        onDeleteCommentTap: isOwnComment
            ? () async {
                final activeCommentKey = _activeCommentKeys[comment.id];

                if (activeCommentKey == null) {
                  return;
                }

                await DeleteCommentUtil(
                  context: context,
                  ref: ref,
                  photoId: widget.photo.id,
                  commentToDelete: comment,
                  targetKey: activeCommentKey,
                ).handleDeleteEvent();
              }
            : null,
        onCommentReported: isOwnComment ? null : _handleCommentReported,
      ),
    );
  }

  Future<bool> _handleLoadMore() async {
    CommentListResponse response = await _commentListService.fetch(
      postId: widget.photo.id,
      limit: _loadMorePerPage,
      offset: _pageIndex * _loadMorePerPage,
    );

    return _handleCommentListResponse(response);
  }

  bool _handleCommentListResponse(CommentListResponse response) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return false;
    }

    if (response.data.isEmpty) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }

      return true;
    }

    _pageIndex++;

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    final photoCommentService = ref.read(photoCommentServiceProvider);

    photoCommentService.prependComments(widget.photo.id, response.data);

    if (response.data.length < _loadMorePerPage) {
      if (mounted) {
        setState(() {
          _loadMoreEndReached = true;
        });
      }
    }

    return true;
  }

  void _handleCommentReported(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}

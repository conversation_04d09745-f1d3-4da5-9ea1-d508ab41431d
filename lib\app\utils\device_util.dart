import 'dart:io';

import 'package:device_info_plus/device_info_plus.dart';
import 'package:portraitmode/app/utils/content_util.dart';

Future<String> getDeviceModel() async {
  final deviceInfoPlugin = DeviceInfoPlugin();

  if (Platform.isAndroid) {
    final androidInfo = await deviceInfoPlugin.androidInfo;
    return androidInfo.model;
  }

  if (Platform.isIOS) {
    final iosInfo = await deviceInfoPlugin.iosInfo;
    return iosInfo.utsname.machine;
  }

  return 'mobile-app';
}

Future<String> getDeviceModelSlug() async {
  final deviceModel = await getDeviceModel();
  return slugify(deviceModel);
}

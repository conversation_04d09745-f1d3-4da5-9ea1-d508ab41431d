import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/modals/modal_drag_handle.dart';
import 'package:portraitmode/system_status/dto/system_status_history_data.dart';

class LatestIssueModal extends StatelessWidget {
  final ScrollController scrollController;
  final SystemStatusHistoryData issue;

  const LatestIssueModal({
    super.key,
    required this.scrollController,
    required this.issue,
  });

  @override
  Widget build(BuildContext context) {
    final Color resolvedStatusColor = issue.resolved
        ? context.colors.successColor
        : context.colors.warningColor;

    return Container(
      padding: EdgeInsets.only(
        top: 8.0,
        bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: Container(
          padding: EdgeInsets.only(
            left: ScreenStyleConfig.horizontalPadding,
            right: ScreenStyleConfig.horizontalPadding,
            bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
          ),
          decoration: BoxDecoration(
            color: context.colors.scaffoldColor,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(20.0),
              topRight: Radius.circular(20.0),
            ),
          ),
          child: ListView(
            controller: scrollController,
            children: [
              const SizedBox(height: 10.0, child: ModalDragHandle()),
              const SizedBox(height: 10.0),
              Text(
                issue.headline,
                style: const TextStyle(
                  fontSize: 18.0,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 10.0),
              HtmlWidget(issue.description),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(height: 20.0),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8.0,
                      horizontal: 14.0,
                    ),
                    // Use resolvedStatusColor.withOpacity(0.1) for bg, and resolvedStatusColor for the text and border color.
                    decoration: BoxDecoration(
                      color: resolvedStatusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(5.0),
                      border: Border.all(
                        color: resolvedStatusColor,
                        width: 1.0,
                      ),
                    ),
                    child: Text(
                      issue.resolved
                          ? 'This issue has been resolved'
                          : 'This issue is still ongoing',
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: resolvedStatusColor,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';

@immutable
class PhotoListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<PhotoData> data;

  const PhotoListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory PhotoListResponse.fromMap(Map<String, dynamic> map) {
    return PhotoListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<PhotoData>((data) => PhotoData.fromMap(data))
                .toList()
          : [],
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is PhotoListResponse &&
          runtimeType == other.runtimeType &&
          success == other.success &&
          errorCode == other.errorCode &&
          message == other.message &&
          listEquals(data, other.data);

  @override
  int get hashCode => Object.hash(success, errorCode, message, data);

  @override
  String toString() {
    return '''
PhotoListResponse{
  success: $success,
  errorCode: "$errorCode",
  message: "$message",
  data: $data,
}
''';
  }
}

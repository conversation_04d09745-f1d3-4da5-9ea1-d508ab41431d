import 'package:dio/dio.dart';
import 'package:portraitmode/app/config/url.dart';
import 'package:portraitmode/base/base_response.dart';
import 'package:portraitmode/base/base_service.dart';
import 'package:portraitmode/common/enum.dart';

class PhotoArchiveService extends BaseService {
  Future<BaseResponse> handleAssignment({
    required int photoId,
    required AssignmentActionType actionType,
  }) async {
    try {
      final response = await http.put(
        '${URL.baseApiUrl}/photo-archive-assignment/$photoId',
        data: {'action_type': actionType.text},
      );

      return BaseResponse.fromMap(response.data);
    } on DioException catch (e) {
      if (errorHasData(e)) {
        return BaseResponse.fromMap(e.response?.data);
      }

      return BaseResponse(success: false, message: getDioExceptionMsg(e: e));
    } catch (e) {
      return BaseResponse(success: false, message: "Something went wrong.");
    }
  }
}

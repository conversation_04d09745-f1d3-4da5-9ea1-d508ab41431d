import 'package:portraitmode/app/config/emoji.dart';

String parseEmojiChars(String content) {
  // Create a regex pattern that matches any of the smiley sequences in our map
  final pattern = RegExp(
    r'[:;><8BTO3o\^\\/\$\-)(|*]+',
  ); // This captures most of the potential sequences

  // Use the regex to replace matches with their corresponding emoji
  return content.replaceAllMapped(pattern, (Match match) {
    final matchedString = match[0]!;
    // If the matched string exists in our map, replace it with the corresponding emoji
    // Otherwise, return the matched string as is
    return globalEmojiMap[matchedString] ?? matchedString;
  });
}

String slugify(String content) {
  return content
      .toLowerCase()
      .replaceAll(RegExp(r'[^a-z0-9\s-]'), '')
      .replaceAll(RegExp(r'\s+'), '-')
      .replaceAll(RegExp(r'-+'), '-');
}

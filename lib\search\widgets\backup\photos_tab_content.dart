import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/load_more/enum.dart';
import 'package:portraitmode/load_more/masonry_load_more.dart';
import 'package:portraitmode/photo/dto/photo_data.dart';
import 'package:portraitmode/photo/http_responses/photo_list_response.dart';
import 'package:portraitmode/photo/providers/photo_store_provider.dart';
import 'package:portraitmode/photo/services/photo_list_service.dart';
import 'package:portraitmode/photo/utils/photo_util.dart';
import 'package:portraitmode/photo/widgets/masonry/photo_masonry_item.dart';
import 'package:portraitmode/photo/widgets/photo_detail_screen.dart';
import 'package:portraitmode/search/dto/search_data.dart';
import 'package:portraitmode/search/providers/search_photos_data_provider.dart';
import 'package:portraitmode/search/providers/search_photos_provider.dart';

class PhotosTabContent extends ConsumerStatefulWidget {
  const PhotosTabContent({
    super.key,
    required this.searchData,
    required this.keyword,
    required this.dataList,
    this.onPhotoItemTwoFingersOn,
    this.onPhotoItemTwoFingersOff,
  });

  final PhotosSearchData searchData;
  final String keyword;
  final List<PhotoData> dataList;
  final Function? onPhotoItemTwoFingersOn;
  final Function? onPhotoItemTwoFingersOff;

  @override
  PhotosTabContentState createState() => PhotosTabContentState();
}

class PhotosTabContentState extends ConsumerState<PhotosTabContent> {
  final GlobalKey<RefreshIndicatorState> _refreshIndicatorKey =
      GlobalKey<RefreshIndicatorState>();
  late final int _profileId;

  final _photoListService = PhotoListService();
  final int _loadMorePerPage = LoadMoreConfig.photosPerPage;

  @override
  void initState() {
    _profileId = LocalUserService.userId ?? 0;
    super.initState();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Container(
          constraints: const BoxConstraints(maxWidth: 768.0),
          child: RefreshIndicator(
            key: _refreshIndicatorKey,
            onRefresh: _handleRefresh,
            child: MasonryLoadMore(
              isFinished: widget.searchData.loadMoreEndReached,
              onLoadMore: _handleLoadMore,
              loadingWidgetColor: context.colors.baseColorAlt,
              runOnEmptyResult: true,
              loadingStatusText: "",
              finishedStatusText: "",
              padding: const EdgeInsets.symmetric(
                vertical: 8.0,
                horizontal: 8.0,
              ),
              crossAxisCount: 2,
              mainAxisSpacing: 8.0,
              crossAxisSpacing: 8.0,
              itemsCount: widget.dataList.length,
              itemBuilder: (BuildContext masonryContext, int index) {
                if (index >= widget.dataList.length) {
                  return const SizedBox.shrink();
                }

                return PhotoMasonryItem(
                  index: index,
                  photo: widget.dataList[index],
                  isOwnProfile: widget.dataList[index].authorId == _profileId,
                  screenName: 'photo_search_screen',
                  onZoomStart: () {
                    if (!mounted) return;

                    if (widget.onPhotoItemTwoFingersOn != null) {
                      widget.onPhotoItemTwoFingersOn!();
                    }
                  },
                  onZoomEnd: () {
                    if (!mounted) return;

                    if (widget.onPhotoItemTwoFingersOff != null) {
                      widget.onPhotoItemTwoFingersOff!();
                    }
                  },
                  onPhotoTap: () => _handlePhotoTap(widget.dataList[index]),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  void _handlePhotoTap(PhotoData photo) {
    FocusScope.of(context).unfocus();

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PhotoDetailScreen(
          photo: photo,
          originScreenName: 'photo_search_screen',
        ),
      ),
    );
  }

  Future<void> _handleRefresh() async {
    // log('The photos search keyword onRefresh is: "${widget.keyword}"');

    final PhotoListResponse response = widget.keyword.isNotEmpty
        ? await _photoListService.search(
            keyword: widget.keyword,
            limit: _loadMorePerPage,
            lastId: 0,
          )
        : await _photoListService.fetch(limit: _loadMorePerPage, lastId: 0);

    _handlePhotosResponse(response, true, false);
  }

  Future<LoadMoreResult> _handleLoadMore() async {
    // log('The photos search keyword is: "${widget.keyword}"');

    final PhotoListResponse response = widget.keyword.isNotEmpty
        ? await _photoListService.search(
            keyword: widget.keyword,
            limit: _loadMorePerPage,
            lastId: widget.searchData.loadMoreLastId,
          )
        : await _photoListService.fetch(
            limit: _loadMorePerPage,
            lastId: widget.searchData.loadMoreLastId,
          );

    final isFirstLoad = widget.searchData.loadMoreLastId == 0;

    _handlePhotosResponse(response, false, isFirstLoad);

    if (!response.success) {
      return LoadMoreResult.failed;
    }

    if (response.data.isEmpty || response.data.length < _loadMorePerPage) {
      return LoadMoreResult.finished;
    }

    return LoadMoreResult.success;
  }

  void _handlePhotosResponse(
    PhotoListResponse response,
    bool isRefresh,
    bool isFirstLoad,
  ) {
    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        showSessionEndedDialog(context, ref);
      } else {
        showErrorDialog(context, ref, message: response.message);
      }

      return;
    }

    if (response.data.isEmpty) {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
      return;
    }

    ref
        .read(searchPhotosDataProvider.notifier)
        .setLoadMoreLastId(response.data.last.id);

    ref
        .read(photoStoreProvider.notifier)
        .updateItems(response.data, addIfNotExists: true);

    // Sort the array before saving it (last comes first).
    response.data.sort((a, b) => b.id.compareTo(a.id));

    if (isRefresh) {
      ref
          .read(searchPhotoIdsProvider.notifier)
          .replaceAll(photoListToIdList(response.data));
    } else {
      if (isFirstLoad) {
        ref
            .read(searchPhotoIdsProvider.notifier)
            .replaceAll(photoListToIdList(response.data));
      } else {
        ref
            .read(searchPhotoIdsProvider.notifier)
            .addItems(photoListToIdList(response.data));
      }
    }

    if (response.data.length < _loadMorePerPage) {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(true);
    } else {
      ref.read(searchPhotosDataProvider.notifier).setLoadMoreEndReached(false);
    }
  }
}

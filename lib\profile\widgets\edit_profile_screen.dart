import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:google_place/google_place.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/appbar/pm_sliver_app_bar.dart';
import 'package:portraitmode/artist/dto/artist_partial_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail_screen.dart';
import 'package:portraitmode/auth/utils/auth_util.dart';
import 'package:portraitmode/dialogs/error_dialog.dart';
import 'package:portraitmode/form/fields/pm_text_field.dart';
import 'package:portraitmode/form/submit_button.dart';
import 'package:portraitmode/form/utils/field_validators.dart';
import 'package:portraitmode/hive/dto/local_user_data.dart';
import 'package:portraitmode/hive/services/local_user_service.dart';
import 'package:portraitmode/location_picker/location_picker.dart';
import 'package:portraitmode/profile/dto/profile_data.dart';
import 'package:portraitmode/profile/http_responses/profile_http_response.dart';
import 'package:portraitmode/profile/providers/profile_provider.dart';
import 'package:portraitmode/profile/services/profile_service.dart';
import 'package:portraitmode/profile/widgets/avatar_picker.dart';

class EditProfileScreen extends ConsumerStatefulWidget {
  const EditProfileScreen({super.key});

  @override
  EditProfileScreenState createState() => EditProfileScreenState();
}

class EditProfileScreenState extends ConsumerState<EditProfileScreen> {
  final _scrollController = ScrollController();
  final _formKey = GlobalKey<FormState>();
  final GooglePlace _googlePlace = GooglePlace(GoogleConfig.mapApiKey);

  final _nicenameFieldController = TextEditingController();
  final _instagramFieldController = TextEditingController();
  final _firstNameFieldController = TextEditingController();
  final _lastNameFieldController = TextEditingController();
  final _emailFieldController = TextEditingController();
  final _locationFieldController = TextEditingController();
  final _websiteFieldController = TextEditingController();
  final _cameraFieldController = TextEditingController();
  final _focalLengthFieldController = TextEditingController();
  final _descriptionFieldController = TextEditingController();

  final _profileService = ProfileService();

  double? _locationLat;
  double? _locationLng;
  String _locationAddress = '';

  String? error;

  @override
  void initState() {
    ProfileData profile = ref.read(profileProvider);

    _nicenameFieldController.text = profile.nicename;
    _emailFieldController.text = profile.email;
    _websiteFieldController.text = profile.website;
    _instagramFieldController.text = profile.instagram;
    _firstNameFieldController.text = profile.firstName;
    _lastNameFieldController.text = profile.lastName;
    _locationFieldController.text = profile.location;
    _cameraFieldController.text = profile.camera;
    _focalLengthFieldController.text = profile.focalLength;
    _descriptionFieldController.text = profile.description;

    super.initState();
  }

  @override
  void dispose() {
    _nicenameFieldController.dispose();
    _emailFieldController.dispose();
    _websiteFieldController.dispose();
    _instagramFieldController.dispose();
    _firstNameFieldController.dispose();
    _lastNameFieldController.dispose();
    _locationFieldController.dispose();
    _cameraFieldController.dispose();
    _focalLengthFieldController.dispose();
    _descriptionFieldController.dispose();

    _scrollController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: Form(
          key: _formKey,
          child: CustomScrollView(
            controller: _scrollController,
            slivers: [
              PmSliverAppBar(
                scrollController: _scrollController,
                titleText: "Edit profile",
                useLogo: false,
                automaticallyImplyLeading: true,
              ),
              SliverToBoxAdapter(
                child: Column(
                  children: [
                    const SizedBox(height: 13),
                    const AvatarPicker(),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 32.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _nicenameFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Username",
                        validator: FieldValidators.usernameValidator.call,
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _emailFieldController,
                        keyboardType: TextInputType.emailAddress,
                        labelText: "Email",
                        validator: FieldValidators.usernameValidator.call,
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _instagramFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Instagram profile",
                        hintText: "@portraitmode.io",
                        validator: FieldValidators.instagramValidator.call,
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _firstNameFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "First name",
                        validator: FieldValidators.firstNameValidator.call,
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _lastNameFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Last name",
                        validator: FieldValidators.firstNameValidator.call,
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _locationFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Location",
                        validator: FieldValidators.locationValidator.call,
                        onTap: () async {
                          _openLocationPicker();
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _websiteFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Website",
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _cameraFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Camera/s",
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _focalLengthFieldController,
                        keyboardType: TextInputType.text,
                        labelText: "Favorite focal length/s",
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 8.0,
                        bottom: 16.0,
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: PmTextField(
                        controller: _descriptionFieldController,
                        keyboardType: TextInputType.multiline,
                        minLines: 2,
                        maxLines: 7,
                        labelText: "Artist bio",
                        onChanged: (String value) {
                          if (!mounted) return;

                          setState(() {
                            error = null;
                          });
                        },
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: ScreenStyleConfig.horizontalPadding,
                        right: ScreenStyleConfig.horizontalPadding,
                      ),
                      child: SizedBox(
                        height: 50.0,
                        width: double.infinity,
                        child: SubmitButton(
                          buttonText: "Save",
                          onPressed: _onFormSubmit,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16.0),
                    error != null
                        ? Text(
                            "$error",
                            style: const TextStyle(color: Colors.red),
                          )
                        : const SizedBox.shrink(),
                    // const Padding(
                    //   padding: EdgeInsets.only(
                    //     top: 8.0,
                    //     bottom: 16.0,
                    //     left: ScreenStyleConfig.horizontalPadding,
                    //     right: ScreenStyleConfig.horizontalPadding,
                    //   ),
                    //   child: YoutubeVerificationButton(),
                    // ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _openLocationPicker() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) {
          return LocationPicker(onChanged: _handleLocationChanged);
        },
      ),
    );
  }

  void _handleLocationChanged(AutocompletePrediction prediction) async {
    _locationFieldController.text = prediction.description ?? _locationAddress;

    if (mounted) {
      setState(() {
        _locationAddress = prediction.description ?? _locationAddress;
      });
    }

    DetailsResponse? response = await _getDetailedResult(prediction);
    if (response == null) return;

    DetailsResult? result = response.result;
    if (result == null) return;
    if (result.formattedAddress == null) return;

    _locationFieldController.text =
        result.formattedAddress ?? _locationFieldController.text;

    Geometry? geometry = result.geometry;
    if (geometry == null) return;

    Location? location = geometry.location;
    if (location == null) return;
    if (location.lat == null || location.lng == null) return;

    if (mounted) {
      setState(() {
        _locationAddress = result.formattedAddress ?? _locationAddress;
        _locationLat = location.lat;
        _locationLng = location.lng;
      });
    }
  }

  Future<DetailsResponse?> _getDetailedResult(
    AutocompletePrediction prediction,
  ) async {
    if (prediction.placeId == null) return null;

    DetailsResponse? result = await _googlePlace.details.get(
      prediction.placeId ?? '',
    );

    return result;
  }

  Future<void> _onFormSubmit() async {
    if (!_formKey.currentState!.validate()) return;
    FocusScope.of(context).unfocus();

    ProfileHttpResponse response = await _profileService.edit(
      profileData: ProfileData(
        email: _emailFieldController.text,
        instagram: _instagramFieldController.text,
        firstName: _firstNameFieldController.text,
        lastName: _lastNameFieldController.text,
        location: _locationFieldController.text,
        website: _websiteFieldController.text,
        camera: _cameraFieldController.text,
        focalLength: _focalLengthFieldController.text,
        description: _descriptionFieldController.text,
      ),
      locationLat: _locationLat,
      locationLng: _locationLng,
    );

    if (!response.success) {
      if (authUtil.errorCodeRequiresLogin(response.errorCode)) {
        if (mounted) {
          showSessionEndedDialog(context, ref);
        }

        return;
      }

      if (mounted) {
        setState(() {
          error = response.message;
        });
      }

      return;
    }

    final data = response.data ?? const ProfileData();

    await LocalUserService.replace(
      LocalUserData(
        userId: data.id,
        nicename: data.nicename,
        role: data.role,
        displayName: data.displayName,
        profileUrl: data.profileUrl,
        avatarUrl: data.avatarUrl,
        membershipType: data.membershipType,
      ),
    );

    ref.read(profileProvider.notifier).replace(data);

    if (mounted) {
      Navigator.of(context).push(
        MaterialPageRoute(
          builder: (context) => ArtistDetailScreen(
            isOwnProfile: true,
            partialData: ArtistPartialData(
              id: data.id,
              nicename: data.nicename,
              displayName: data.displayName,
              profileUrl: data.profileUrl,
              avatarUrl: data.avatarUrl,
              membershipType: data.membershipType,
            ),
          ),
        ),
      );
    }
  }
}

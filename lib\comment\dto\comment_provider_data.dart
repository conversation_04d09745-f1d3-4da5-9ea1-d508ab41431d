import 'dart:convert';

import 'package:flutter/foundation.dart';
import 'package:portraitmode/comment/enum.dart';

@immutable
final class CommentProviderData {
  final int id;
  final bool? isLiked;
  final int? totalLikes;
  final String content;
  final CommentSubmissionStatus submissionStatus;

  const CommentProviderData({
    this.id = 0,
    this.isLiked,
    this.totalLikes,
    this.content = '',
    this.submissionStatus = CommentSubmissionStatus.submitted,
  });

  CommentProviderData copyWith({
    int? id,
    bool? isLiked,
    int? totalLikes,
    String? content,
    CommentSubmissionStatus? submissionStatus,
  }) {
    return CommentProviderData(
      id: id ?? this.id,
      isLiked: isLiked ?? this.isLiked,
      totalLikes: totalLikes ?? this.totalLikes,
      content: content ?? this.content,
      submissionStatus: submissionStatus ?? this.submissionStatus,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'isLiked': isLiked,
      'totalLikes': totalLikes,
      'content': content,
    };
  }

  factory CommentProviderData.fromMap(Map<String, dynamic> data) {
    return CommentProviderData(
      id: data['id'],
      isLiked: data['isLiked'] ?? false,
      totalLikes: data['totalLikes'] ?? 0,
      content: data['content'] ?? '',
    );
  }

  factory CommentProviderData.fromJson(String source) =>
      CommentProviderData.fromMap(json.decode(source));

  @override
  int get hashCode =>
      Object.hash(id, isLiked, totalLikes, content, submissionStatus);

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    if (other is! CommentProviderData) return false;

    return other.id == id &&
        other.isLiked == isLiked &&
        other.totalLikes == totalLikes &&
        other.content == content &&
        other.submissionStatus == submissionStatus;
  }

  @override
  String toString() {
    return '''
CommentProviderData(
  id: $id,
  isLiked: $isLiked,
  totalLikes: $totalLikes,
  content: "$content",
  submissionStatus: $submissionStatus,
)
''';
  }
}

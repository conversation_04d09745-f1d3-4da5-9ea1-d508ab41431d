import 'package:portraitmode/comment/dto/comment_data.dart';
import 'package:portraitmode/photo/dto/photo_provider_data.dart';

class RefreshCommentListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<CommentData> data;
  final PhotoProviderData? photoData;

  RefreshCommentListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
    this.photoData,
  });

  factory RefreshCommentListResponse.fromMap(Map<String, dynamic> map) {
    return RefreshCommentListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<CommentData>((data) => CommentData.fromMap(data))
                .toList()
          : [],
      photoData: map['photoData'] != null && map['photoData'] is Map
          ? PhotoProviderData.fromMap(map['photoData'])
          : null,
    );
  }
}

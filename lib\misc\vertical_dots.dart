import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class VerticalDots extends StatelessWidget {
  final double width;
  final double? height;
  final double dotSize;
  final double dotGap;
  final Color? dotColor;
  final EdgeInsetsGeometry padding;
  final VoidCallback? onTap;

  const VerticalDots({
    super.key,
    this.width = 20.0,
    this.height,
    this.dotSize = 2.5,
    this.dotGap = 3.0,
    this.dotColor,
    this.padding = const EdgeInsets.symmetric(vertical: 7.0),
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: width,
        height: height,
        padding: padding,
        color: Colors.transparent,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: <Widget>[
            _buildDot(context),
            SizedBox(height: dotGap),
            _buildDot(context),
            SizedBox(height: dotGap),
            _buildDot(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDot(BuildContext context) {
    return Container(
      width: dotSize,
      height: dotSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        color: dotColor ?? context.colors.brandColor,
      ),
    );
  }
}

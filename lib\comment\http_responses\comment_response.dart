import 'package:portraitmode/comment/dto/comment_data.dart';

class CommentResponse {
  final bool success;
  final String errorCode;
  final String message;
  final CommentData? data;

  CommentResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data,
  });

  factory CommentResponse.fromMap(Map<String, dynamic> map) {
    return CommentResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is Map
          ? CommentData.fromMap(map['data'])
          : null,
    );
  }
}

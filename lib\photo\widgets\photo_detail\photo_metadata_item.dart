import 'package:flutter/material.dart';
import 'package:portraitmode/app/config/colors.dart';

class PhotoMetadataItem extends StatelessWidget {
  const PhotoMetadataItem({
    super.key,
    required this.label,
    this.value = '',
    this.child,
  });

  final String label;
  final String value;
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return Flexible(
      flex: 1,
      child: SizedBox(
        width: double.infinity,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 13.0,
                color: context.colors.primarySwatch[300],
              ),
            ),
            const SizedBox(height: 3.0),
            if (child != null) child!,
            if (value.isNotEmpty)
              Text(value, style: const TextStyle(fontSize: 13.0)),
          ],
        ),
      ),
    );
  }
}

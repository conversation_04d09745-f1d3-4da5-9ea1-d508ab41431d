import 'package:portraitmode/camera/dto/camera_data.dart';

class CameraListResponse {
  final bool success;
  final String errorCode;
  final String message;
  final List<CameraData> data;

  CameraListResponse({
    this.success = true,
    this.errorCode = '',
    this.message = '',
    this.data = const [],
  });

  factory CameraListResponse.fromMap(Map<String, dynamic> map) {
    return CameraListResponse(
      success: map['success'] ?? false,
      errorCode: map['code'] ?? '',
      message: map['message'] ?? '',
      data: map['data'] != null && map['data'] is List && map['data'].isNotEmpty
          ? map['data']
                .map<CameraData>((data) => CameraData.fromMap(data))
                .toList()
          : [],
    );
  }
}

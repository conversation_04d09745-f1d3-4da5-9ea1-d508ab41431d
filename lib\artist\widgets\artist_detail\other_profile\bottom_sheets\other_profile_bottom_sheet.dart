import 'package:flutter/material.dart';
import 'package:ionicons/ionicons.dart';
import 'package:portraitmode/app/config/colors.dart';
import 'package:portraitmode/app/config/config.dart';
import 'package:portraitmode/artist/dto/artist_data.dart';
import 'package:portraitmode/artist/widgets/artist_detail/other_profile/bottom_sheets/report_artist_bottom_sheet.dart';
import 'package:portraitmode/modals/modal_list_tile.dart';

class OtherProfileBottomSheet extends StatefulWidget {
  const OtherProfileBottomSheet({
    super.key,
    required this.artist,
    this.showBlockUnblockMenu = true,
    this.onBlockArtist,
    this.onUnblockArtist,
  });

  final ArtistData artist;
  final bool showBlockUnblockMenu;
  final Function? onBlockArtist;
  final Function? onUnblockArtist;

  @override
  OtherProfileBottomSheetState createState() => OtherProfileBottomSheetState();
}

class OtherProfileBottomSheetState extends State<OtherProfileBottomSheet> {
  bool _doingBlockUnblock = false;

  @override
  Widget build(BuildContext context) {
    List<Widget> menuItems = [];

    if (widget.showBlockUnblockMenu) {
      menuItems.add(
        ModalListTile(
          title: widget.artist.isBlocked
              ? "Unblock this artist"
              : "Block this artist",
          iconData: Icons.block,
          isLoading: _doingBlockUnblock,
          onTap: () {
            final String actionType = widget.artist.isBlocked
                ? 'unblock'
                : 'block';

            _showBlockUnblockDialog(actionType);
          },
        ),
      );
    }

    menuItems.add(
      ModalListTile(
        title: "Report this artist",
        textColor: context.colors.dangerColor,
        iconColor: context.colors.dangerColor,
        iconData: Ionicons.notifications_outline,
        onTap: () {
          Navigator.pop(context);
          _showReportArtistModal();
        },
      ),
    );

    return Container(
      padding: EdgeInsets.only(
        top: 9.0,
        bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      decoration: BoxDecoration(
        color: context.colors.scaffoldColor,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(20.0),
          topRight: Radius.circular(20.0),
        ),
      ),
      child: SafeArea(
        child: SizedBox(
          height:
              BottomSheetConfig.extraSpace +
              (BottomSheetConfig.menuItemHeight * menuItems.length),
          child: Column(children: menuItems),
        ),
      ),
    );
  }

  void _showReportArtistModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (BuildContext context) {
        return DraggableScrollableSheet(
          maxChildSize: 0.9,
          initialChildSize: 0.45,
          expand: false,
          builder: ((context, scrollController) {
            return Container(
              padding: EdgeInsets.only(
                top: 8.0,
                bottom: MediaQuery.viewInsetsOf(context).bottom + 15.0,
              ),
              decoration: BoxDecoration(
                color: context.colors.scaffoldColor,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(20.0),
                  topRight: Radius.circular(20.0),
                ),
              ),
              child: ReportArtistBottomSheet(
                scrollController: scrollController,
                artistId: widget.artist.id,
              ),
            );
          }),
        );
      },
    );
  }

  void _showBlockUnblockDialog(String actionType) {
    final String dialogText = actionType == 'unblock'
        ? "Are you sure you want to un-block this artist?"
        : "Are you sure you want to block this artist? Blocked users won't be able to follow you or view your photos.";

    showDialog(
      context: context,
      builder: (BuildContext context) => AlertDialog(
        title: Text(
          actionType == 'unblock'
              ? 'Ublock @${widget.artist.nicename}'
              : 'Block @${widget.artist.nicename}',
        ),
        content: Text(dialogText),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.pop(context);
            },
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: _handleBlockUnblock,
            child: Text(
              actionType == 'unblock' ? 'Unblock' : 'Block',
              style: TextStyle(color: context.colors.dangerColor),
            ),
          ),
        ],
      ),
    );
  }

  void _handleBlockUnblock() async {
    if (mounted) {
      Navigator.of(context).pop();
    }

    setState(() {
      _doingBlockUnblock = true;
    });

    final bool hasCallback =
        widget.onBlockArtist != null && widget.onUnblockArtist != null;

    if (widget.artist.isBlocked) {
      if (widget.onUnblockArtist != null) {
        await widget.onUnblockArtist!();
      }
    } else {
      if (widget.onBlockArtist != null) {
        await widget.onBlockArtist!();
      }
    }

    if (hasCallback && mounted) {
      setState(() {
        _doingBlockUnblock = false;
      });
    }

    if (mounted) {
      Navigator.of(context).pop();
    }
  }
}

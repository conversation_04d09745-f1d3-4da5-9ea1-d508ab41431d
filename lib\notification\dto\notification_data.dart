class NotificationData {
  final int id;
  final String time;
  final String readableTime;

  /// This property is not marked as `final`
  /// because it has a direct update operation in `notificationProvider`.
  int isRead;

  final String type;
  final String url;
  final String thumbnailUrl;
  final int postId;
  final String postType;
  final int commentId;
  final int commentParentId;
  final int senderId;
  final String senderNicename;
  final String senderName;
  final String senderDisplayName;
  final String senderAvatarUrl;
  final int recipientId;
  final String message;

  NotificationData({
    this.id = 0,
    this.time = '',
    this.readableTime = '',
    this.isRead = 0,
    this.type = '',
    this.url = '',
    this.thumbnailUrl = '',
    this.postId = 0,
    this.postType = '',
    this.commentId = 0,
    this.commentParentId = 0,
    this.senderId = 0,
    this.senderNicename = '',
    this.senderName = '',
    this.senderDisplayName = '',
    this.senderAvatarUrl = '',
    this.recipientId = 0,
    this.message = '',
  });

  NotificationData copyWith({
    int? id,
    String? time,
    String? readableTime,
    int? isRead,
    String? type,
    String? url,
    String? thumbnailUrl,
    int? postId,
    String? postType,
    int? commentId,
    int? commentParentId,
    int? senderId,
    String? senderNicename,
    String? senderName,
    String? senderDisplayName,
    String? senderAvatarUrl,
    int? recipientId,
    String? message,
  }) {
    return NotificationData(
      id: id ?? this.id,
      time: time ?? this.time,
      readableTime: readableTime ?? this.readableTime,
      isRead: isRead ?? this.isRead,
      type: type ?? this.type,
      url: url ?? this.url,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      postId: postId ?? this.postId,
      postType: postType ?? this.postType,
      commentId: commentId ?? this.commentId,
      commentParentId: commentParentId ?? this.commentParentId,
      senderId: senderId ?? this.senderId,
      senderNicename: senderNicename ?? this.senderNicename,
      senderName: senderName ?? this.senderName,
      senderDisplayName: senderDisplayName ?? this.senderDisplayName,
      senderAvatarUrl: senderAvatarUrl ?? this.senderAvatarUrl,
      recipientId: recipientId ?? this.recipientId,
      message: message ?? this.message,
    );
  }

  factory NotificationData.fromMap(Map<String, dynamic> map) {
    return NotificationData(
      id: map['id'] ?? 0,
      time: map['time'] ?? 0,
      readableTime: map['readableTime'] ?? '',
      isRead: map['isRead'] ?? 0,
      type: map['type'] ?? '',
      url: map['url'] ?? '',
      thumbnailUrl: map['thumbnailUrl'] ?? '',
      postId: map['postId'] ?? 0,
      postType: map['postType'] ?? '',
      commentId: map['commentId'] ?? 0,
      commentParentId: map['commentParentId'] ?? 0,
      senderId: map['senderId'] ?? 0,
      senderNicename: map['senderNicename'] != null
          ? map['senderNicename'] is int
                ? map['senderNicename'].toString()
                : map['senderNicename']
          : '',
      senderName: map['senderName'] ?? '',
      senderDisplayName: map['senderDisplayName'] ?? '',
      senderAvatarUrl: map['senderAvatarUrl'] ?? '',
      recipientId: map['recipientId'] ?? 0,
      message: map['message'] ?? '',
    );
  }
}

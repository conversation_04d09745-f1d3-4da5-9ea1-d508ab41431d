import 'package:portraitmode/system_status/dto/system_status_history_data.dart';

class SystemStatusData {
  final String currentStatus;
  final SystemStatusHistoryData? currentIssue;
  final List<SystemStatusHistoryData> histories;

  SystemStatusData({
    this.currentStatus = 'normal',
    this.currentIssue,
    this.histories = const [],
  });

  factory SystemStatusData.fromMap(Map<String, dynamic> map) {
    return SystemStatusData(
      currentStatus: map['currentStatus'] ?? 'normal',
      currentIssue: map['currentIssue'] != null
          ? SystemStatusHistoryData.fromMap(map['currentIssue'])
          : null,
      histories: map['histories'] != null
          ? List<SystemStatusHistoryData>.from(
              map['histories'].map((x) => SystemStatusHistoryData.fromMap(x)),
            )
          : [],
    );
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:portraitmode/search/dto/search_data.dart';

final class SearchCamerasDataNotifier extends Notifier<CamerasSearchData> {
  @override
  CamerasSearchData build() => const CamerasSearchData();

  void setLastId(int? lastId) {
    if (state.lastId == lastId) return;
    state = state.copyWith(lastId: lastId);
  }

  void setLoadMoreEndReached(bool loadMoreEndReached) {
    if (state.loadMoreEndReached == loadMoreEndReached) return;
    state = state.copyWith(loadMoreEndReached: loadMoreEndReached);
  }

  void replace(CamerasSearchData data) {
    state = data;
  }

  void reset() {
    state = const CamerasSearchData();
  }
}

final searchCamerasDataProvider =
    NotifierProvider.autoDispose<SearchCamerasDataNotifier, CamerasSearchData>(
      SearchCamerasDataNotifier.new,
    );
